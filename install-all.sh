#!/bin/bash

# Terminal Dashboard - Install All Dependencies Script
# Menginstall semua dependencies untuk semua program bot

echo "📦 Installing all dependencies for Terminal Dashboard and Bot Programs..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to install dependencies for a directory
install_deps() {
    local dir="$1"
    local name="$2"
    
    if [ -f "${dir}/package.json" ]; then
        print_status "Installing dependencies for: $name ($dir)"
        (
            cd "$dir"
            if [ -f "package-lock.json" ]; then
                npm ci --silent 2>/dev/null || npm install --silent 2>/dev/null
            else
                npm install --silent 2>/dev/null
            fi
        )
        if [ $? -eq 0 ]; then
            print_success "✅ $name - Dependencies installed"
        else
            print_error "❌ $name - Failed to install dependencies"
        fi
    else
        print_warning "⚠️  $name - No package.json found"
    fi
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "Node.js version: $(node --version)"
print_status "npm version: $(npm --version)"

echo ""
print_status "🏗️  Installing Terminal Dashboard dependencies..."

# Install Terminal Dashboard Backend
install_deps "terminal-dashboard/backend" "Terminal Dashboard Backend"

# Install Terminal Dashboard Frontend
install_deps "terminal-dashboard/frontend" "Terminal Dashboard Frontend"

echo ""
print_status "🤖 Installing Bot Program dependencies..."

# Install all bot programs
install_deps "0g" "0G Network Bot"
install_deps "Pharos" "Pharos Protocol Bot"
install_deps "Maitrix" "Maitrix Auto Bot"
install_deps "T1" "T1 Auto Bridge"
install_deps "Rise" "Rise Protocol Bot"
install_deps "INFTS" "INFTS Auto Bot"
install_deps "Union" "Union Auto Bot"
install_deps "Huddle" "Huddle Deploy Bot"
install_deps "TradeGPT" "TradeGPT Auto Bot"
install_deps "Enso" "Enso Auto Bot"
install_deps "kite" "Kite AI Auto Bot"
install_deps "merak" "Merak Bot"
install_deps "Shift" "CoinShift Auto Bot"
install_deps "Byte" "Bytenova Auto Bot"
install_deps "Euclid-Bot" "Euclid Bot"
install_deps "Inco" "INCO Auto Bot"
install_deps "AsterAutoBot-NTE" "Aster Auto Bot"
install_deps "R2nte" "R2 Testnet Auto Bot"
install_deps "SomniaExchangeBot-NTE" "Somnia Exchange Bot"
install_deps "rome" "Rome EVM Deployer"

echo ""
print_status "🔧 Building Terminal Dashboard Frontend..."

if [ -d "terminal-dashboard/frontend" ]; then
    (
        cd terminal-dashboard/frontend
        print_status "Building production frontend..."
        npm run build --silent 2>/dev/null
    )
    if [ $? -eq 0 ]; then
        print_success "✅ Frontend built successfully"
    else
        print_warning "⚠️  Frontend build failed, but system can still run in dev mode"
    fi
fi

echo ""
print_status "🔑 Setting up permissions..."

# Make scripts executable
chmod +x terminal-dashboard/start.sh 2>/dev/null
chmod +x terminal-dashboard/stop.sh 2>/dev/null
chmod +x cleanup.sh 2>/dev/null

echo ""
print_status "📋 Generating installation report..."

# Create installation report
cat > installation_report.txt << EOF
Terminal Dashboard Installation Report
Generated: $(date)

=== SYSTEM REQUIREMENTS ===
Node.js: $(node --version)
npm: $(npm --version)

=== TERMINAL DASHBOARD ===
Backend: $([ -d "terminal-dashboard/backend/node_modules" ] && echo "✅ Installed" || echo "❌ Failed")
Frontend: $([ -d "terminal-dashboard/frontend/node_modules" ] && echo "✅ Installed" || echo "❌ Failed")
Frontend Build: $([ -d "terminal-dashboard/frontend/dist" ] && echo "✅ Built" || echo "❌ Not Built")

=== BOT PROGRAMS STATUS ===
EOF

# Check installation status for each program
programs=(
    "0g:0G Network Bot"
    "Pharos:Pharos Protocol Bot"
    "Maitrix:Maitrix Auto Bot"
    "T1:T1 Auto Bridge"
    "Rise:Rise Protocol Bot"
    "INFTS:INFTS Auto Bot"
    "Union:Union Auto Bot"
    "Huddle:Huddle Deploy Bot"
    "TradeGPT:TradeGPT Auto Bot"
    "Enso:Enso Auto Bot"
    "kite:Kite AI Auto Bot"
    "merak:Merak Bot"
    "Shift:CoinShift Auto Bot"
    "Byte:Bytenova Auto Bot"
    "Euclid-Bot:Euclid Bot"
    "Inco:INCO Auto Bot"
    "AsterAutoBot-NTE:Aster Auto Bot"
    "R2nte:R2 Testnet Auto Bot"
    "SomniaExchangeBot-NTE:Somnia Exchange Bot"
    "rome:Rome EVM Deployer"
)

for program in "${programs[@]}"; do
    IFS=':' read -r dir name <<< "$program"
    if [ -d "${dir}/node_modules" ]; then
        echo "✅ $name ($dir)" >> installation_report.txt
    else
        echo "❌ $name ($dir)" >> installation_report.txt
    fi
done

cat >> installation_report.txt << EOF

=== NEXT STEPS ===
1. Run cleanup: ./cleanup.sh
2. Start Terminal Dashboard: cd terminal-dashboard && ./start.sh
3. Access dashboard: http://localhost:3001
4. Select and run any bot program from the web interface

=== TROUBLESHOOTING ===
- If a program fails to start, check its individual README.md
- Make sure to configure wallet files and environment variables
- Check logs in terminal-dashboard/backend.log for issues
EOF

print_success "Installation report generated: installation_report.txt"

echo ""
print_success "🎉 All dependencies installed successfully!"
print_status "📊 Check installation_report.txt for details"

echo ""
echo "🚀 Ready to launch Terminal Dashboard!"
echo ""
echo "Next steps:"
echo "1. ./cleanup.sh                    # Clean up system"
echo "2. cd terminal-dashboard           # Enter dashboard directory"
echo "3. ./start.sh                      # Start the system"
echo "4. Open http://localhost:3001      # Access web dashboard"
echo ""
echo "🎮 You can then run any of the $(echo "${programs[@]}" | wc -w) bot programs from the web interface!"
