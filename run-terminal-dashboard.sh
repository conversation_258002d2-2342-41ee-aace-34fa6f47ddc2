#!/bin/bash

# Terminal Dashboard - Main Launcher Script
# Script utama untuk menjalankan Terminal Dashboard dengan semua bot programs

echo "🚀 Terminal Dashboard - Multi-Program Console Manager"
echo "======================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

print_highlight() {
    echo -e "${PURPLE}$1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check system requirements
check_requirements() {
    print_header "🔍 Checking system requirements..."
    
    local all_good=true
    
    if ! command_exists node; then
        print_error "Node.js is not installed"
        all_good=false
    else
        print_success "Node.js: $(node --version)"
    fi
    
    if ! command_exists npm; then
        print_error "npm is not installed"
        all_good=false
    else
        print_success "npm: $(npm --version)"
    fi
    
    if [ ! -d "terminal-dashboard" ]; then
        print_error "Terminal Dashboard directory not found"
        all_good=false
    else
        print_success "Terminal Dashboard directory found"
    fi
    
    if [ "$all_good" = false ]; then
        print_error "System requirements not met. Please install missing components."
        exit 1
    fi
}

# Function to show available programs
show_programs() {
    print_header "🤖 Available Bot Programs:"
    echo ""
    
    local programs=(
        "🌐 0G Network Bot (0g)"
        "⚡ Pharos Protocol Bot (Pharos)"
        "🤖 Maitrix Auto Bot (Maitrix)"
        "🌉 T1 Auto Bridge (T1)"
        "📈 Rise Protocol Bot (Rise)"
        "🎨 INFTS Auto Bot (INFTS)"
        "🔗 Union Auto Bot (Union)"
        "🚀 Huddle Deploy Bot (Huddle)"
        "💹 TradeGPT Auto Bot (TradeGPT)"
        "🎯 Enso Auto Bot (Enso)"
        "🪁 Kite AI Auto Bot (kite)"
        "🌟 Merak Bot (merak)"
        "💰 CoinShift Auto Bot (Shift)"
        "🔥 Bytenova Auto Bot (Byte)"
        "🔄 Euclid Bot (Euclid-Bot)"
        "🔐 INCO Auto Bot (Inco)"
        "⭐ Aster Auto Bot (AsterAutoBot-NTE)"
        "🎮 R2 Testnet Auto Bot (R2nte)"
        "💫 Somnia Exchange Bot (SomniaExchangeBot-NTE)"
        "🏛️ Rome EVM Deployer (rome)"
    )
    
    for program in "${programs[@]}"; do
        echo "  $program"
    done
    echo ""
}

# Function to show menu
show_menu() {
    echo ""
    print_header "📋 Choose an action:"
    echo ""
    echo "1. 🔧 Install all dependencies"
    echo "2. 🧹 Clean up system"
    echo "3. 🚀 Start Terminal Dashboard"
    echo "4. 🛑 Stop Terminal Dashboard"
    echo "5. 📊 Show system status"
    echo "6. 🤖 Show available programs"
    echo "7. 📖 Show help"
    echo "8. 🚪 Exit"
    echo ""
}

# Function to install dependencies
install_all() {
    print_header "📦 Installing all dependencies..."
    if [ -f "install-all.sh" ]; then
        chmod +x install-all.sh
        ./install-all.sh
    else
        print_error "install-all.sh not found"
    fi
}

# Function to cleanup system
cleanup_system() {
    print_header "🧹 Cleaning up system..."
    if [ -f "cleanup.sh" ]; then
        chmod +x cleanup.sh
        ./cleanup.sh
    else
        print_error "cleanup.sh not found"
    fi
}

# Function to start terminal dashboard
start_dashboard() {
    print_header "🚀 Starting Terminal Dashboard..."
    if [ -d "terminal-dashboard" ]; then
        cd terminal-dashboard
        if [ -f "start.sh" ]; then
            chmod +x start.sh
            ./start.sh
        else
            print_error "start.sh not found in terminal-dashboard directory"
        fi
    else
        print_error "terminal-dashboard directory not found"
    fi
}

# Function to stop terminal dashboard
stop_dashboard() {
    print_header "🛑 Stopping Terminal Dashboard..."
    if [ -d "terminal-dashboard" ]; then
        cd terminal-dashboard
        if [ -f "stop.sh" ]; then
            chmod +x stop.sh
            ./stop.sh
        else
            print_error "stop.sh not found in terminal-dashboard directory"
        fi
    else
        print_error "terminal-dashboard directory not found"
    fi
}

# Function to show system status
show_status() {
    print_header "📊 System Status:"
    echo ""
    
    # Check if terminal dashboard is running
    if pgrep -f "terminal-dashboard" > /dev/null; then
        print_success "Terminal Dashboard: Running"
    else
        print_warning "Terminal Dashboard: Stopped"
    fi
    
    # Check backend
    if [ -f "terminal-dashboard/backend/server.js" ]; then
        print_success "Backend: Ready"
    else
        print_error "Backend: Missing"
    fi
    
    # Check frontend
    if [ -f "terminal-dashboard/frontend/src/App.jsx" ]; then
        print_success "Frontend: Ready"
    else
        print_error "Frontend: Missing"
    fi
    
    # Check if port 3001 is in use
    if netstat -tuln 2>/dev/null | grep -q ":3001 "; then
        print_success "Port 3001: In use (Dashboard likely running)"
        print_highlight "🌐 Access dashboard at: http://localhost:3001"
    else
        print_warning "Port 3001: Available"
    fi
    
    echo ""
    print_status "Available bot programs: 20"
    print_status "System ready for use"
}

# Function to show help
show_help() {
    print_header "📖 Terminal Dashboard Help"
    echo ""
    echo "This is a web-based terminal dashboard that allows you to run multiple"
    echo "bot programs simultaneously through a modern web interface."
    echo ""
    echo "Features:"
    echo "• 🌐 Web-based terminal interface"
    echo "• 🔄 Real-time terminal output"
    echo "• 📱 Multiple concurrent programs"
    echo "• 🎮 Interactive program management"
    echo "• 📊 Resource monitoring"
    echo ""
    echo "Usage:"
    echo "1. Install dependencies (option 1)"
    echo "2. Clean up system (option 2)"
    echo "3. Start dashboard (option 3)"
    echo "4. Open http://localhost:3001 in your browser"
    echo "5. Click on any program to start it"
    echo ""
    echo "Each program runs in its own terminal session and can be"
    echo "controlled independently through the web interface."
    echo ""
}

# Main script
main() {
    check_requirements
    show_programs
    
    while true; do
        show_menu
        read -p "Enter your choice (1-8): " choice
        
        case $choice in
            1)
                install_all
                ;;
            2)
                cleanup_system
                ;;
            3)
                start_dashboard
                break
                ;;
            4)
                stop_dashboard
                ;;
            5)
                show_status
                ;;
            6)
                show_programs
                ;;
            7)
                show_help
                ;;
            8)
                print_success "👋 Goodbye!"
                exit 0
                ;;
            *)
                print_error "Invalid choice. Please enter 1-8."
                ;;
        esac
        
        echo ""
        read -p "Press Enter to continue..."
    done
}

# Run main function
main
