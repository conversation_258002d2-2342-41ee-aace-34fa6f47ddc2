#!/bin/bash

# Terminal Dashboard Cleanup Script
# Membersihkan file-file yang tidak diperlukan dan mengoptimalkan sistem

echo "🧹 Starting Terminal Dashboard Cleanup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to safely remove directory
safe_remove() {
    local dir="$1"
    if [ -d "$dir" ]; then
        print_status "Removing directory: $dir"
        rm -rf "$dir"
        print_success "Removed: $dir"
    else
        print_warning "Directory not found: $dir"
    fi
}

# Function to safely remove file
safe_remove_file() {
    local file="$1"
    if [ -f "$file" ]; then
        print_status "Removing file: $file"
        rm -f "$file"
        print_success "Removed: $file"
    else
        print_warning "File not found: $file"
    fi
}

echo ""
print_status "🗂️  Cleaning up duplicate directories..."

# Remove duplicate directories (keeping the main ones)
safe_remove "Pharos-asli"  # Keep Pharos
safe_remove "R2"           # Keep R2nte

echo ""
print_status "🗑️  Cleaning up temporary and cache files..."

# Clean up log files
find . -name "*.log" -type f -not -path "./terminal-dashboard/*" -delete 2>/dev/null
find . -name "*.log.*" -type f -not -path "./terminal-dashboard/*" -delete 2>/dev/null

# Clean up backup files
find . -name "*~" -type f -delete 2>/dev/null
find . -name "*.bak" -type f -delete 2>/dev/null
find . -name "*.tmp" -type f -delete 2>/dev/null

# Clean up OS specific files
find . -name ".DS_Store" -type f -delete 2>/dev/null
find . -name "Thumbs.db" -type f -delete 2>/dev/null
find . -name "desktop.ini" -type f -delete 2>/dev/null

echo ""
print_status "📦 Cleaning up node_modules caches..."

# Clean npm cache in each project
for dir in */; do
    if [ -f "${dir}package.json" ] && [ -d "${dir}node_modules" ]; then
        print_status "Cleaning cache for: $dir"
        (cd "$dir" && npm cache clean --force 2>/dev/null || true)
    fi
done

echo ""
print_status "🔧 Optimizing package.json files..."

# Check for missing dependencies
for dir in */; do
    if [ -f "${dir}package.json" ]; then
        print_status "Checking dependencies for: $dir"
        (cd "$dir" && npm audit fix --force 2>/dev/null || true)
    fi
done

echo ""
print_status "📋 Generating system report..."

# Create cleanup report
cat > cleanup_report.txt << EOF
Terminal Dashboard Cleanup Report
Generated: $(date)

=== ACTIVE PROGRAMS ===
EOF

# List all directories with package.json
for dir in */; do
    if [ -f "${dir}package.json" ]; then
        name=$(grep -o '"name"[[:space:]]*:[[:space:]]*"[^"]*"' "${dir}package.json" | cut -d'"' -f4)
        echo "✅ $dir - $name" >> cleanup_report.txt
    fi
done

cat >> cleanup_report.txt << EOF

=== TERMINAL DASHBOARD STATUS ===
Backend: $([ -f "terminal-dashboard/backend/server.js" ] && echo "✅ Ready" || echo "❌ Missing")
Frontend: $([ -f "terminal-dashboard/frontend/src/App.jsx" ] && echo "✅ Ready" || echo "❌ Missing")
Start Script: $([ -f "terminal-dashboard/start.sh" ] && echo "✅ Ready" || echo "❌ Missing")
Stop Script: $([ -f "terminal-dashboard/stop.sh" ] && echo "✅ Ready" || echo "❌ Missing")

=== CLEANUP SUMMARY ===
- Removed duplicate directories
- Cleaned temporary files
- Optimized npm caches
- Fixed package dependencies
- System ready for use

=== NEXT STEPS ===
1. Run: cd terminal-dashboard && chmod +x start.sh stop.sh
2. Start system: ./start.sh
3. Access dashboard: http://localhost:3001
EOF

print_success "Cleanup report generated: cleanup_report.txt"

echo ""
print_success "🎉 Cleanup completed successfully!"
print_status "📊 Check cleanup_report.txt for details"
print_status "🚀 Ready to start Terminal Dashboard!"

echo ""
echo "Next steps:"
echo "1. cd terminal-dashboard"
echo "2. chmod +x start.sh stop.sh"
echo "3. ./start.sh"
echo "4. Open http://localhost:3001"
