// Fix Terminal Scroll - Inject this script to fix scrolling issues

(function() {
    console.log('🔧 Fixing terminal scroll issues...');
    
    // Wait for xterm to be loaded
    function waitForXterm() {
        if (typeof window.Terminal !== 'undefined') {
            fixTerminalScroll();
        } else {
            setTimeout(waitForXterm, 100);
        }
    }
    
    function fixTerminalScroll() {
        // Override xterm configuration
        const originalTerminal = window.Terminal;
        
        window.Terminal = function(options = {}) {
            // Enhanced options for better scrolling
            const enhancedOptions = {
                ...options,
                scrollback: 50000,
                scrollOnUserInput: true,
                fastScrollModifier: 'alt',
                fastScrollSensitivity: 5,
                scrollSensitivity: 1,
                rows: 30,
                cols: 120,
                fontSize: 13,
                lineHeight: 1.2,
                convertEol: true,
                allowTransparency: false
            };
            
            const terminal = new originalTerminal(enhancedOptions);
            
            // Override open method to fix container
            const originalOpen = terminal.open;
            terminal.open = function(container) {
                const result = originalOpen.call(this, container);
                
                // Fix container styles
                setTimeout(() => {
                    const viewport = container.querySelector('.xterm-viewport');
                    if (viewport) {
                        viewport.style.overflowY = 'auto';
                        viewport.style.scrollbarWidth = 'thin';
                        viewport.style.scrollbarColor = '#30363d #161b22';
                    }
                    
                    const screen = container.querySelector('.xterm-screen');
                    if (screen) {
                        screen.style.height = 'auto';
                        screen.style.minHeight = '100%';
                    }
                    
                    // Force fit
                    if (terminal._fitAddon) {
                        terminal._fitAddon.fit();
                    }
                }, 100);
                
                return result;
            };
            
            return terminal;
        };
        
        // Copy static properties
        Object.setPrototypeOf(window.Terminal, originalTerminal);
        Object.assign(window.Terminal, originalTerminal);
        
        console.log('✅ Terminal scroll fix applied');
    }
    
    // Apply CSS fixes
    function applyCSSFixes() {
        const style = document.createElement('style');
        style.textContent = `
            /* Enhanced terminal scrolling */
            .terminal-content {
                height: calc(100vh - 200px) !important;
                min-height: 500px !important;
                max-height: calc(100vh - 150px) !important;
                overflow: hidden !important;
                position: relative !important;
            }
            
            .terminal-content .xterm {
                height: 100% !important;
                width: 100% !important;
            }
            
            .terminal-content .xterm .xterm-viewport {
                overflow-y: auto !important;
                scrollbar-width: thin !important;
                scrollbar-color: #30363d #161b22 !important;
                height: 100% !important;
            }
            
            .terminal-content .xterm .xterm-screen {
                height: auto !important;
                min-height: 100% !important;
            }
            
            /* Better scrollbar */
            .xterm-viewport::-webkit-scrollbar {
                width: 12px !important;
            }
            
            .xterm-viewport::-webkit-scrollbar-track {
                background: #161b22 !important;
                border-radius: 6px !important;
            }
            
            .xterm-viewport::-webkit-scrollbar-thumb {
                background: #30363d !important;
                border-radius: 6px !important;
                border: 2px solid #161b22 !important;
            }
            
            .xterm-viewport::-webkit-scrollbar-thumb:hover {
                background: #484f58 !important;
            }
            
            /* Fix terminal text rendering */
            .xterm-rows {
                line-height: 1.2 !important;
            }
            
            /* Ensure proper sizing */
            .xterm-helper-textarea {
                position: absolute !important;
                opacity: 0 !important;
                left: -9999px !important;
            }
        `;
        document.head.appendChild(style);
        console.log('✅ CSS fixes applied');
    }
    
    // Monitor for new terminals and fix them
    function monitorTerminals() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1 && node.querySelector && node.querySelector('.xterm')) {
                        setTimeout(() => {
                            const viewport = node.querySelector('.xterm-viewport');
                            if (viewport) {
                                viewport.style.overflowY = 'auto';
                                console.log('🔧 Fixed new terminal viewport');
                            }
                        }, 100);
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('👀 Terminal monitor started');
    }
    
    // Initialize fixes
    applyCSSFixes();
    waitForXterm();
    monitorTerminals();
    
    // Add keyboard shortcuts for scrolling
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.key === 'Home') {
            // Scroll to top
            const viewport = document.querySelector('.xterm-viewport');
            if (viewport) {
                viewport.scrollTop = 0;
            }
        } else if (e.ctrlKey && e.key === 'End') {
            // Scroll to bottom
            const viewport = document.querySelector('.xterm-viewport');
            if (viewport) {
                viewport.scrollTop = viewport.scrollHeight;
            }
        }
    });
    
    console.log('🚀 Terminal scroll fix script loaded');
})();
