#!/bin/bash

echo "🧪 Quick Terminal Dashboard Test"

# Kill existing processes
pkill -f "node.*server.js" 2>/dev/null || true
sleep 1

# Start server in background
echo "🚀 Starting server..."
cd terminal-dashboard/backend
nohup node server.js > server.log 2>&1 &
SERVER_PID=$!
cd ../..

# Wait and test
echo "⏳ Waiting for server..."
sleep 5

# Test connection
if curl -s http://localhost:3001/ > /dev/null 2>&1; then
    echo "✅ Server is running!"
    echo "🌐 Dashboard URL: http://localhost:3001"
    echo "📊 Server PID: $SERVER_PID"
    echo ""
    echo "🎯 Fixes Applied:"
    echo "   ✅ Enhanced terminal scrolling"
    echo "   ✅ Better viewport handling"
    echo "   ✅ Improved CSS for xterm.js"
    echo "   ✅ Auto-fix for new terminals"
    echo ""
    echo "🎮 Test Instructions:"
    echo "   1. Open http://localhost:3001 in browser"
    echo "   2. Start any program (e.g., Pharos Protocol)"
    echo "   3. Check if terminal can scroll properly"
    echo "   4. Try mouse wheel and scrollbar"
    echo "   5. Use Ctrl+Home/End for quick navigation"
    echo ""
    echo "Press Ctrl+C to stop server (PID: $SERVER_PID)"
    
    # Keep script running
    trap "kill $SERVER_PID 2>/dev/null; echo 'Server stopped'; exit 0" INT
    while kill -0 $SERVER_PID 2>/dev/null; do
        sleep 1
    done
else
    echo "❌ Server failed to start"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi
