#!/bin/bash

echo "🧪 Testing Terminal Dashboard..."

# Kill any existing processes
pkill -f "node.*server.js" 2>/dev/null || true
sleep 2

# Start backend
echo "🚀 Starting backend..."
cd terminal-dashboard/backend
node server.js &
BACKEND_PID=$!
cd ../..

# Wait for server to start
echo "⏳ Waiting for server to start..."
sleep 5

# Test API
echo "🔍 Testing API..."
if curl -s http://localhost:3001/api/programs > /dev/null; then
    echo "✅ API is responding"
    
    # Test program count
    PROGRAM_COUNT=$(curl -s http://localhost:3001/api/programs | jq length 2>/dev/null || echo "0")
    echo "📊 Found $PROGRAM_COUNT programs configured"
    
    echo ""
    echo "🌐 Dashboard is ready!"
    echo "   URL: http://localhost:3001"
    echo "   Backend PID: $BACKEND_PID"
    echo ""
    echo "🎮 Features available:"
    echo "   ✅ Start/Stop buttons for each program"
    echo "   ✅ Real-time terminal interface"
    echo "   ✅ Multiple concurrent programs"
    echo "   ✅ Terminal controls (clear, copy, download)"
    echo ""
    echo "Press Ctrl+C to stop the server"
    
    # Keep running
    wait $BACKEND_PID
else
    echo "❌ API not responding"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi
