
╔══════════════════════════════════════════════════════════════╗
║                 🚀 TERMINAL DASHBOARD SERVER                 ║
║                                                              ║
║  Server running on: http://localhost:3001                     ║
║  WebSocket ready for real-time terminal connections         ║
║                                                              ║
║  Features:                                                   ║
║  ✅ True terminal emulation with node-pty                   ║
║  ✅ Real-time WebSocket communication                        ║
║  ✅ Multiple concurrent programs                             ║
║  ✅ Interactive input/output                                 ║
║  ✅ Session management                                       ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
  
🔌 Client connected: sGXEfQvYu0KpOK_aAAAB
🔌 Client disconnected: sGXEfQvYu0KpOK_aAAAB
🔌 Client connected: c5-VsteQQP1kgPaOAAAD
🚀 Starting 0G Network...
   Directory: /home/<USER>/testnet (Salin)/0g
   Command: npm start
✅ 0G Network started with terminal ID: 991fad28-8d27-418d-ba9e-69f17d3e6f9f
🚀 Starting Pharos Protocol...
   Directory: /home/<USER>/testnet (Salin)/Pharos
   Command: node main.js
✅ Pharos Protocol started with terminal ID: 93128543-a3d1-4b4d-a709-7bb85e0e3f61
