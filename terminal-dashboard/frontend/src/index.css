@tailwind base;
@tailwind components;
@tailwind utilities;

/* XTerm.js terminal styling */
.xterm {
  font-family: 'JetBrains Mono', monospace !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
}

.xterm .xterm-viewport {
  background-color: transparent !important;
}

.xterm .xterm-screen {
  background-color: transparent !important;
}

/* Terminal container styling */
.terminal-container {
  background: #0d1117;
  border: 1px solid #30363d;
  border-radius: 8px;
  overflow: hidden;
}

.terminal-header {
  background: #161b22;
  border-bottom: 1px solid #30363d;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.terminal-content {
  height: calc(100vh - 200px);
  min-height: 500px;
  max-height: calc(100vh - 150px);
  overflow: hidden;
  position: relative;
}

.terminal-content .xterm {
  height: 100% !important;
  width: 100% !important;
}

.terminal-content .xterm .xterm-viewport {
  overflow-y: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #30363d #161b22;
}

.terminal-content .xterm .xterm-screen {
  height: auto !important;
}

/* Button styles */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

/* Program card styling */
.program-card {
  @apply bg-gray-800 border border-gray-700 rounded-lg p-6 hover:border-gray-600 transition-all duration-200;
}

.program-card.running {
  @apply border-green-500/50 bg-green-500/5;
}

/* Status dots */
.status-dot {
  @apply w-2 h-2 rounded-full;
}

.status-running {
  @apply bg-green-500 animate-pulse;
}

.status-stopped {
  @apply bg-red-500;
}

/* Loading spinner */
.loading-spinner {
  @apply w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin;
}

/* Custom scrollbar */
.terminal-content::-webkit-scrollbar,
.xterm-viewport::-webkit-scrollbar {
  width: 12px;
}

.terminal-content::-webkit-scrollbar-track,
.xterm-viewport::-webkit-scrollbar-track {
  background: #161b22;
  border-radius: 6px;
}

.terminal-content::-webkit-scrollbar-thumb,
.xterm-viewport::-webkit-scrollbar-thumb {
  background: #30363d;
  border-radius: 6px;
  border: 2px solid #161b22;
}

.terminal-content::-webkit-scrollbar-thumb:hover,
.xterm-viewport::-webkit-scrollbar-thumb:hover {
  background: #484f58;
}

/* Ensure xterm viewport can scroll */
.xterm .xterm-viewport {
  overflow-y: scroll !important;
  scrollbar-width: thin;
  scrollbar-color: #30363d #161b22;
}

/* Fix xterm screen height */
.xterm .xterm-screen {
  height: auto !important;
  min-height: 100%;
}

/* Program card styling */
.program-card {
  background: linear-gradient(135deg, #161b22 0%, #21262d 100%);
  border: 1px solid #30363d;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.program-card:hover {
  border-color: #58a6ff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(88, 166, 255, 0.15);
}

.program-card.running {
  border-color: #3fb950;
  background: linear-gradient(135deg, #0d1117 0%, #1a2332 100%);
}

/* Status indicators */
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.status-running {
  background: #3fb950;
  box-shadow: 0 0 8px rgba(63, 185, 80, 0.5);
}

.status-stopped {
  background: #f85149;
}

.status-starting {
  background: #d29922;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Button styling */
.btn-primary {
  background: linear-gradient(135deg, #238636 0%, #2ea043 100%);
  border: 1px solid #3fb950;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2ea043 0%, #3fb950 100%);
  transform: translateY(-1px);
}

.btn-secondary {
  background: #21262d;
  border: 1px solid #30363d;
  color: #f0f6fc;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #30363d;
  border-color: #484f58;
}

.btn-danger {
  background: linear-gradient(135deg, #da3633 0%, #f85149 100%);
  border: 1px solid #f85149;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #f85149 0%, #ff6b6b 100%);
  transform: translateY(-1px);
}

/* Tab styling */
.tab-button {
  background: transparent;
  border: none;
  color: #8b949e;
  padding: 12px 16px;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  font-weight: 500;
}

.tab-button.active {
  color: #58a6ff;
  border-bottom-color: #58a6ff;
}

.tab-button:hover {
  color: #f0f6fc;
  background: rgba(240, 246, 252, 0.05);
}

/* Loading animation */
.loading-spinner {
  border: 2px solid #30363d;
  border-top: 2px solid #58a6ff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
