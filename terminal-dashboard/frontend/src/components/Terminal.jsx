import React, { useEffect, useRef, useState } from 'react';
import { Terminal as XTerm } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import { SearchAddon } from '@xterm/addon-search';
import { Maximize2, Minimize2, Search, Copy, Download, Play, Square, RotateCcw } from 'lucide-react';

function Terminal({ terminalId, socket, programId, programName, status, onStop, onRestart }) {
  const terminalRef = useRef(null);
  const xtermRef = useRef(null);
  const fitAddonRef = useRef(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [searchVisible, setSearchVisible] = useState(false);
  const [terminalData, setTerminalData] = useState('');
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (!terminalRef.current || !socket) return;

    // Clear any existing terminal
    if (xtermRef.current) {
      xtermRef.current.dispose();
    }

    // Initialize xterm.js
    const xterm = new XTerm({
      theme: {
        background: '#0d1117',
        foreground: '#f0f6fc',
        cursor: '#58a6ff',
        cursorAccent: '#0d1117',
        selection: '#58a6ff40',
        black: '#484f58',
        red: '#ff7b72',
        green: '#3fb950',
        yellow: '#d29922',
        blue: '#58a6ff',
        magenta: '#bc8cff',
        cyan: '#39c5cf',
        white: '#b1bac4',
        brightBlack: '#6e7681',
        brightRed: '#ffa198',
        brightGreen: '#56d364',
        brightYellow: '#e3b341',
        brightBlue: '#79c0ff',
        brightMagenta: '#d2a8ff',
        brightCyan: '#56d4dd',
        brightWhite: '#f0f6fc'
      },
      fontFamily: 'JetBrains Mono, Fira Code, Monaco, Consolas, monospace',
      fontSize: 13,
      lineHeight: 1.2,
      cursorBlink: true,
      cursorStyle: 'block',
      scrollback: 50000,
      tabStopWidth: 4,
      allowTransparency: false,
      convertEol: true,
      rows: 30,
      cols: 120,
      scrollOnUserInput: true,
      fastScrollModifier: 'alt',
      fastScrollSensitivity: 5,
      scrollSensitivity: 1
    });

    // Add addons
    const fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();
    const searchAddon = new SearchAddon();

    xterm.loadAddon(fitAddon);
    xterm.loadAddon(webLinksAddon);
    xterm.loadAddon(searchAddon);

    // Open terminal
    xterm.open(terminalRef.current);

    // Store references
    xtermRef.current = xterm;
    fitAddonRef.current = fitAddon;

    // Fit terminal after a short delay to ensure container is ready
    setTimeout(() => {
      fitAddon.fit();
    }, 100);

    // Show welcome message
    xterm.write('\x1b[36m╔══════════════════════════════════════════════════════════════╗\x1b[0m\r\n');
    xterm.write('\x1b[36m║                 🚀 TERMINAL DASHBOARD                        ║\x1b[0m\r\n');
    xterm.write('\x1b[36m╚══════════════════════════════════════════════════════════════╝\x1b[0m\r\n');
    xterm.write(`\x1b[32m✅ Connected to: ${programName || 'Terminal'}\x1b[0m\r\n`);
    xterm.write(`\x1b[33m📋 Terminal ID: ${terminalId.slice(0, 8)}\x1b[0m\r\n`);
    xterm.write(`\x1b[34m🔌 Status: ${status || 'Ready'}\x1b[0m\r\n`);
    xterm.write('\x1b[90m─────────────────────────────────────────────────────────────\x1b[0m\r\n');

    if (status !== 'running') {
      xterm.write('\x1b[33m⚠️  Program not running. Use the Start button to begin.\x1b[0m\r\n');
    }

    xterm.write('\r\n');

    // Handle terminal input
    xterm.onData((data) => {
      socket.emit('terminal-input', {
        terminalId,
        input: data
      });
    });

    // Handle terminal resize
    xterm.onResize(({ cols, rows }) => {
      socket.emit('terminal-resize', {
        terminalId,
        cols,
        rows
      });
    });

    // Socket event listeners
    const handleTerminalData = (data) => {
      if (data.terminalId === terminalId) {
        xterm.write(data.data);
        setTerminalData(prev => prev + data.data);
      }
    };

    const handleTerminalExit = (data) => {
      if (data.terminalId === terminalId) {
        xterm.write(`\r\n\x1b[31m[Process exited with code ${data.exitCode}]\x1b[0m\r\n`);
      }
    };

    socket.on('terminal-data', handleTerminalData);
    socket.on('terminal-exit', handleTerminalExit);

    // Handle window resize
    const handleResize = () => {
      if (fitAddon && xtermRef.current) {
        setTimeout(() => {
          fitAddon.fit();
        }, 50);
      }
    };

    // Handle container resize with ResizeObserver
    const resizeObserver = new ResizeObserver(() => {
      handleResize();
    });

    if (terminalRef.current) {
      resizeObserver.observe(terminalRef.current);
    }

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      socket.off('terminal-data', handleTerminalData);
      socket.off('terminal-exit', handleTerminalExit);
      window.removeEventListener('resize', handleResize);
      resizeObserver.disconnect();

      if (xterm) {
        xterm.dispose();
      }
    };
  }, [terminalId, socket]);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    setTimeout(() => {
      if (fitAddonRef.current) {
        fitAddonRef.current.fit();
      }
    }, 100);
  };

  const copyTerminalContent = () => {
    if (xtermRef.current) {
      const selection = xtermRef.current.getSelection();
      if (selection) {
        navigator.clipboard.writeText(selection);
      } else {
        // Copy all visible content if no selection
        const buffer = xtermRef.current.buffer.active;
        let content = '';
        for (let i = 0; i < buffer.length; i++) {
          const line = buffer.getLine(i);
          if (line) {
            content += line.translateToString(true) + '\n';
          }
        }
        navigator.clipboard.writeText(content);
      }
    }
  };

  const downloadLog = () => {
    const blob = new Blob([terminalData], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `terminal-${terminalId}-${new Date().toISOString().slice(0, 19)}.log`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className={`terminal-container ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      <div className="terminal-header">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${status === 'running' ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
          </div>
          <span className="text-sm font-medium">
            {programName || `Terminal ${terminalId.slice(0, 8)}`}
          </span>
          <span className={`text-xs px-2 py-1 rounded ${
            status === 'running' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
          }`}>
            {status || 'unknown'}
          </span>
        </div>

        <div className="flex items-center gap-2">
          {/* Control Buttons */}
          {status === 'running' ? (
            <button
              onClick={() => onStop && onStop(terminalId)}
              className="p-2 hover:bg-red-500/20 rounded text-red-400 hover:text-red-300 transition-colors"
              title="Stop Program"
            >
              <Square className="w-4 h-4" />
            </button>
          ) : (
            <button
              onClick={() => onRestart && onRestart(programId)}
              className="p-2 hover:bg-green-500/20 rounded text-green-400 hover:text-green-300 transition-colors"
              title="Restart Program"
            >
              <Play className="w-4 h-4" />
            </button>
          )}

          <button
            onClick={() => {
              if (xtermRef.current) {
                xtermRef.current.clear();
                setTerminalData('');
              }
            }}
            className="p-2 hover:bg-blue-500/20 rounded text-blue-400 hover:text-blue-300 transition-colors"
            title="Clear Terminal"
          >
            <RotateCcw className="w-4 h-4" />
          </button>

          <div className="w-px h-6 bg-terminal-border mx-1"></div>

          <button
            onClick={() => setSearchVisible(!searchVisible)}
            className="p-2 hover:bg-terminal-bg rounded text-terminal-muted hover:text-terminal-text transition-colors"
            title="Search"
          >
            <Search className="w-4 h-4" />
          </button>

          <button
            onClick={copyTerminalContent}
            className="p-2 hover:bg-terminal-bg rounded text-terminal-muted hover:text-terminal-text transition-colors"
            title="Copy content"
          >
            <Copy className="w-4 h-4" />
          </button>

          <button
            onClick={downloadLog}
            className="p-2 hover:bg-terminal-bg rounded text-terminal-muted hover:text-terminal-text transition-colors"
            title="Download log"
          >
            <Download className="w-4 h-4" />
          </button>

          <button
            onClick={toggleFullscreen}
            className="p-2 hover:bg-terminal-bg rounded text-terminal-muted hover:text-terminal-text transition-colors"
            title={isFullscreen ? 'Exit fullscreen' : 'Fullscreen'}
          >
            {isFullscreen ? (
              <Minimize2 className="w-4 h-4" />
            ) : (
              <Maximize2 className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      {searchVisible && (
        <div className="bg-terminal-surface border-b border-terminal-border p-3">
          <div className="flex items-center gap-2">
            <Search className="w-4 h-4 text-terminal-muted" />
            <input
              type="text"
              placeholder="Search in terminal..."
              className="flex-1 bg-terminal-bg border border-terminal-border rounded px-3 py-1 text-sm focus:outline-none focus:border-blue-500"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && xtermRef.current) {
                  // Implement search functionality
                  console.log('Search:', e.target.value);
                }
              }}
            />
          </div>
        </div>
      )}

      <div
        ref={terminalRef}
        className={`terminal-content ${isFullscreen ? 'h-screen' : ''}`}
        style={{
          height: isFullscreen ? 'calc(100vh - 120px)' : 'calc(100vh - 300px)',
          minHeight: '400px'
        }}
      />
    </div>
  );
}

export default Terminal;
