import React, { useState, useEffect } from 'react';
import { io } from 'socket.io-client';
import ProgramGrid from './components/ProgramGrid';
import TerminalTabs from './components/TerminalTabs';
import Terminal from './components/Terminal';
import Header from './components/Header';
import { Terminal as TerminalIcon, Zap, Activity } from 'lucide-react';

function App() {
  const [socket, setSocket] = useState(null);
  const [programs, setPrograms] = useState([]);
  const [terminals, setTerminals] = useState([]);
  const [activeTerminal, setActiveTerminal] = useState(null);
  const [view, setView] = useState('dashboard'); // 'dashboard' or 'terminal'
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io('http://localhost:3001');
    setSocket(newSocket);

    // Fetch programs
    fetch('/api/programs')
      .then(res => res.json())
      .then(data => {
        setPrograms(data);
        setLoading(false);
      })
      .catch(err => {
        console.error('Failed to fetch programs:', err);
        setLoading(false);
      });

    // Socket event listeners
    newSocket.on('connect', () => {
      console.log('🔌 Connected to server');
    });

    newSocket.on('active-terminals', (activeTerminals) => {
      setTerminals(activeTerminals);
    });

    newSocket.on('terminal-data', (data) => {
      // Update terminal data in real-time
      setTerminals(prev => prev.map(term =>
        term.id === data.terminalId
          ? { ...term, lastActivity: data.timestamp }
          : term
      ));
    });

    newSocket.on('terminal-exit', (data) => {
      setTerminals(prev => prev.map(term =>
        term.id === data.terminalId
          ? { ...term, status: 'stopped', exitCode: data.exitCode }
          : term
      ));
    });

    newSocket.on('disconnect', () => {
      console.log('🔌 Disconnected from server');
    });

    return () => {
      newSocket.close();
    };
  }, []);

  const startProgram = async (programId) => {
    try {
      const response = await fetch(`/api/programs/${programId}/start`, {
        method: 'POST'
      });

      const result = await response.json();

      if (result.success) {
        const newTerminal = {
          id: result.terminalId,
          programId: result.programId,
          programName: result.programName,
          status: 'running',
          startTime: new Date().toISOString(),
          pid: result.pid
        };

        setTerminals(prev => [...prev, newTerminal]);
        setActiveTerminal(result.terminalId);
        setView('terminal');
      }
    } catch (error) {
      console.error('Failed to start program:', error);
    }
  };

  const stopTerminal = async (terminalId) => {
    try {
      await fetch(`/api/terminals/${terminalId}/stop`, {
        method: 'POST'
      });

      setTerminals(prev => prev.map(term =>
        term.id === terminalId
          ? { ...term, status: 'stopped' }
          : term
      ));
    } catch (error) {
      console.error('Failed to stop terminal:', error);
    }
  };

  const closeTerminal = (terminalId) => {
    setTerminals(prev => prev.filter(term => term.id !== terminalId));

    if (activeTerminal === terminalId) {
      const remainingTerminals = terminals.filter(term => term.id !== terminalId);
      setActiveTerminal(remainingTerminals.length > 0 ? remainingTerminals[0].id : null);

      if (remainingTerminals.length === 0) {
        setView('dashboard');
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-terminal-bg flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-terminal-muted">Loading Terminal Dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-terminal-bg text-terminal-text">
      <Header
        view={view}
        setView={setView}
        terminalsCount={terminals.length}
        runningCount={terminals.filter(t => t.status === 'running').length}
      />

      <main className="container mx-auto px-4 py-6">
        {view === 'dashboard' ? (
          <div>
            <div className="mb-8">
              <h1 className="text-3xl font-bold mb-2 flex items-center gap-3">
                <TerminalIcon className="text-blue-400" />
                Terminal Dashboard
              </h1>
              <p className="text-terminal-muted">
                Manage and monitor your testnet programs with real-time terminal access
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
              <div className="bg-terminal-surface border border-terminal-border rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/20 rounded-lg">
                    <TerminalIcon className="w-5 h-5 text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm text-terminal-muted">Active Terminals</p>
                    <p className="text-2xl font-bold">{terminals.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-terminal-surface border border-terminal-border rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-500/20 rounded-lg">
                    <Activity className="w-5 h-5 text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm text-terminal-muted">Running Programs</p>
                    <p className="text-2xl font-bold text-green-400">
                      {terminals.filter(t => t.status === 'running').length}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-terminal-surface border border-terminal-border rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-500/20 rounded-lg">
                    <Zap className="w-5 h-5 text-purple-400" />
                  </div>
                  <div>
                    <p className="text-sm text-terminal-muted">Available Programs</p>
                    <p className="text-2xl font-bold text-purple-400">{programs.length}</p>
                  </div>
                </div>
              </div>
            </div>

            <ProgramGrid
              programs={programs}
              terminals={terminals}
              onStartProgram={startProgram}
              onStopProgram={stopTerminal}
              onViewTerminal={(terminalId) => {
                setActiveTerminal(terminalId);
                setView('terminal');
              }}
            />
          </div>
        ) : (
          <div className="space-y-4">
            <TerminalTabs
              terminals={terminals}
              activeTerminal={activeTerminal}
              onSelectTerminal={setActiveTerminal}
              onCloseTerminal={closeTerminal}
              onStopTerminal={stopTerminal}
            />

            {activeTerminal && (
              <Terminal
                terminalId={activeTerminal}
                socket={socket}
                programId={terminals.find(t => t.id === activeTerminal)?.programId}
                programName={terminals.find(t => t.id === activeTerminal)?.programName}
                status={terminals.find(t => t.id === activeTerminal)?.status}
                onStop={stopTerminal}
                onRestart={startProgram}
              />
            )}
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
