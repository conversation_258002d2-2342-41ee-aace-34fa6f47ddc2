<!doctype html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/terminal-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🚀 Terminal Dashboard - Testnet Programs</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
      body {
        font-family: 'JetBrains Mono', monospace;
        background: #0d1117;
        color: #f0f6fc;
      }

      /* Custom scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      ::-webkit-scrollbar-track {
        background: #161b22;
      }

      ::-webkit-scrollbar-thumb {
        background: #30363d;
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #484f58;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    <script>
      // Terminal scroll fix - inline for immediate loading
      (function() {
        console.log('🔧 Applying terminal scroll fixes...');

        // Enhanced CSS for terminal scrolling
        const style = document.createElement('style');
        style.textContent = \`
          .terminal-content {
            height: calc(100vh - 200px) !important;
            min-height: 500px !important;
            overflow: hidden !important;
            position: relative !important;
          }

          .xterm .xterm-viewport {
            overflow-y: auto !important;
            scrollbar-width: thin !important;
            scrollbar-color: #30363d #161b22 !important;
          }

          .xterm .xterm-screen {
            height: auto !important;
            min-height: 100% !important;
          }

          .xterm-viewport::-webkit-scrollbar {
            width: 12px !important;
          }

          .xterm-viewport::-webkit-scrollbar-track {
            background: #161b22 !important;
            border-radius: 6px !important;
          }

          .xterm-viewport::-webkit-scrollbar-thumb {
            background: #30363d !important;
            border-radius: 6px !important;
            border: 2px solid #161b22 !important;
          }

          .xterm-viewport::-webkit-scrollbar-thumb:hover {
            background: #484f58 !important;
          }
        \`;
        document.head.appendChild(style);

        // Monitor for terminal creation and fix scrolling
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === 1 && node.querySelector) {
                const viewport = node.querySelector('.xterm-viewport');
                if (viewport) {
                  setTimeout(() => {
                    viewport.style.overflowY = 'auto';
                    console.log('✅ Fixed terminal viewport scrolling');
                  }, 100);
                }
              }
            });
          });
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });

        console.log('✅ Terminal scroll monitor active');
      })();
    </script>
  </body>
</html>
