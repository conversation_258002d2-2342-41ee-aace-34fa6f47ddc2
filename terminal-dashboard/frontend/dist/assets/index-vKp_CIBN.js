(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))i(l);new MutationObserver(l=>{for(const d of l)if(d.type==="childList")for(const m of d.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&i(m)}).observe(document,{childList:!0,subtree:!0});function r(l){const d={};return l.integrity&&(d.integrity=l.integrity),l.referrerPolicy&&(d.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?d.credentials="include":l.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function i(l){if(l.ep)return;l.ep=!0;const d=r(l);fetch(l.href,d)}})();function Iu(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var rc={exports:{}},Us={},ic={exports:{}},Z={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ti=Symbol.for("react.element"),Nu=Symbol.for("react.portal"),Hu=Symbol.for("react.fragment"),Fu=Symbol.for("react.strict_mode"),ju=Symbol.for("react.profiler"),zu=Symbol.for("react.provider"),Wu=Symbol.for("react.context"),Uu=Symbol.for("react.forward_ref"),$u=Symbol.for("react.suspense"),Vu=Symbol.for("react.memo"),Ku=Symbol.for("react.lazy"),zl=Symbol.iterator;function qu(e){return e===null||typeof e!="object"?null:(e=zl&&e[zl]||e["@@iterator"],typeof e=="function"?e:null)}var sc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},nc=Object.assign,oc={};function Ir(e,t,r){this.props=e,this.context=t,this.refs=oc,this.updater=r||sc}Ir.prototype.isReactComponent={};Ir.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Ir.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function lc(){}lc.prototype=Ir.prototype;function zo(e,t,r){this.props=e,this.context=t,this.refs=oc,this.updater=r||sc}var Wo=zo.prototype=new lc;Wo.constructor=zo;nc(Wo,Ir.prototype);Wo.isPureReactComponent=!0;var Wl=Array.isArray,ac=Object.prototype.hasOwnProperty,Uo={current:null},cc={key:!0,ref:!0,__self:!0,__source:!0};function hc(e,t,r){var i,l={},d=null,m=null;if(t!=null)for(i in t.ref!==void 0&&(m=t.ref),t.key!==void 0&&(d=""+t.key),t)ac.call(t,i)&&!cc.hasOwnProperty(i)&&(l[i]=t[i]);var n=arguments.length-2;if(n===1)l.children=r;else if(1<n){for(var c=Array(n),f=0;f<n;f++)c[f]=arguments[f+2];l.children=c}if(e&&e.defaultProps)for(i in n=e.defaultProps,n)l[i]===void 0&&(l[i]=n[i]);return{$$typeof:Ti,type:e,key:d,ref:m,props:l,_owner:Uo.current}}function Xu(e,t){return{$$typeof:Ti,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function $o(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ti}function Gu(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var Ul=/\/+/g;function dn(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Gu(""+e.key):t.toString(36)}function rs(e,t,r,i,l){var d=typeof e;(d==="undefined"||d==="boolean")&&(e=null);var m=!1;if(e===null)m=!0;else switch(d){case"string":case"number":m=!0;break;case"object":switch(e.$$typeof){case Ti:case Nu:m=!0}}if(m)return m=e,l=l(m),e=i===""?"."+dn(m,0):i,Wl(l)?(r="",e!=null&&(r=e.replace(Ul,"$&/")+"/"),rs(l,t,r,"",function(f){return f})):l!=null&&($o(l)&&(l=Xu(l,r+(!l.key||m&&m.key===l.key?"":(""+l.key).replace(Ul,"$&/")+"/")+e)),t.push(l)),1;if(m=0,i=i===""?".":i+":",Wl(e))for(var n=0;n<e.length;n++){d=e[n];var c=i+dn(d,n);m+=rs(d,t,r,c,l)}else if(c=qu(e),typeof c=="function")for(e=c.call(e),n=0;!(d=e.next()).done;)d=d.value,c=i+dn(d,n++),m+=rs(d,t,r,c,l);else if(d==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return m}function Ni(e,t,r){if(e==null)return e;var i=[],l=0;return rs(e,i,"","",function(d){return t.call(r,d,l++)}),i}function Yu(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Be={current:null},is={transition:null},Qu={ReactCurrentDispatcher:Be,ReactCurrentBatchConfig:is,ReactCurrentOwner:Uo};function uc(){throw Error("act(...) is not supported in production builds of React.")}Z.Children={map:Ni,forEach:function(e,t,r){Ni(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return Ni(e,function(){t++}),t},toArray:function(e){return Ni(e,function(t){return t})||[]},only:function(e){if(!$o(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Z.Component=Ir;Z.Fragment=Hu;Z.Profiler=ju;Z.PureComponent=zo;Z.StrictMode=Fu;Z.Suspense=$u;Z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Qu;Z.act=uc;Z.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var i=nc({},e.props),l=e.key,d=e.ref,m=e._owner;if(t!=null){if(t.ref!==void 0&&(d=t.ref,m=Uo.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var n=e.type.defaultProps;for(c in t)ac.call(t,c)&&!cc.hasOwnProperty(c)&&(i[c]=t[c]===void 0&&n!==void 0?n[c]:t[c])}var c=arguments.length-2;if(c===1)i.children=r;else if(1<c){n=Array(c);for(var f=0;f<c;f++)n[f]=arguments[f+2];i.children=n}return{$$typeof:Ti,type:e.type,key:l,ref:d,props:i,_owner:m}};Z.createContext=function(e){return e={$$typeof:Wu,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:zu,_context:e},e.Consumer=e};Z.createElement=hc;Z.createFactory=function(e){var t=hc.bind(null,e);return t.type=e,t};Z.createRef=function(){return{current:null}};Z.forwardRef=function(e){return{$$typeof:Uu,render:e}};Z.isValidElement=$o;Z.lazy=function(e){return{$$typeof:Ku,_payload:{_status:-1,_result:e},_init:Yu}};Z.memo=function(e,t){return{$$typeof:Vu,type:e,compare:t===void 0?null:t}};Z.startTransition=function(e){var t=is.transition;is.transition={};try{e()}finally{is.transition=t}};Z.unstable_act=uc;Z.useCallback=function(e,t){return Be.current.useCallback(e,t)};Z.useContext=function(e){return Be.current.useContext(e)};Z.useDebugValue=function(){};Z.useDeferredValue=function(e){return Be.current.useDeferredValue(e)};Z.useEffect=function(e,t){return Be.current.useEffect(e,t)};Z.useId=function(){return Be.current.useId()};Z.useImperativeHandle=function(e,t,r){return Be.current.useImperativeHandle(e,t,r)};Z.useInsertionEffect=function(e,t){return Be.current.useInsertionEffect(e,t)};Z.useLayoutEffect=function(e,t){return Be.current.useLayoutEffect(e,t)};Z.useMemo=function(e,t){return Be.current.useMemo(e,t)};Z.useReducer=function(e,t,r){return Be.current.useReducer(e,t,r)};Z.useRef=function(e){return Be.current.useRef(e)};Z.useState=function(e){return Be.current.useState(e)};Z.useSyncExternalStore=function(e,t,r){return Be.current.useSyncExternalStore(e,t,r)};Z.useTransition=function(){return Be.current.useTransition()};Z.version="18.3.1";ic.exports=Z;var pe=ic.exports;const Ju=Iu(pe);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zu=pe,ed=Symbol.for("react.element"),td=Symbol.for("react.fragment"),rd=Object.prototype.hasOwnProperty,id=Zu.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,sd={key:!0,ref:!0,__self:!0,__source:!0};function dc(e,t,r){var i,l={},d=null,m=null;r!==void 0&&(d=""+r),t.key!==void 0&&(d=""+t.key),t.ref!==void 0&&(m=t.ref);for(i in t)rd.call(t,i)&&!sd.hasOwnProperty(i)&&(l[i]=t[i]);if(e&&e.defaultProps)for(i in t=e.defaultProps,t)l[i]===void 0&&(l[i]=t[i]);return{$$typeof:ed,type:e,key:d,ref:m,props:l,_owner:id.current}}Us.Fragment=td;Us.jsx=dc;Us.jsxs=dc;rc.exports=Us;var I=rc.exports,zn={},fc={exports:{}},Ve={},_c={exports:{}},pc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,x){var R=N.length;N.push(x);e:for(;0<R;){var A=R-1>>>1,P=N[A];if(0<l(P,x))N[A]=x,N[R]=P,R=A;else break e}}function r(N){return N.length===0?null:N[0]}function i(N){if(N.length===0)return null;var x=N[0],R=N.pop();if(R!==x){N[0]=R;e:for(var A=0,P=N.length,z=P>>>1;A<z;){var V=2*(A+1)-1,Y=N[V],Q=V+1,M=N[Q];if(0>l(Y,R))Q<P&&0>l(M,Y)?(N[A]=M,N[Q]=R,A=Q):(N[A]=Y,N[V]=R,A=V);else if(Q<P&&0>l(M,R))N[A]=M,N[Q]=R,A=Q;else break e}}return x}function l(N,x){var R=N.sortIndex-x.sortIndex;return R!==0?R:N.id-x.id}if(typeof performance=="object"&&typeof performance.now=="function"){var d=performance;e.unstable_now=function(){return d.now()}}else{var m=Date,n=m.now();e.unstable_now=function(){return m.now()-n}}var c=[],f=[],S=1,u=null,_=3,g=!1,C=!1,v=!1,h=typeof setTimeout=="function"?setTimeout:null,a=typeof clearTimeout=="function"?clearTimeout:null,s=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function o(N){for(var x=r(f);x!==null;){if(x.callback===null)i(f);else if(x.startTime<=N)i(f),x.sortIndex=x.expirationTime,t(c,x);else break;x=r(f)}}function p(N){if(v=!1,o(N),!C)if(r(c)!==null)C=!0,$(w);else{var x=r(f);x!==null&&q(p,x.startTime-N)}}function w(N,x){C=!1,v&&(v=!1,a(y),y=-1),g=!0;var R=_;try{for(o(x),u=r(c);u!==null&&(!(u.expirationTime>x)||N&&!B());){var A=u.callback;if(typeof A=="function"){u.callback=null,_=u.priorityLevel;var P=A(u.expirationTime<=x);x=e.unstable_now(),typeof P=="function"?u.callback=P:u===r(c)&&i(c),o(x)}else i(c);u=r(c)}if(u!==null)var z=!0;else{var V=r(f);V!==null&&q(p,V.startTime-x),z=!1}return z}finally{u=null,_=R,g=!1}}var E=!1,k=null,y=-1,b=5,D=-1;function B(){return!(e.unstable_now()-D<b)}function O(){if(k!==null){var N=e.unstable_now();D=N;var x=!0;try{x=k(!0,N)}finally{x?T():(E=!1,k=null)}}else E=!1}var T;if(typeof s=="function")T=function(){s(O)};else if(typeof MessageChannel<"u"){var H=new MessageChannel,j=H.port2;H.port1.onmessage=O,T=function(){j.postMessage(null)}}else T=function(){h(O,0)};function $(N){k=N,E||(E=!0,T())}function q(N,x){y=h(function(){N(e.unstable_now())},x)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){C||g||(C=!0,$(w))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):b=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return _},e.unstable_getFirstCallbackNode=function(){return r(c)},e.unstable_next=function(N){switch(_){case 1:case 2:case 3:var x=3;break;default:x=_}var R=_;_=x;try{return N()}finally{_=R}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,x){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var R=_;_=N;try{return x()}finally{_=R}},e.unstable_scheduleCallback=function(N,x,R){var A=e.unstable_now();switch(typeof R=="object"&&R!==null?(R=R.delay,R=typeof R=="number"&&0<R?A+R:A):R=A,N){case 1:var P=-1;break;case 2:P=250;break;case 5:P=**********;break;case 4:P=1e4;break;default:P=5e3}return P=R+P,N={id:S++,callback:x,priorityLevel:N,startTime:R,expirationTime:P,sortIndex:-1},R>A?(N.sortIndex=R,t(f,N),r(c)===null&&N===r(f)&&(v?(a(y),y=-1):v=!0,q(p,R-A))):(N.sortIndex=P,t(c,N),C||g||(C=!0,$(w))),N},e.unstable_shouldYield=B,e.unstable_wrapCallback=function(N){var x=_;return function(){var R=_;_=x;try{return N.apply(this,arguments)}finally{_=R}}}})(pc);_c.exports=pc;var nd=_c.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var od=pe,$e=nd;function U(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var vc=new Set,fi={};function ar(e,t){Dr(e,t),Dr(e+"Capture",t)}function Dr(e,t){for(fi[e]=t,e=0;e<t.length;e++)vc.add(t[e])}var Ct=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Wn=Object.prototype.hasOwnProperty,ld=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,$l={},Vl={};function ad(e){return Wn.call(Vl,e)?!0:Wn.call($l,e)?!1:ld.test(e)?Vl[e]=!0:($l[e]=!0,!1)}function cd(e,t,r,i){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return i?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function hd(e,t,r,i){if(t===null||typeof t>"u"||cd(e,t,r,i))return!0;if(i)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Oe(e,t,r,i,l,d,m){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=i,this.attributeNamespace=l,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=d,this.removeEmptyString=m}var xe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){xe[e]=new Oe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];xe[t]=new Oe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){xe[e]=new Oe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){xe[e]=new Oe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){xe[e]=new Oe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){xe[e]=new Oe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){xe[e]=new Oe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){xe[e]=new Oe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){xe[e]=new Oe(e,5,!1,e.toLowerCase(),null,!1,!1)});var Vo=/[\-:]([a-z])/g;function Ko(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Vo,Ko);xe[t]=new Oe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Vo,Ko);xe[t]=new Oe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Vo,Ko);xe[t]=new Oe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){xe[e]=new Oe(e,1,!1,e.toLowerCase(),null,!1,!1)});xe.xlinkHref=new Oe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){xe[e]=new Oe(e,1,!1,e.toLowerCase(),null,!0,!0)});function qo(e,t,r,i){var l=xe.hasOwnProperty(t)?xe[t]:null;(l!==null?l.type!==0:i||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(hd(t,r,l,i)&&(r=null),i||l===null?ad(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):l.mustUseProperty?e[l.propertyName]=r===null?l.type===3?!1:"":r:(t=l.attributeName,i=l.attributeNamespace,r===null?e.removeAttribute(t):(l=l.type,r=l===3||l===4&&r===!0?"":""+r,i?e.setAttributeNS(i,t,r):e.setAttribute(t,r))))}var bt=od.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Hi=Symbol.for("react.element"),ur=Symbol.for("react.portal"),dr=Symbol.for("react.fragment"),Xo=Symbol.for("react.strict_mode"),Un=Symbol.for("react.profiler"),gc=Symbol.for("react.provider"),mc=Symbol.for("react.context"),Go=Symbol.for("react.forward_ref"),$n=Symbol.for("react.suspense"),Vn=Symbol.for("react.suspense_list"),Yo=Symbol.for("react.memo"),Tt=Symbol.for("react.lazy"),Sc=Symbol.for("react.offscreen"),Kl=Symbol.iterator;function $r(e){return e===null||typeof e!="object"?null:(e=Kl&&e[Kl]||e["@@iterator"],typeof e=="function"?e:null)}var ue=Object.assign,fn;function Zr(e){if(fn===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);fn=t&&t[1]||""}return`
`+fn+e}var _n=!1;function pn(e,t){if(!e||_n)return"";_n=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(f){var i=f}Reflect.construct(e,[],t)}else{try{t.call()}catch(f){i=f}e.call(t.prototype)}else{try{throw Error()}catch(f){i=f}e()}}catch(f){if(f&&i&&typeof f.stack=="string"){for(var l=f.stack.split(`
`),d=i.stack.split(`
`),m=l.length-1,n=d.length-1;1<=m&&0<=n&&l[m]!==d[n];)n--;for(;1<=m&&0<=n;m--,n--)if(l[m]!==d[n]){if(m!==1||n!==1)do if(m--,n--,0>n||l[m]!==d[n]){var c=`
`+l[m].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=m&&0<=n);break}}}finally{_n=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?Zr(e):""}function ud(e){switch(e.tag){case 5:return Zr(e.type);case 16:return Zr("Lazy");case 13:return Zr("Suspense");case 19:return Zr("SuspenseList");case 0:case 2:case 15:return e=pn(e.type,!1),e;case 11:return e=pn(e.type.render,!1),e;case 1:return e=pn(e.type,!0),e;default:return""}}function Kn(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case dr:return"Fragment";case ur:return"Portal";case Un:return"Profiler";case Xo:return"StrictMode";case $n:return"Suspense";case Vn:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case mc:return(e.displayName||"Context")+".Consumer";case gc:return(e._context.displayName||"Context")+".Provider";case Go:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Yo:return t=e.displayName||null,t!==null?t:Kn(e.type)||"Memo";case Tt:t=e._payload,e=e._init;try{return Kn(e(t))}catch{}}return null}function dd(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Kn(t);case 8:return t===Xo?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Vt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function yc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function fd(e){var t=yc(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var l=r.get,d=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(m){i=""+m,d.call(this,m)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return i},setValue:function(m){i=""+m},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Fi(e){e._valueTracker||(e._valueTracker=fd(e))}function wc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),i="";return e&&(i=yc(e)?e.checked?"true":"false":e.value),e=i,e!==r?(t.setValue(e),!0):!1}function ms(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function qn(e,t){var r=t.checked;return ue({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function ql(e,t){var r=t.defaultValue==null?"":t.defaultValue,i=t.checked!=null?t.checked:t.defaultChecked;r=Vt(t.value!=null?t.value:r),e._wrapperState={initialChecked:i,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Cc(e,t){t=t.checked,t!=null&&qo(e,"checked",t,!1)}function Xn(e,t){Cc(e,t);var r=Vt(t.value),i=t.type;if(r!=null)i==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(i==="submit"||i==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Gn(e,t.type,r):t.hasOwnProperty("defaultValue")&&Gn(e,t.type,Vt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Xl(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var i=t.type;if(!(i!=="submit"&&i!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function Gn(e,t,r){(t!=="number"||ms(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var ei=Array.isArray;function Er(e,t,r,i){if(e=e.options,t){t={};for(var l=0;l<r.length;l++)t["$"+r[l]]=!0;for(r=0;r<e.length;r++)l=t.hasOwnProperty("$"+e[r].value),e[r].selected!==l&&(e[r].selected=l),l&&i&&(e[r].defaultSelected=!0)}else{for(r=""+Vt(r),t=null,l=0;l<e.length;l++){if(e[l].value===r){e[l].selected=!0,i&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Yn(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(U(91));return ue({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Gl(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(U(92));if(ei(r)){if(1<r.length)throw Error(U(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:Vt(r)}}function Ec(e,t){var r=Vt(t.value),i=Vt(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),i!=null&&(e.defaultValue=""+i)}function Yl(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function kc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Qn(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?kc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ji,xc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,i,l){MSApp.execUnsafeLocalFunction(function(){return e(t,r,i,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ji=ji||document.createElement("div"),ji.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ji.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function _i(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var si={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},_d=["Webkit","ms","Moz","O"];Object.keys(si).forEach(function(e){_d.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),si[t]=si[e]})});function bc(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||si.hasOwnProperty(e)&&si[e]?(""+t).trim():t+"px"}function Lc(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var i=r.indexOf("--")===0,l=bc(r,t[r],i);r==="float"&&(r="cssFloat"),i?e.setProperty(r,l):e[r]=l}}var pd=ue({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Jn(e,t){if(t){if(pd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(U(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(U(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(U(61))}if(t.style!=null&&typeof t.style!="object")throw Error(U(62))}}function Zn(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var eo=null;function Qo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var to=null,kr=null,xr=null;function Ql(e){if(e=Oi(e)){if(typeof to!="function")throw Error(U(280));var t=e.stateNode;t&&(t=Xs(t),to(e.stateNode,e.type,t))}}function Rc(e){kr?xr?xr.push(e):xr=[e]:kr=e}function Dc(){if(kr){var e=kr,t=xr;if(xr=kr=null,Ql(e),t)for(e=0;e<t.length;e++)Ql(t[e])}}function Tc(e,t){return e(t)}function Ac(){}var vn=!1;function Bc(e,t,r){if(vn)return e(t,r);vn=!0;try{return Tc(e,t,r)}finally{vn=!1,(kr!==null||xr!==null)&&(Ac(),Dc())}}function pi(e,t){var r=e.stateNode;if(r===null)return null;var i=Xs(r);if(i===null)return null;r=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(U(231,t,typeof r));return r}var ro=!1;if(Ct)try{var Vr={};Object.defineProperty(Vr,"passive",{get:function(){ro=!0}}),window.addEventListener("test",Vr,Vr),window.removeEventListener("test",Vr,Vr)}catch{ro=!1}function vd(e,t,r,i,l,d,m,n,c){var f=Array.prototype.slice.call(arguments,3);try{t.apply(r,f)}catch(S){this.onError(S)}}var ni=!1,Ss=null,ys=!1,io=null,gd={onError:function(e){ni=!0,Ss=e}};function md(e,t,r,i,l,d,m,n,c){ni=!1,Ss=null,vd.apply(gd,arguments)}function Sd(e,t,r,i,l,d,m,n,c){if(md.apply(this,arguments),ni){if(ni){var f=Ss;ni=!1,Ss=null}else throw Error(U(198));ys||(ys=!0,io=f)}}function cr(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function Oc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Jl(e){if(cr(e)!==e)throw Error(U(188))}function yd(e){var t=e.alternate;if(!t){if(t=cr(e),t===null)throw Error(U(188));return t!==e?null:e}for(var r=e,i=t;;){var l=r.return;if(l===null)break;var d=l.alternate;if(d===null){if(i=l.return,i!==null){r=i;continue}break}if(l.child===d.child){for(d=l.child;d;){if(d===r)return Jl(l),e;if(d===i)return Jl(l),t;d=d.sibling}throw Error(U(188))}if(r.return!==i.return)r=l,i=d;else{for(var m=!1,n=l.child;n;){if(n===r){m=!0,r=l,i=d;break}if(n===i){m=!0,i=l,r=d;break}n=n.sibling}if(!m){for(n=d.child;n;){if(n===r){m=!0,r=d,i=l;break}if(n===i){m=!0,i=d,r=l;break}n=n.sibling}if(!m)throw Error(U(189))}}if(r.alternate!==i)throw Error(U(190))}if(r.tag!==3)throw Error(U(188));return r.stateNode.current===r?e:t}function Pc(e){return e=yd(e),e!==null?Mc(e):null}function Mc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Mc(e);if(t!==null)return t;e=e.sibling}return null}var Ic=$e.unstable_scheduleCallback,Zl=$e.unstable_cancelCallback,wd=$e.unstable_shouldYield,Cd=$e.unstable_requestPaint,ve=$e.unstable_now,Ed=$e.unstable_getCurrentPriorityLevel,Jo=$e.unstable_ImmediatePriority,Nc=$e.unstable_UserBlockingPriority,ws=$e.unstable_NormalPriority,kd=$e.unstable_LowPriority,Hc=$e.unstable_IdlePriority,$s=null,ft=null;function xd(e){if(ft&&typeof ft.onCommitFiberRoot=="function")try{ft.onCommitFiberRoot($s,e,void 0,(e.current.flags&128)===128)}catch{}}var ot=Math.clz32?Math.clz32:Rd,bd=Math.log,Ld=Math.LN2;function Rd(e){return e>>>=0,e===0?32:31-(bd(e)/Ld|0)|0}var zi=64,Wi=4194304;function ti(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Cs(e,t){var r=e.pendingLanes;if(r===0)return 0;var i=0,l=e.suspendedLanes,d=e.pingedLanes,m=r&268435455;if(m!==0){var n=m&~l;n!==0?i=ti(n):(d&=m,d!==0&&(i=ti(d)))}else m=r&~l,m!==0?i=ti(m):d!==0&&(i=ti(d));if(i===0)return 0;if(t!==0&&t!==i&&!(t&l)&&(l=i&-i,d=t&-t,l>=d||l===16&&(d&4194240)!==0))return t;if(i&4&&(i|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=i;0<t;)r=31-ot(t),l=1<<r,i|=e[r],t&=~l;return i}function Dd(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Td(e,t){for(var r=e.suspendedLanes,i=e.pingedLanes,l=e.expirationTimes,d=e.pendingLanes;0<d;){var m=31-ot(d),n=1<<m,c=l[m];c===-1?(!(n&r)||n&i)&&(l[m]=Dd(n,t)):c<=t&&(e.expiredLanes|=n),d&=~n}}function so(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Fc(){var e=zi;return zi<<=1,!(zi&4194240)&&(zi=64),e}function gn(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function Ai(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ot(t),e[t]=r}function Ad(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var i=e.eventTimes;for(e=e.expirationTimes;0<r;){var l=31-ot(r),d=1<<l;t[l]=0,i[l]=-1,e[l]=-1,r&=~d}}function Zo(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var i=31-ot(r),l=1<<i;l&t|e[i]&t&&(e[i]|=t),r&=~l}}var ie=0;function jc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var zc,el,Wc,Uc,$c,no=!1,Ui=[],It=null,Nt=null,Ht=null,vi=new Map,gi=new Map,Bt=[],Bd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ea(e,t){switch(e){case"focusin":case"focusout":It=null;break;case"dragenter":case"dragleave":Nt=null;break;case"mouseover":case"mouseout":Ht=null;break;case"pointerover":case"pointerout":vi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":gi.delete(t.pointerId)}}function Kr(e,t,r,i,l,d){return e===null||e.nativeEvent!==d?(e={blockedOn:t,domEventName:r,eventSystemFlags:i,nativeEvent:d,targetContainers:[l]},t!==null&&(t=Oi(t),t!==null&&el(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Od(e,t,r,i,l){switch(t){case"focusin":return It=Kr(It,e,t,r,i,l),!0;case"dragenter":return Nt=Kr(Nt,e,t,r,i,l),!0;case"mouseover":return Ht=Kr(Ht,e,t,r,i,l),!0;case"pointerover":var d=l.pointerId;return vi.set(d,Kr(vi.get(d)||null,e,t,r,i,l)),!0;case"gotpointercapture":return d=l.pointerId,gi.set(d,Kr(gi.get(d)||null,e,t,r,i,l)),!0}return!1}function Vc(e){var t=Jt(e.target);if(t!==null){var r=cr(t);if(r!==null){if(t=r.tag,t===13){if(t=Oc(r),t!==null){e.blockedOn=t,$c(e.priority,function(){Wc(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ss(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=oo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var i=new r.constructor(r.type,r);eo=i,r.target.dispatchEvent(i),eo=null}else return t=Oi(r),t!==null&&el(t),e.blockedOn=r,!1;t.shift()}return!0}function ta(e,t,r){ss(e)&&r.delete(t)}function Pd(){no=!1,It!==null&&ss(It)&&(It=null),Nt!==null&&ss(Nt)&&(Nt=null),Ht!==null&&ss(Ht)&&(Ht=null),vi.forEach(ta),gi.forEach(ta)}function qr(e,t){e.blockedOn===t&&(e.blockedOn=null,no||(no=!0,$e.unstable_scheduleCallback($e.unstable_NormalPriority,Pd)))}function mi(e){function t(l){return qr(l,e)}if(0<Ui.length){qr(Ui[0],e);for(var r=1;r<Ui.length;r++){var i=Ui[r];i.blockedOn===e&&(i.blockedOn=null)}}for(It!==null&&qr(It,e),Nt!==null&&qr(Nt,e),Ht!==null&&qr(Ht,e),vi.forEach(t),gi.forEach(t),r=0;r<Bt.length;r++)i=Bt[r],i.blockedOn===e&&(i.blockedOn=null);for(;0<Bt.length&&(r=Bt[0],r.blockedOn===null);)Vc(r),r.blockedOn===null&&Bt.shift()}var br=bt.ReactCurrentBatchConfig,Es=!0;function Md(e,t,r,i){var l=ie,d=br.transition;br.transition=null;try{ie=1,tl(e,t,r,i)}finally{ie=l,br.transition=d}}function Id(e,t,r,i){var l=ie,d=br.transition;br.transition=null;try{ie=4,tl(e,t,r,i)}finally{ie=l,br.transition=d}}function tl(e,t,r,i){if(Es){var l=oo(e,t,r,i);if(l===null)Ln(e,t,i,ks,r),ea(e,i);else if(Od(l,e,t,r,i))i.stopPropagation();else if(ea(e,i),t&4&&-1<Bd.indexOf(e)){for(;l!==null;){var d=Oi(l);if(d!==null&&zc(d),d=oo(e,t,r,i),d===null&&Ln(e,t,i,ks,r),d===l)break;l=d}l!==null&&i.stopPropagation()}else Ln(e,t,i,null,r)}}var ks=null;function oo(e,t,r,i){if(ks=null,e=Qo(i),e=Jt(e),e!==null)if(t=cr(e),t===null)e=null;else if(r=t.tag,r===13){if(e=Oc(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ks=e,null}function Kc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ed()){case Jo:return 1;case Nc:return 4;case ws:case kd:return 16;case Hc:return 536870912;default:return 16}default:return 16}}var Pt=null,rl=null,ns=null;function qc(){if(ns)return ns;var e,t=rl,r=t.length,i,l="value"in Pt?Pt.value:Pt.textContent,d=l.length;for(e=0;e<r&&t[e]===l[e];e++);var m=r-e;for(i=1;i<=m&&t[r-i]===l[d-i];i++);return ns=l.slice(e,1<i?1-i:void 0)}function os(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function $i(){return!0}function ra(){return!1}function Ke(e){function t(r,i,l,d,m){this._reactName=r,this._targetInst=l,this.type=i,this.nativeEvent=d,this.target=m,this.currentTarget=null;for(var n in e)e.hasOwnProperty(n)&&(r=e[n],this[n]=r?r(d):d[n]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?$i:ra,this.isPropagationStopped=ra,this}return ue(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=$i)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=$i)},persist:function(){},isPersistent:$i}),t}var Nr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},il=Ke(Nr),Bi=ue({},Nr,{view:0,detail:0}),Nd=Ke(Bi),mn,Sn,Xr,Vs=ue({},Bi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:sl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Xr&&(Xr&&e.type==="mousemove"?(mn=e.screenX-Xr.screenX,Sn=e.screenY-Xr.screenY):Sn=mn=0,Xr=e),mn)},movementY:function(e){return"movementY"in e?e.movementY:Sn}}),ia=Ke(Vs),Hd=ue({},Vs,{dataTransfer:0}),Fd=Ke(Hd),jd=ue({},Bi,{relatedTarget:0}),yn=Ke(jd),zd=ue({},Nr,{animationName:0,elapsedTime:0,pseudoElement:0}),Wd=Ke(zd),Ud=ue({},Nr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),$d=Ke(Ud),Vd=ue({},Nr,{data:0}),sa=Ke(Vd),Kd={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},qd={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Xd={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Gd(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Xd[e])?!!t[e]:!1}function sl(){return Gd}var Yd=ue({},Bi,{key:function(e){if(e.key){var t=Kd[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=os(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?qd[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:sl,charCode:function(e){return e.type==="keypress"?os(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?os(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Qd=Ke(Yd),Jd=ue({},Vs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),na=Ke(Jd),Zd=ue({},Bi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:sl}),ef=Ke(Zd),tf=ue({},Nr,{propertyName:0,elapsedTime:0,pseudoElement:0}),rf=Ke(tf),sf=ue({},Vs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),nf=Ke(sf),of=[9,13,27,32],nl=Ct&&"CompositionEvent"in window,oi=null;Ct&&"documentMode"in document&&(oi=document.documentMode);var lf=Ct&&"TextEvent"in window&&!oi,Xc=Ct&&(!nl||oi&&8<oi&&11>=oi),oa=" ",la=!1;function Gc(e,t){switch(e){case"keyup":return of.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Yc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var fr=!1;function af(e,t){switch(e){case"compositionend":return Yc(t);case"keypress":return t.which!==32?null:(la=!0,oa);case"textInput":return e=t.data,e===oa&&la?null:e;default:return null}}function cf(e,t){if(fr)return e==="compositionend"||!nl&&Gc(e,t)?(e=qc(),ns=rl=Pt=null,fr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Xc&&t.locale!=="ko"?null:t.data;default:return null}}var hf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function aa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!hf[e.type]:t==="textarea"}function Qc(e,t,r,i){Rc(i),t=xs(t,"onChange"),0<t.length&&(r=new il("onChange","change",null,r,i),e.push({event:r,listeners:t}))}var li=null,Si=null;function uf(e){ah(e,0)}function Ks(e){var t=vr(e);if(wc(t))return e}function df(e,t){if(e==="change")return t}var Jc=!1;if(Ct){var wn;if(Ct){var Cn="oninput"in document;if(!Cn){var ca=document.createElement("div");ca.setAttribute("oninput","return;"),Cn=typeof ca.oninput=="function"}wn=Cn}else wn=!1;Jc=wn&&(!document.documentMode||9<document.documentMode)}function ha(){li&&(li.detachEvent("onpropertychange",Zc),Si=li=null)}function Zc(e){if(e.propertyName==="value"&&Ks(Si)){var t=[];Qc(t,Si,e,Qo(e)),Bc(uf,t)}}function ff(e,t,r){e==="focusin"?(ha(),li=t,Si=r,li.attachEvent("onpropertychange",Zc)):e==="focusout"&&ha()}function _f(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ks(Si)}function pf(e,t){if(e==="click")return Ks(t)}function vf(e,t){if(e==="input"||e==="change")return Ks(t)}function gf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var at=typeof Object.is=="function"?Object.is:gf;function yi(e,t){if(at(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),i=Object.keys(t);if(r.length!==i.length)return!1;for(i=0;i<r.length;i++){var l=r[i];if(!Wn.call(t,l)||!at(e[l],t[l]))return!1}return!0}function ua(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function da(e,t){var r=ua(e);e=0;for(var i;r;){if(r.nodeType===3){if(i=e+r.textContent.length,e<=t&&i>=t)return{node:r,offset:t-e};e=i}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ua(r)}}function eh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?eh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function th(){for(var e=window,t=ms();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=ms(e.document)}return t}function ol(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function mf(e){var t=th(),r=e.focusedElem,i=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&eh(r.ownerDocument.documentElement,r)){if(i!==null&&ol(r)){if(t=i.start,e=i.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=r.textContent.length,d=Math.min(i.start,l);i=i.end===void 0?d:Math.min(i.end,l),!e.extend&&d>i&&(l=i,i=d,d=l),l=da(r,d);var m=da(r,i);l&&m&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==m.node||e.focusOffset!==m.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),d>i?(e.addRange(t),e.extend(m.node,m.offset)):(t.setEnd(m.node,m.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Sf=Ct&&"documentMode"in document&&11>=document.documentMode,_r=null,lo=null,ai=null,ao=!1;function fa(e,t,r){var i=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;ao||_r==null||_r!==ms(i)||(i=_r,"selectionStart"in i&&ol(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),ai&&yi(ai,i)||(ai=i,i=xs(lo,"onSelect"),0<i.length&&(t=new il("onSelect","select",null,t,r),e.push({event:t,listeners:i}),t.target=_r)))}function Vi(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var pr={animationend:Vi("Animation","AnimationEnd"),animationiteration:Vi("Animation","AnimationIteration"),animationstart:Vi("Animation","AnimationStart"),transitionend:Vi("Transition","TransitionEnd")},En={},rh={};Ct&&(rh=document.createElement("div").style,"AnimationEvent"in window||(delete pr.animationend.animation,delete pr.animationiteration.animation,delete pr.animationstart.animation),"TransitionEvent"in window||delete pr.transitionend.transition);function qs(e){if(En[e])return En[e];if(!pr[e])return e;var t=pr[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in rh)return En[e]=t[r];return e}var ih=qs("animationend"),sh=qs("animationiteration"),nh=qs("animationstart"),oh=qs("transitionend"),lh=new Map,_a="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function qt(e,t){lh.set(e,t),ar(t,[e])}for(var kn=0;kn<_a.length;kn++){var xn=_a[kn],yf=xn.toLowerCase(),wf=xn[0].toUpperCase()+xn.slice(1);qt(yf,"on"+wf)}qt(ih,"onAnimationEnd");qt(sh,"onAnimationIteration");qt(nh,"onAnimationStart");qt("dblclick","onDoubleClick");qt("focusin","onFocus");qt("focusout","onBlur");qt(oh,"onTransitionEnd");Dr("onMouseEnter",["mouseout","mouseover"]);Dr("onMouseLeave",["mouseout","mouseover"]);Dr("onPointerEnter",["pointerout","pointerover"]);Dr("onPointerLeave",["pointerout","pointerover"]);ar("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));ar("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));ar("onBeforeInput",["compositionend","keypress","textInput","paste"]);ar("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));ar("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));ar("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ri="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Cf=new Set("cancel close invalid load scroll toggle".split(" ").concat(ri));function pa(e,t,r){var i=e.type||"unknown-event";e.currentTarget=r,Sd(i,t,void 0,e),e.currentTarget=null}function ah(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var i=e[r],l=i.event;i=i.listeners;e:{var d=void 0;if(t)for(var m=i.length-1;0<=m;m--){var n=i[m],c=n.instance,f=n.currentTarget;if(n=n.listener,c!==d&&l.isPropagationStopped())break e;pa(l,n,f),d=c}else for(m=0;m<i.length;m++){if(n=i[m],c=n.instance,f=n.currentTarget,n=n.listener,c!==d&&l.isPropagationStopped())break e;pa(l,n,f),d=c}}}if(ys)throw e=io,ys=!1,io=null,e}function ne(e,t){var r=t[_o];r===void 0&&(r=t[_o]=new Set);var i=e+"__bubble";r.has(i)||(ch(t,e,2,!1),r.add(i))}function bn(e,t,r){var i=0;t&&(i|=4),ch(r,e,i,t)}var Ki="_reactListening"+Math.random().toString(36).slice(2);function wi(e){if(!e[Ki]){e[Ki]=!0,vc.forEach(function(r){r!=="selectionchange"&&(Cf.has(r)||bn(r,!1,e),bn(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ki]||(t[Ki]=!0,bn("selectionchange",!1,t))}}function ch(e,t,r,i){switch(Kc(t)){case 1:var l=Md;break;case 4:l=Id;break;default:l=tl}r=l.bind(null,t,r,e),l=void 0,!ro||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),i?l!==void 0?e.addEventListener(t,r,{capture:!0,passive:l}):e.addEventListener(t,r,!0):l!==void 0?e.addEventListener(t,r,{passive:l}):e.addEventListener(t,r,!1)}function Ln(e,t,r,i,l){var d=i;if(!(t&1)&&!(t&2)&&i!==null)e:for(;;){if(i===null)return;var m=i.tag;if(m===3||m===4){var n=i.stateNode.containerInfo;if(n===l||n.nodeType===8&&n.parentNode===l)break;if(m===4)for(m=i.return;m!==null;){var c=m.tag;if((c===3||c===4)&&(c=m.stateNode.containerInfo,c===l||c.nodeType===8&&c.parentNode===l))return;m=m.return}for(;n!==null;){if(m=Jt(n),m===null)return;if(c=m.tag,c===5||c===6){i=d=m;continue e}n=n.parentNode}}i=i.return}Bc(function(){var f=d,S=Qo(r),u=[];e:{var _=lh.get(e);if(_!==void 0){var g=il,C=e;switch(e){case"keypress":if(os(r)===0)break e;case"keydown":case"keyup":g=Qd;break;case"focusin":C="focus",g=yn;break;case"focusout":C="blur",g=yn;break;case"beforeblur":case"afterblur":g=yn;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=ia;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Fd;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=ef;break;case ih:case sh:case nh:g=Wd;break;case oh:g=rf;break;case"scroll":g=Nd;break;case"wheel":g=nf;break;case"copy":case"cut":case"paste":g=$d;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=na}var v=(t&4)!==0,h=!v&&e==="scroll",a=v?_!==null?_+"Capture":null:_;v=[];for(var s=f,o;s!==null;){o=s;var p=o.stateNode;if(o.tag===5&&p!==null&&(o=p,a!==null&&(p=pi(s,a),p!=null&&v.push(Ci(s,p,o)))),h)break;s=s.return}0<v.length&&(_=new g(_,C,null,r,S),u.push({event:_,listeners:v}))}}if(!(t&7)){e:{if(_=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",_&&r!==eo&&(C=r.relatedTarget||r.fromElement)&&(Jt(C)||C[Et]))break e;if((g||_)&&(_=S.window===S?S:(_=S.ownerDocument)?_.defaultView||_.parentWindow:window,g?(C=r.relatedTarget||r.toElement,g=f,C=C?Jt(C):null,C!==null&&(h=cr(C),C!==h||C.tag!==5&&C.tag!==6)&&(C=null)):(g=null,C=f),g!==C)){if(v=ia,p="onMouseLeave",a="onMouseEnter",s="mouse",(e==="pointerout"||e==="pointerover")&&(v=na,p="onPointerLeave",a="onPointerEnter",s="pointer"),h=g==null?_:vr(g),o=C==null?_:vr(C),_=new v(p,s+"leave",g,r,S),_.target=h,_.relatedTarget=o,p=null,Jt(S)===f&&(v=new v(a,s+"enter",C,r,S),v.target=o,v.relatedTarget=h,p=v),h=p,g&&C)t:{for(v=g,a=C,s=0,o=v;o;o=hr(o))s++;for(o=0,p=a;p;p=hr(p))o++;for(;0<s-o;)v=hr(v),s--;for(;0<o-s;)a=hr(a),o--;for(;s--;){if(v===a||a!==null&&v===a.alternate)break t;v=hr(v),a=hr(a)}v=null}else v=null;g!==null&&va(u,_,g,v,!1),C!==null&&h!==null&&va(u,h,C,v,!0)}}e:{if(_=f?vr(f):window,g=_.nodeName&&_.nodeName.toLowerCase(),g==="select"||g==="input"&&_.type==="file")var w=df;else if(aa(_))if(Jc)w=vf;else{w=_f;var E=ff}else(g=_.nodeName)&&g.toLowerCase()==="input"&&(_.type==="checkbox"||_.type==="radio")&&(w=pf);if(w&&(w=w(e,f))){Qc(u,w,r,S);break e}E&&E(e,_,f),e==="focusout"&&(E=_._wrapperState)&&E.controlled&&_.type==="number"&&Gn(_,"number",_.value)}switch(E=f?vr(f):window,e){case"focusin":(aa(E)||E.contentEditable==="true")&&(_r=E,lo=f,ai=null);break;case"focusout":ai=lo=_r=null;break;case"mousedown":ao=!0;break;case"contextmenu":case"mouseup":case"dragend":ao=!1,fa(u,r,S);break;case"selectionchange":if(Sf)break;case"keydown":case"keyup":fa(u,r,S)}var k;if(nl)e:{switch(e){case"compositionstart":var y="onCompositionStart";break e;case"compositionend":y="onCompositionEnd";break e;case"compositionupdate":y="onCompositionUpdate";break e}y=void 0}else fr?Gc(e,r)&&(y="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(y="onCompositionStart");y&&(Xc&&r.locale!=="ko"&&(fr||y!=="onCompositionStart"?y==="onCompositionEnd"&&fr&&(k=qc()):(Pt=S,rl="value"in Pt?Pt.value:Pt.textContent,fr=!0)),E=xs(f,y),0<E.length&&(y=new sa(y,e,null,r,S),u.push({event:y,listeners:E}),k?y.data=k:(k=Yc(r),k!==null&&(y.data=k)))),(k=lf?af(e,r):cf(e,r))&&(f=xs(f,"onBeforeInput"),0<f.length&&(S=new sa("onBeforeInput","beforeinput",null,r,S),u.push({event:S,listeners:f}),S.data=k))}ah(u,t)})}function Ci(e,t,r){return{instance:e,listener:t,currentTarget:r}}function xs(e,t){for(var r=t+"Capture",i=[];e!==null;){var l=e,d=l.stateNode;l.tag===5&&d!==null&&(l=d,d=pi(e,r),d!=null&&i.unshift(Ci(e,d,l)),d=pi(e,t),d!=null&&i.push(Ci(e,d,l))),e=e.return}return i}function hr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function va(e,t,r,i,l){for(var d=t._reactName,m=[];r!==null&&r!==i;){var n=r,c=n.alternate,f=n.stateNode;if(c!==null&&c===i)break;n.tag===5&&f!==null&&(n=f,l?(c=pi(r,d),c!=null&&m.unshift(Ci(r,c,n))):l||(c=pi(r,d),c!=null&&m.push(Ci(r,c,n)))),r=r.return}m.length!==0&&e.push({event:t,listeners:m})}var Ef=/\r\n?/g,kf=/\u0000|\uFFFD/g;function ga(e){return(typeof e=="string"?e:""+e).replace(Ef,`
`).replace(kf,"")}function qi(e,t,r){if(t=ga(t),ga(e)!==t&&r)throw Error(U(425))}function bs(){}var co=null,ho=null;function uo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var fo=typeof setTimeout=="function"?setTimeout:void 0,xf=typeof clearTimeout=="function"?clearTimeout:void 0,ma=typeof Promise=="function"?Promise:void 0,bf=typeof queueMicrotask=="function"?queueMicrotask:typeof ma<"u"?function(e){return ma.resolve(null).then(e).catch(Lf)}:fo;function Lf(e){setTimeout(function(){throw e})}function Rn(e,t){var r=t,i=0;do{var l=r.nextSibling;if(e.removeChild(r),l&&l.nodeType===8)if(r=l.data,r==="/$"){if(i===0){e.removeChild(l),mi(t);return}i--}else r!=="$"&&r!=="$?"&&r!=="$!"||i++;r=l}while(r);mi(t)}function Ft(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Sa(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var Hr=Math.random().toString(36).slice(2),dt="__reactFiber$"+Hr,Ei="__reactProps$"+Hr,Et="__reactContainer$"+Hr,_o="__reactEvents$"+Hr,Rf="__reactListeners$"+Hr,Df="__reactHandles$"+Hr;function Jt(e){var t=e[dt];if(t)return t;for(var r=e.parentNode;r;){if(t=r[Et]||r[dt]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=Sa(e);e!==null;){if(r=e[dt])return r;e=Sa(e)}return t}e=r,r=e.parentNode}return null}function Oi(e){return e=e[dt]||e[Et],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function vr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(U(33))}function Xs(e){return e[Ei]||null}var po=[],gr=-1;function Xt(e){return{current:e}}function oe(e){0>gr||(e.current=po[gr],po[gr]=null,gr--)}function se(e,t){gr++,po[gr]=e.current,e.current=t}var Kt={},De=Xt(Kt),He=Xt(!1),ir=Kt;function Tr(e,t){var r=e.type.contextTypes;if(!r)return Kt;var i=e.stateNode;if(i&&i.__reactInternalMemoizedUnmaskedChildContext===t)return i.__reactInternalMemoizedMaskedChildContext;var l={},d;for(d in r)l[d]=t[d];return i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Fe(e){return e=e.childContextTypes,e!=null}function Ls(){oe(He),oe(De)}function ya(e,t,r){if(De.current!==Kt)throw Error(U(168));se(De,t),se(He,r)}function hh(e,t,r){var i=e.stateNode;if(t=t.childContextTypes,typeof i.getChildContext!="function")return r;i=i.getChildContext();for(var l in i)if(!(l in t))throw Error(U(108,dd(e)||"Unknown",l));return ue({},r,i)}function Rs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Kt,ir=De.current,se(De,e),se(He,He.current),!0}function wa(e,t,r){var i=e.stateNode;if(!i)throw Error(U(169));r?(e=hh(e,t,ir),i.__reactInternalMemoizedMergedChildContext=e,oe(He),oe(De),se(De,e)):oe(He),se(He,r)}var mt=null,Gs=!1,Dn=!1;function uh(e){mt===null?mt=[e]:mt.push(e)}function Tf(e){Gs=!0,uh(e)}function Gt(){if(!Dn&&mt!==null){Dn=!0;var e=0,t=ie;try{var r=mt;for(ie=1;e<r.length;e++){var i=r[e];do i=i(!0);while(i!==null)}mt=null,Gs=!1}catch(l){throw mt!==null&&(mt=mt.slice(e+1)),Ic(Jo,Gt),l}finally{ie=t,Dn=!1}}return null}var mr=[],Sr=0,Ds=null,Ts=0,Xe=[],Ge=0,sr=null,St=1,yt="";function Yt(e,t){mr[Sr++]=Ts,mr[Sr++]=Ds,Ds=e,Ts=t}function dh(e,t,r){Xe[Ge++]=St,Xe[Ge++]=yt,Xe[Ge++]=sr,sr=e;var i=St;e=yt;var l=32-ot(i)-1;i&=~(1<<l),r+=1;var d=32-ot(t)+l;if(30<d){var m=l-l%5;d=(i&(1<<m)-1).toString(32),i>>=m,l-=m,St=1<<32-ot(t)+l|r<<l|i,yt=d+e}else St=1<<d|r<<l|i,yt=e}function ll(e){e.return!==null&&(Yt(e,1),dh(e,1,0))}function al(e){for(;e===Ds;)Ds=mr[--Sr],mr[Sr]=null,Ts=mr[--Sr],mr[Sr]=null;for(;e===sr;)sr=Xe[--Ge],Xe[Ge]=null,yt=Xe[--Ge],Xe[Ge]=null,St=Xe[--Ge],Xe[Ge]=null}var Ue=null,We=null,le=!1,nt=null;function fh(e,t){var r=Qe(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function Ca(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ue=e,We=Ft(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ue=e,We=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=sr!==null?{id:St,overflow:yt}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=Qe(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,Ue=e,We=null,!0):!1;default:return!1}}function vo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function go(e){if(le){var t=We;if(t){var r=t;if(!Ca(e,t)){if(vo(e))throw Error(U(418));t=Ft(r.nextSibling);var i=Ue;t&&Ca(e,t)?fh(i,r):(e.flags=e.flags&-4097|2,le=!1,Ue=e)}}else{if(vo(e))throw Error(U(418));e.flags=e.flags&-4097|2,le=!1,Ue=e}}}function Ea(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ue=e}function Xi(e){if(e!==Ue)return!1;if(!le)return Ea(e),le=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!uo(e.type,e.memoizedProps)),t&&(t=We)){if(vo(e))throw _h(),Error(U(418));for(;t;)fh(e,t),t=Ft(t.nextSibling)}if(Ea(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(U(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){We=Ft(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}We=null}}else We=Ue?Ft(e.stateNode.nextSibling):null;return!0}function _h(){for(var e=We;e;)e=Ft(e.nextSibling)}function Ar(){We=Ue=null,le=!1}function cl(e){nt===null?nt=[e]:nt.push(e)}var Af=bt.ReactCurrentBatchConfig;function Gr(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(U(309));var i=r.stateNode}if(!i)throw Error(U(147,e));var l=i,d=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===d?t.ref:(t=function(m){var n=l.refs;m===null?delete n[d]:n[d]=m},t._stringRef=d,t)}if(typeof e!="string")throw Error(U(284));if(!r._owner)throw Error(U(290,e))}return e}function Gi(e,t){throw e=Object.prototype.toString.call(t),Error(U(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ka(e){var t=e._init;return t(e._payload)}function ph(e){function t(a,s){if(e){var o=a.deletions;o===null?(a.deletions=[s],a.flags|=16):o.push(s)}}function r(a,s){if(!e)return null;for(;s!==null;)t(a,s),s=s.sibling;return null}function i(a,s){for(a=new Map;s!==null;)s.key!==null?a.set(s.key,s):a.set(s.index,s),s=s.sibling;return a}function l(a,s){return a=Ut(a,s),a.index=0,a.sibling=null,a}function d(a,s,o){return a.index=o,e?(o=a.alternate,o!==null?(o=o.index,o<s?(a.flags|=2,s):o):(a.flags|=2,s)):(a.flags|=1048576,s)}function m(a){return e&&a.alternate===null&&(a.flags|=2),a}function n(a,s,o,p){return s===null||s.tag!==6?(s=In(o,a.mode,p),s.return=a,s):(s=l(s,o),s.return=a,s)}function c(a,s,o,p){var w=o.type;return w===dr?S(a,s,o.props.children,p,o.key):s!==null&&(s.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===Tt&&ka(w)===s.type)?(p=l(s,o.props),p.ref=Gr(a,s,o),p.return=a,p):(p=fs(o.type,o.key,o.props,null,a.mode,p),p.ref=Gr(a,s,o),p.return=a,p)}function f(a,s,o,p){return s===null||s.tag!==4||s.stateNode.containerInfo!==o.containerInfo||s.stateNode.implementation!==o.implementation?(s=Nn(o,a.mode,p),s.return=a,s):(s=l(s,o.children||[]),s.return=a,s)}function S(a,s,o,p,w){return s===null||s.tag!==7?(s=rr(o,a.mode,p,w),s.return=a,s):(s=l(s,o),s.return=a,s)}function u(a,s,o){if(typeof s=="string"&&s!==""||typeof s=="number")return s=In(""+s,a.mode,o),s.return=a,s;if(typeof s=="object"&&s!==null){switch(s.$$typeof){case Hi:return o=fs(s.type,s.key,s.props,null,a.mode,o),o.ref=Gr(a,null,s),o.return=a,o;case ur:return s=Nn(s,a.mode,o),s.return=a,s;case Tt:var p=s._init;return u(a,p(s._payload),o)}if(ei(s)||$r(s))return s=rr(s,a.mode,o,null),s.return=a,s;Gi(a,s)}return null}function _(a,s,o,p){var w=s!==null?s.key:null;if(typeof o=="string"&&o!==""||typeof o=="number")return w!==null?null:n(a,s,""+o,p);if(typeof o=="object"&&o!==null){switch(o.$$typeof){case Hi:return o.key===w?c(a,s,o,p):null;case ur:return o.key===w?f(a,s,o,p):null;case Tt:return w=o._init,_(a,s,w(o._payload),p)}if(ei(o)||$r(o))return w!==null?null:S(a,s,o,p,null);Gi(a,o)}return null}function g(a,s,o,p,w){if(typeof p=="string"&&p!==""||typeof p=="number")return a=a.get(o)||null,n(s,a,""+p,w);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Hi:return a=a.get(p.key===null?o:p.key)||null,c(s,a,p,w);case ur:return a=a.get(p.key===null?o:p.key)||null,f(s,a,p,w);case Tt:var E=p._init;return g(a,s,o,E(p._payload),w)}if(ei(p)||$r(p))return a=a.get(o)||null,S(s,a,p,w,null);Gi(s,p)}return null}function C(a,s,o,p){for(var w=null,E=null,k=s,y=s=0,b=null;k!==null&&y<o.length;y++){k.index>y?(b=k,k=null):b=k.sibling;var D=_(a,k,o[y],p);if(D===null){k===null&&(k=b);break}e&&k&&D.alternate===null&&t(a,k),s=d(D,s,y),E===null?w=D:E.sibling=D,E=D,k=b}if(y===o.length)return r(a,k),le&&Yt(a,y),w;if(k===null){for(;y<o.length;y++)k=u(a,o[y],p),k!==null&&(s=d(k,s,y),E===null?w=k:E.sibling=k,E=k);return le&&Yt(a,y),w}for(k=i(a,k);y<o.length;y++)b=g(k,a,y,o[y],p),b!==null&&(e&&b.alternate!==null&&k.delete(b.key===null?y:b.key),s=d(b,s,y),E===null?w=b:E.sibling=b,E=b);return e&&k.forEach(function(B){return t(a,B)}),le&&Yt(a,y),w}function v(a,s,o,p){var w=$r(o);if(typeof w!="function")throw Error(U(150));if(o=w.call(o),o==null)throw Error(U(151));for(var E=w=null,k=s,y=s=0,b=null,D=o.next();k!==null&&!D.done;y++,D=o.next()){k.index>y?(b=k,k=null):b=k.sibling;var B=_(a,k,D.value,p);if(B===null){k===null&&(k=b);break}e&&k&&B.alternate===null&&t(a,k),s=d(B,s,y),E===null?w=B:E.sibling=B,E=B,k=b}if(D.done)return r(a,k),le&&Yt(a,y),w;if(k===null){for(;!D.done;y++,D=o.next())D=u(a,D.value,p),D!==null&&(s=d(D,s,y),E===null?w=D:E.sibling=D,E=D);return le&&Yt(a,y),w}for(k=i(a,k);!D.done;y++,D=o.next())D=g(k,a,y,D.value,p),D!==null&&(e&&D.alternate!==null&&k.delete(D.key===null?y:D.key),s=d(D,s,y),E===null?w=D:E.sibling=D,E=D);return e&&k.forEach(function(O){return t(a,O)}),le&&Yt(a,y),w}function h(a,s,o,p){if(typeof o=="object"&&o!==null&&o.type===dr&&o.key===null&&(o=o.props.children),typeof o=="object"&&o!==null){switch(o.$$typeof){case Hi:e:{for(var w=o.key,E=s;E!==null;){if(E.key===w){if(w=o.type,w===dr){if(E.tag===7){r(a,E.sibling),s=l(E,o.props.children),s.return=a,a=s;break e}}else if(E.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===Tt&&ka(w)===E.type){r(a,E.sibling),s=l(E,o.props),s.ref=Gr(a,E,o),s.return=a,a=s;break e}r(a,E);break}else t(a,E);E=E.sibling}o.type===dr?(s=rr(o.props.children,a.mode,p,o.key),s.return=a,a=s):(p=fs(o.type,o.key,o.props,null,a.mode,p),p.ref=Gr(a,s,o),p.return=a,a=p)}return m(a);case ur:e:{for(E=o.key;s!==null;){if(s.key===E)if(s.tag===4&&s.stateNode.containerInfo===o.containerInfo&&s.stateNode.implementation===o.implementation){r(a,s.sibling),s=l(s,o.children||[]),s.return=a,a=s;break e}else{r(a,s);break}else t(a,s);s=s.sibling}s=Nn(o,a.mode,p),s.return=a,a=s}return m(a);case Tt:return E=o._init,h(a,s,E(o._payload),p)}if(ei(o))return C(a,s,o,p);if($r(o))return v(a,s,o,p);Gi(a,o)}return typeof o=="string"&&o!==""||typeof o=="number"?(o=""+o,s!==null&&s.tag===6?(r(a,s.sibling),s=l(s,o),s.return=a,a=s):(r(a,s),s=In(o,a.mode,p),s.return=a,a=s),m(a)):r(a,s)}return h}var Br=ph(!0),vh=ph(!1),As=Xt(null),Bs=null,yr=null,hl=null;function ul(){hl=yr=Bs=null}function dl(e){var t=As.current;oe(As),e._currentValue=t}function mo(e,t,r){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===r)break;e=e.return}}function Lr(e,t){Bs=e,hl=yr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ne=!0),e.firstContext=null)}function Ze(e){var t=e._currentValue;if(hl!==e)if(e={context:e,memoizedValue:t,next:null},yr===null){if(Bs===null)throw Error(U(308));yr=e,Bs.dependencies={lanes:0,firstContext:e}}else yr=yr.next=e;return t}var Zt=null;function fl(e){Zt===null?Zt=[e]:Zt.push(e)}function gh(e,t,r,i){var l=t.interleaved;return l===null?(r.next=r,fl(t)):(r.next=l.next,l.next=r),t.interleaved=r,kt(e,i)}function kt(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var At=!1;function _l(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function mh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function wt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function jt(e,t,r){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,re&2){var l=i.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),i.pending=t,kt(e,r)}return l=i.interleaved,l===null?(t.next=t,fl(i)):(t.next=l.next,l.next=t),i.interleaved=t,kt(e,r)}function ls(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var i=t.lanes;i&=e.pendingLanes,r|=i,t.lanes=r,Zo(e,r)}}function xa(e,t){var r=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,r===i)){var l=null,d=null;if(r=r.firstBaseUpdate,r!==null){do{var m={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};d===null?l=d=m:d=d.next=m,r=r.next}while(r!==null);d===null?l=d=t:d=d.next=t}else l=d=t;r={baseState:i.baseState,firstBaseUpdate:l,lastBaseUpdate:d,shared:i.shared,effects:i.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function Os(e,t,r,i){var l=e.updateQueue;At=!1;var d=l.firstBaseUpdate,m=l.lastBaseUpdate,n=l.shared.pending;if(n!==null){l.shared.pending=null;var c=n,f=c.next;c.next=null,m===null?d=f:m.next=f,m=c;var S=e.alternate;S!==null&&(S=S.updateQueue,n=S.lastBaseUpdate,n!==m&&(n===null?S.firstBaseUpdate=f:n.next=f,S.lastBaseUpdate=c))}if(d!==null){var u=l.baseState;m=0,S=f=c=null,n=d;do{var _=n.lane,g=n.eventTime;if((i&_)===_){S!==null&&(S=S.next={eventTime:g,lane:0,tag:n.tag,payload:n.payload,callback:n.callback,next:null});e:{var C=e,v=n;switch(_=t,g=r,v.tag){case 1:if(C=v.payload,typeof C=="function"){u=C.call(g,u,_);break e}u=C;break e;case 3:C.flags=C.flags&-65537|128;case 0:if(C=v.payload,_=typeof C=="function"?C.call(g,u,_):C,_==null)break e;u=ue({},u,_);break e;case 2:At=!0}}n.callback!==null&&n.lane!==0&&(e.flags|=64,_=l.effects,_===null?l.effects=[n]:_.push(n))}else g={eventTime:g,lane:_,tag:n.tag,payload:n.payload,callback:n.callback,next:null},S===null?(f=S=g,c=u):S=S.next=g,m|=_;if(n=n.next,n===null){if(n=l.shared.pending,n===null)break;_=n,n=_.next,_.next=null,l.lastBaseUpdate=_,l.shared.pending=null}}while(!0);if(S===null&&(c=u),l.baseState=c,l.firstBaseUpdate=f,l.lastBaseUpdate=S,t=l.shared.interleaved,t!==null){l=t;do m|=l.lane,l=l.next;while(l!==t)}else d===null&&(l.shared.lanes=0);or|=m,e.lanes=m,e.memoizedState=u}}function ba(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var i=e[t],l=i.callback;if(l!==null){if(i.callback=null,i=r,typeof l!="function")throw Error(U(191,l));l.call(i)}}}var Pi={},_t=Xt(Pi),ki=Xt(Pi),xi=Xt(Pi);function er(e){if(e===Pi)throw Error(U(174));return e}function pl(e,t){switch(se(xi,t),se(ki,e),se(_t,Pi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Qn(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Qn(t,e)}oe(_t),se(_t,t)}function Or(){oe(_t),oe(ki),oe(xi)}function Sh(e){er(xi.current);var t=er(_t.current),r=Qn(t,e.type);t!==r&&(se(ki,e),se(_t,r))}function vl(e){ki.current===e&&(oe(_t),oe(ki))}var ce=Xt(0);function Ps(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Tn=[];function gl(){for(var e=0;e<Tn.length;e++)Tn[e]._workInProgressVersionPrimary=null;Tn.length=0}var as=bt.ReactCurrentDispatcher,An=bt.ReactCurrentBatchConfig,nr=0,he=null,Se=null,we=null,Ms=!1,ci=!1,bi=0,Bf=0;function be(){throw Error(U(321))}function ml(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!at(e[r],t[r]))return!1;return!0}function Sl(e,t,r,i,l,d){if(nr=d,he=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,as.current=e===null||e.memoizedState===null?If:Nf,e=r(i,l),ci){d=0;do{if(ci=!1,bi=0,25<=d)throw Error(U(301));d+=1,we=Se=null,t.updateQueue=null,as.current=Hf,e=r(i,l)}while(ci)}if(as.current=Is,t=Se!==null&&Se.next!==null,nr=0,we=Se=he=null,Ms=!1,t)throw Error(U(300));return e}function yl(){var e=bi!==0;return bi=0,e}function ut(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return we===null?he.memoizedState=we=e:we=we.next=e,we}function et(){if(Se===null){var e=he.alternate;e=e!==null?e.memoizedState:null}else e=Se.next;var t=we===null?he.memoizedState:we.next;if(t!==null)we=t,Se=e;else{if(e===null)throw Error(U(310));Se=e,e={memoizedState:Se.memoizedState,baseState:Se.baseState,baseQueue:Se.baseQueue,queue:Se.queue,next:null},we===null?he.memoizedState=we=e:we=we.next=e}return we}function Li(e,t){return typeof t=="function"?t(e):t}function Bn(e){var t=et(),r=t.queue;if(r===null)throw Error(U(311));r.lastRenderedReducer=e;var i=Se,l=i.baseQueue,d=r.pending;if(d!==null){if(l!==null){var m=l.next;l.next=d.next,d.next=m}i.baseQueue=l=d,r.pending=null}if(l!==null){d=l.next,i=i.baseState;var n=m=null,c=null,f=d;do{var S=f.lane;if((nr&S)===S)c!==null&&(c=c.next={lane:0,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null}),i=f.hasEagerState?f.eagerState:e(i,f.action);else{var u={lane:S,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null};c===null?(n=c=u,m=i):c=c.next=u,he.lanes|=S,or|=S}f=f.next}while(f!==null&&f!==d);c===null?m=i:c.next=n,at(i,t.memoizedState)||(Ne=!0),t.memoizedState=i,t.baseState=m,t.baseQueue=c,r.lastRenderedState=i}if(e=r.interleaved,e!==null){l=e;do d=l.lane,he.lanes|=d,or|=d,l=l.next;while(l!==e)}else l===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function On(e){var t=et(),r=t.queue;if(r===null)throw Error(U(311));r.lastRenderedReducer=e;var i=r.dispatch,l=r.pending,d=t.memoizedState;if(l!==null){r.pending=null;var m=l=l.next;do d=e(d,m.action),m=m.next;while(m!==l);at(d,t.memoizedState)||(Ne=!0),t.memoizedState=d,t.baseQueue===null&&(t.baseState=d),r.lastRenderedState=d}return[d,i]}function yh(){}function wh(e,t){var r=he,i=et(),l=t(),d=!at(i.memoizedState,l);if(d&&(i.memoizedState=l,Ne=!0),i=i.queue,wl(kh.bind(null,r,i,e),[e]),i.getSnapshot!==t||d||we!==null&&we.memoizedState.tag&1){if(r.flags|=2048,Ri(9,Eh.bind(null,r,i,l,t),void 0,null),Ce===null)throw Error(U(349));nr&30||Ch(r,t,l)}return l}function Ch(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=he.updateQueue,t===null?(t={lastEffect:null,stores:null},he.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function Eh(e,t,r,i){t.value=r,t.getSnapshot=i,xh(t)&&bh(e)}function kh(e,t,r){return r(function(){xh(t)&&bh(e)})}function xh(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!at(e,r)}catch{return!0}}function bh(e){var t=kt(e,1);t!==null&&lt(t,e,1,-1)}function La(e){var t=ut();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Li,lastRenderedState:e},t.queue=e,e=e.dispatch=Mf.bind(null,he,e),[t.memoizedState,e]}function Ri(e,t,r,i){return e={tag:e,create:t,destroy:r,deps:i,next:null},t=he.updateQueue,t===null?(t={lastEffect:null,stores:null},he.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(i=r.next,r.next=e,e.next=i,t.lastEffect=e)),e}function Lh(){return et().memoizedState}function cs(e,t,r,i){var l=ut();he.flags|=e,l.memoizedState=Ri(1|t,r,void 0,i===void 0?null:i)}function Ys(e,t,r,i){var l=et();i=i===void 0?null:i;var d=void 0;if(Se!==null){var m=Se.memoizedState;if(d=m.destroy,i!==null&&ml(i,m.deps)){l.memoizedState=Ri(t,r,d,i);return}}he.flags|=e,l.memoizedState=Ri(1|t,r,d,i)}function Ra(e,t){return cs(8390656,8,e,t)}function wl(e,t){return Ys(2048,8,e,t)}function Rh(e,t){return Ys(4,2,e,t)}function Dh(e,t){return Ys(4,4,e,t)}function Th(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ah(e,t,r){return r=r!=null?r.concat([e]):null,Ys(4,4,Th.bind(null,t,e),r)}function Cl(){}function Bh(e,t){var r=et();t=t===void 0?null:t;var i=r.memoizedState;return i!==null&&t!==null&&ml(t,i[1])?i[0]:(r.memoizedState=[e,t],e)}function Oh(e,t){var r=et();t=t===void 0?null:t;var i=r.memoizedState;return i!==null&&t!==null&&ml(t,i[1])?i[0]:(e=e(),r.memoizedState=[e,t],e)}function Ph(e,t,r){return nr&21?(at(r,t)||(r=Fc(),he.lanes|=r,or|=r,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ne=!0),e.memoizedState=r)}function Of(e,t){var r=ie;ie=r!==0&&4>r?r:4,e(!0);var i=An.transition;An.transition={};try{e(!1),t()}finally{ie=r,An.transition=i}}function Mh(){return et().memoizedState}function Pf(e,t,r){var i=Wt(e);if(r={lane:i,action:r,hasEagerState:!1,eagerState:null,next:null},Ih(e))Nh(t,r);else if(r=gh(e,t,r,i),r!==null){var l=Ae();lt(r,e,i,l),Hh(r,t,i)}}function Mf(e,t,r){var i=Wt(e),l={lane:i,action:r,hasEagerState:!1,eagerState:null,next:null};if(Ih(e))Nh(t,l);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=t.lastRenderedReducer,d!==null))try{var m=t.lastRenderedState,n=d(m,r);if(l.hasEagerState=!0,l.eagerState=n,at(n,m)){var c=t.interleaved;c===null?(l.next=l,fl(t)):(l.next=c.next,c.next=l),t.interleaved=l;return}}catch{}finally{}r=gh(e,t,l,i),r!==null&&(l=Ae(),lt(r,e,i,l),Hh(r,t,i))}}function Ih(e){var t=e.alternate;return e===he||t!==null&&t===he}function Nh(e,t){ci=Ms=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Hh(e,t,r){if(r&4194240){var i=t.lanes;i&=e.pendingLanes,r|=i,t.lanes=r,Zo(e,r)}}var Is={readContext:Ze,useCallback:be,useContext:be,useEffect:be,useImperativeHandle:be,useInsertionEffect:be,useLayoutEffect:be,useMemo:be,useReducer:be,useRef:be,useState:be,useDebugValue:be,useDeferredValue:be,useTransition:be,useMutableSource:be,useSyncExternalStore:be,useId:be,unstable_isNewReconciler:!1},If={readContext:Ze,useCallback:function(e,t){return ut().memoizedState=[e,t===void 0?null:t],e},useContext:Ze,useEffect:Ra,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,cs(4194308,4,Th.bind(null,t,e),r)},useLayoutEffect:function(e,t){return cs(4194308,4,e,t)},useInsertionEffect:function(e,t){return cs(4,2,e,t)},useMemo:function(e,t){var r=ut();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var i=ut();return t=r!==void 0?r(t):t,i.memoizedState=i.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},i.queue=e,e=e.dispatch=Pf.bind(null,he,e),[i.memoizedState,e]},useRef:function(e){var t=ut();return e={current:e},t.memoizedState=e},useState:La,useDebugValue:Cl,useDeferredValue:function(e){return ut().memoizedState=e},useTransition:function(){var e=La(!1),t=e[0];return e=Of.bind(null,e[1]),ut().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var i=he,l=ut();if(le){if(r===void 0)throw Error(U(407));r=r()}else{if(r=t(),Ce===null)throw Error(U(349));nr&30||Ch(i,t,r)}l.memoizedState=r;var d={value:r,getSnapshot:t};return l.queue=d,Ra(kh.bind(null,i,d,e),[e]),i.flags|=2048,Ri(9,Eh.bind(null,i,d,r,t),void 0,null),r},useId:function(){var e=ut(),t=Ce.identifierPrefix;if(le){var r=yt,i=St;r=(i&~(1<<32-ot(i)-1)).toString(32)+r,t=":"+t+"R"+r,r=bi++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=Bf++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Nf={readContext:Ze,useCallback:Bh,useContext:Ze,useEffect:wl,useImperativeHandle:Ah,useInsertionEffect:Rh,useLayoutEffect:Dh,useMemo:Oh,useReducer:Bn,useRef:Lh,useState:function(){return Bn(Li)},useDebugValue:Cl,useDeferredValue:function(e){var t=et();return Ph(t,Se.memoizedState,e)},useTransition:function(){var e=Bn(Li)[0],t=et().memoizedState;return[e,t]},useMutableSource:yh,useSyncExternalStore:wh,useId:Mh,unstable_isNewReconciler:!1},Hf={readContext:Ze,useCallback:Bh,useContext:Ze,useEffect:wl,useImperativeHandle:Ah,useInsertionEffect:Rh,useLayoutEffect:Dh,useMemo:Oh,useReducer:On,useRef:Lh,useState:function(){return On(Li)},useDebugValue:Cl,useDeferredValue:function(e){var t=et();return Se===null?t.memoizedState=e:Ph(t,Se.memoizedState,e)},useTransition:function(){var e=On(Li)[0],t=et().memoizedState;return[e,t]},useMutableSource:yh,useSyncExternalStore:wh,useId:Mh,unstable_isNewReconciler:!1};function rt(e,t){if(e&&e.defaultProps){t=ue({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function So(e,t,r,i){t=e.memoizedState,r=r(i,t),r=r==null?t:ue({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var Qs={isMounted:function(e){return(e=e._reactInternals)?cr(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var i=Ae(),l=Wt(e),d=wt(i,l);d.payload=t,r!=null&&(d.callback=r),t=jt(e,d,l),t!==null&&(lt(t,e,l,i),ls(t,e,l))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var i=Ae(),l=Wt(e),d=wt(i,l);d.tag=1,d.payload=t,r!=null&&(d.callback=r),t=jt(e,d,l),t!==null&&(lt(t,e,l,i),ls(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=Ae(),i=Wt(e),l=wt(r,i);l.tag=2,t!=null&&(l.callback=t),t=jt(e,l,i),t!==null&&(lt(t,e,i,r),ls(t,e,i))}};function Da(e,t,r,i,l,d,m){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,d,m):t.prototype&&t.prototype.isPureReactComponent?!yi(r,i)||!yi(l,d):!0}function Fh(e,t,r){var i=!1,l=Kt,d=t.contextType;return typeof d=="object"&&d!==null?d=Ze(d):(l=Fe(t)?ir:De.current,i=t.contextTypes,d=(i=i!=null)?Tr(e,l):Kt),t=new t(r,d),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Qs,e.stateNode=t,t._reactInternals=e,i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=d),t}function Ta(e,t,r,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,i),t.state!==e&&Qs.enqueueReplaceState(t,t.state,null)}function yo(e,t,r,i){var l=e.stateNode;l.props=r,l.state=e.memoizedState,l.refs={},_l(e);var d=t.contextType;typeof d=="object"&&d!==null?l.context=Ze(d):(d=Fe(t)?ir:De.current,l.context=Tr(e,d)),l.state=e.memoizedState,d=t.getDerivedStateFromProps,typeof d=="function"&&(So(e,t,d,r),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Qs.enqueueReplaceState(l,l.state,null),Os(e,r,l,i),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function Pr(e,t){try{var r="",i=t;do r+=ud(i),i=i.return;while(i);var l=r}catch(d){l=`
Error generating stack: `+d.message+`
`+d.stack}return{value:e,source:t,stack:l,digest:null}}function Pn(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function wo(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var Ff=typeof WeakMap=="function"?WeakMap:Map;function jh(e,t,r){r=wt(-1,r),r.tag=3,r.payload={element:null};var i=t.value;return r.callback=function(){Hs||(Hs=!0,Ao=i),wo(e,t)},r}function zh(e,t,r){r=wt(-1,r),r.tag=3;var i=e.type.getDerivedStateFromError;if(typeof i=="function"){var l=t.value;r.payload=function(){return i(l)},r.callback=function(){wo(e,t)}}var d=e.stateNode;return d!==null&&typeof d.componentDidCatch=="function"&&(r.callback=function(){wo(e,t),typeof i!="function"&&(zt===null?zt=new Set([this]):zt.add(this));var m=t.stack;this.componentDidCatch(t.value,{componentStack:m!==null?m:""})}),r}function Aa(e,t,r){var i=e.pingCache;if(i===null){i=e.pingCache=new Ff;var l=new Set;i.set(t,l)}else l=i.get(t),l===void 0&&(l=new Set,i.set(t,l));l.has(r)||(l.add(r),e=Zf.bind(null,e,t,r),t.then(e,e))}function Ba(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Oa(e,t,r,i,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=wt(-1,1),t.tag=2,jt(r,t,1))),r.lanes|=1),e)}var jf=bt.ReactCurrentOwner,Ne=!1;function Te(e,t,r,i){t.child=e===null?vh(t,null,r,i):Br(t,e.child,r,i)}function Pa(e,t,r,i,l){r=r.render;var d=t.ref;return Lr(t,l),i=Sl(e,t,r,i,d,l),r=yl(),e!==null&&!Ne?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,xt(e,t,l)):(le&&r&&ll(t),t.flags|=1,Te(e,t,i,l),t.child)}function Ma(e,t,r,i,l){if(e===null){var d=r.type;return typeof d=="function"&&!Tl(d)&&d.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=d,Wh(e,t,d,i,l)):(e=fs(r.type,null,i,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(d=e.child,!(e.lanes&l)){var m=d.memoizedProps;if(r=r.compare,r=r!==null?r:yi,r(m,i)&&e.ref===t.ref)return xt(e,t,l)}return t.flags|=1,e=Ut(d,i),e.ref=t.ref,e.return=t,t.child=e}function Wh(e,t,r,i,l){if(e!==null){var d=e.memoizedProps;if(yi(d,i)&&e.ref===t.ref)if(Ne=!1,t.pendingProps=i=d,(e.lanes&l)!==0)e.flags&131072&&(Ne=!0);else return t.lanes=e.lanes,xt(e,t,l)}return Co(e,t,r,i,l)}function Uh(e,t,r){var i=t.pendingProps,l=i.children,d=e!==null?e.memoizedState:null;if(i.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},se(Cr,ze),ze|=r;else{if(!(r&1073741824))return e=d!==null?d.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,se(Cr,ze),ze|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},i=d!==null?d.baseLanes:r,se(Cr,ze),ze|=i}else d!==null?(i=d.baseLanes|r,t.memoizedState=null):i=r,se(Cr,ze),ze|=i;return Te(e,t,l,r),t.child}function $h(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function Co(e,t,r,i,l){var d=Fe(r)?ir:De.current;return d=Tr(t,d),Lr(t,l),r=Sl(e,t,r,i,d,l),i=yl(),e!==null&&!Ne?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,xt(e,t,l)):(le&&i&&ll(t),t.flags|=1,Te(e,t,r,l),t.child)}function Ia(e,t,r,i,l){if(Fe(r)){var d=!0;Rs(t)}else d=!1;if(Lr(t,l),t.stateNode===null)hs(e,t),Fh(t,r,i),yo(t,r,i,l),i=!0;else if(e===null){var m=t.stateNode,n=t.memoizedProps;m.props=n;var c=m.context,f=r.contextType;typeof f=="object"&&f!==null?f=Ze(f):(f=Fe(r)?ir:De.current,f=Tr(t,f));var S=r.getDerivedStateFromProps,u=typeof S=="function"||typeof m.getSnapshotBeforeUpdate=="function";u||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(n!==i||c!==f)&&Ta(t,m,i,f),At=!1;var _=t.memoizedState;m.state=_,Os(t,i,m,l),c=t.memoizedState,n!==i||_!==c||He.current||At?(typeof S=="function"&&(So(t,r,S,i),c=t.memoizedState),(n=At||Da(t,r,n,i,_,c,f))?(u||typeof m.UNSAFE_componentWillMount!="function"&&typeof m.componentWillMount!="function"||(typeof m.componentWillMount=="function"&&m.componentWillMount(),typeof m.UNSAFE_componentWillMount=="function"&&m.UNSAFE_componentWillMount()),typeof m.componentDidMount=="function"&&(t.flags|=4194308)):(typeof m.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=c),m.props=i,m.state=c,m.context=f,i=n):(typeof m.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{m=t.stateNode,mh(e,t),n=t.memoizedProps,f=t.type===t.elementType?n:rt(t.type,n),m.props=f,u=t.pendingProps,_=m.context,c=r.contextType,typeof c=="object"&&c!==null?c=Ze(c):(c=Fe(r)?ir:De.current,c=Tr(t,c));var g=r.getDerivedStateFromProps;(S=typeof g=="function"||typeof m.getSnapshotBeforeUpdate=="function")||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(n!==u||_!==c)&&Ta(t,m,i,c),At=!1,_=t.memoizedState,m.state=_,Os(t,i,m,l);var C=t.memoizedState;n!==u||_!==C||He.current||At?(typeof g=="function"&&(So(t,r,g,i),C=t.memoizedState),(f=At||Da(t,r,f,i,_,C,c)||!1)?(S||typeof m.UNSAFE_componentWillUpdate!="function"&&typeof m.componentWillUpdate!="function"||(typeof m.componentWillUpdate=="function"&&m.componentWillUpdate(i,C,c),typeof m.UNSAFE_componentWillUpdate=="function"&&m.UNSAFE_componentWillUpdate(i,C,c)),typeof m.componentDidUpdate=="function"&&(t.flags|=4),typeof m.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof m.componentDidUpdate!="function"||n===e.memoizedProps&&_===e.memoizedState||(t.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||n===e.memoizedProps&&_===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=C),m.props=i,m.state=C,m.context=c,i=f):(typeof m.componentDidUpdate!="function"||n===e.memoizedProps&&_===e.memoizedState||(t.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||n===e.memoizedProps&&_===e.memoizedState||(t.flags|=1024),i=!1)}return Eo(e,t,r,i,d,l)}function Eo(e,t,r,i,l,d){$h(e,t);var m=(t.flags&128)!==0;if(!i&&!m)return l&&wa(t,r,!1),xt(e,t,d);i=t.stateNode,jf.current=t;var n=m&&typeof r.getDerivedStateFromError!="function"?null:i.render();return t.flags|=1,e!==null&&m?(t.child=Br(t,e.child,null,d),t.child=Br(t,null,n,d)):Te(e,t,n,d),t.memoizedState=i.state,l&&wa(t,r,!0),t.child}function Vh(e){var t=e.stateNode;t.pendingContext?ya(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ya(e,t.context,!1),pl(e,t.containerInfo)}function Na(e,t,r,i,l){return Ar(),cl(l),t.flags|=256,Te(e,t,r,i),t.child}var ko={dehydrated:null,treeContext:null,retryLane:0};function xo(e){return{baseLanes:e,cachePool:null,transitions:null}}function Kh(e,t,r){var i=t.pendingProps,l=ce.current,d=!1,m=(t.flags&128)!==0,n;if((n=m)||(n=e!==null&&e.memoizedState===null?!1:(l&2)!==0),n?(d=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),se(ce,l&1),e===null)return go(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(m=i.children,e=i.fallback,d?(i=t.mode,d=t.child,m={mode:"hidden",children:m},!(i&1)&&d!==null?(d.childLanes=0,d.pendingProps=m):d=en(m,i,0,null),e=rr(e,i,r,null),d.return=t,e.return=t,d.sibling=e,t.child=d,t.child.memoizedState=xo(r),t.memoizedState=ko,e):El(t,m));if(l=e.memoizedState,l!==null&&(n=l.dehydrated,n!==null))return zf(e,t,m,i,n,l,r);if(d){d=i.fallback,m=t.mode,l=e.child,n=l.sibling;var c={mode:"hidden",children:i.children};return!(m&1)&&t.child!==l?(i=t.child,i.childLanes=0,i.pendingProps=c,t.deletions=null):(i=Ut(l,c),i.subtreeFlags=l.subtreeFlags&14680064),n!==null?d=Ut(n,d):(d=rr(d,m,r,null),d.flags|=2),d.return=t,i.return=t,i.sibling=d,t.child=i,i=d,d=t.child,m=e.child.memoizedState,m=m===null?xo(r):{baseLanes:m.baseLanes|r,cachePool:null,transitions:m.transitions},d.memoizedState=m,d.childLanes=e.childLanes&~r,t.memoizedState=ko,i}return d=e.child,e=d.sibling,i=Ut(d,{mode:"visible",children:i.children}),!(t.mode&1)&&(i.lanes=r),i.return=t,i.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=i,t.memoizedState=null,i}function El(e,t){return t=en({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Yi(e,t,r,i){return i!==null&&cl(i),Br(t,e.child,null,r),e=El(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function zf(e,t,r,i,l,d,m){if(r)return t.flags&256?(t.flags&=-257,i=Pn(Error(U(422))),Yi(e,t,m,i)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(d=i.fallback,l=t.mode,i=en({mode:"visible",children:i.children},l,0,null),d=rr(d,l,m,null),d.flags|=2,i.return=t,d.return=t,i.sibling=d,t.child=i,t.mode&1&&Br(t,e.child,null,m),t.child.memoizedState=xo(m),t.memoizedState=ko,d);if(!(t.mode&1))return Yi(e,t,m,null);if(l.data==="$!"){if(i=l.nextSibling&&l.nextSibling.dataset,i)var n=i.dgst;return i=n,d=Error(U(419)),i=Pn(d,i,void 0),Yi(e,t,m,i)}if(n=(m&e.childLanes)!==0,Ne||n){if(i=Ce,i!==null){switch(m&-m){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(i.suspendedLanes|m)?0:l,l!==0&&l!==d.retryLane&&(d.retryLane=l,kt(e,l),lt(i,e,l,-1))}return Dl(),i=Pn(Error(U(421))),Yi(e,t,m,i)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=e_.bind(null,e),l._reactRetry=t,null):(e=d.treeContext,We=Ft(l.nextSibling),Ue=t,le=!0,nt=null,e!==null&&(Xe[Ge++]=St,Xe[Ge++]=yt,Xe[Ge++]=sr,St=e.id,yt=e.overflow,sr=t),t=El(t,i.children),t.flags|=4096,t)}function Ha(e,t,r){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),mo(e.return,t,r)}function Mn(e,t,r,i,l){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:r,tailMode:l}:(d.isBackwards=t,d.rendering=null,d.renderingStartTime=0,d.last=i,d.tail=r,d.tailMode=l)}function qh(e,t,r){var i=t.pendingProps,l=i.revealOrder,d=i.tail;if(Te(e,t,i.children,r),i=ce.current,i&2)i=i&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ha(e,r,t);else if(e.tag===19)Ha(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}if(se(ce,i),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(r=t.child,l=null;r!==null;)e=r.alternate,e!==null&&Ps(e)===null&&(l=r),r=r.sibling;r=l,r===null?(l=t.child,t.child=null):(l=r.sibling,r.sibling=null),Mn(t,!1,l,r,d);break;case"backwards":for(r=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&Ps(e)===null){t.child=l;break}e=l.sibling,l.sibling=r,r=l,l=e}Mn(t,!0,r,null,d);break;case"together":Mn(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function hs(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function xt(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),or|=t.lanes,!(r&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(U(153));if(t.child!==null){for(e=t.child,r=Ut(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=Ut(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function Wf(e,t,r){switch(t.tag){case 3:Vh(t),Ar();break;case 5:Sh(t);break;case 1:Fe(t.type)&&Rs(t);break;case 4:pl(t,t.stateNode.containerInfo);break;case 10:var i=t.type._context,l=t.memoizedProps.value;se(As,i._currentValue),i._currentValue=l;break;case 13:if(i=t.memoizedState,i!==null)return i.dehydrated!==null?(se(ce,ce.current&1),t.flags|=128,null):r&t.child.childLanes?Kh(e,t,r):(se(ce,ce.current&1),e=xt(e,t,r),e!==null?e.sibling:null);se(ce,ce.current&1);break;case 19:if(i=(r&t.childLanes)!==0,e.flags&128){if(i)return qh(e,t,r);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),se(ce,ce.current),i)break;return null;case 22:case 23:return t.lanes=0,Uh(e,t,r)}return xt(e,t,r)}var Xh,bo,Gh,Yh;Xh=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};bo=function(){};Gh=function(e,t,r,i){var l=e.memoizedProps;if(l!==i){e=t.stateNode,er(_t.current);var d=null;switch(r){case"input":l=qn(e,l),i=qn(e,i),d=[];break;case"select":l=ue({},l,{value:void 0}),i=ue({},i,{value:void 0}),d=[];break;case"textarea":l=Yn(e,l),i=Yn(e,i),d=[];break;default:typeof l.onClick!="function"&&typeof i.onClick=="function"&&(e.onclick=bs)}Jn(r,i);var m;r=null;for(f in l)if(!i.hasOwnProperty(f)&&l.hasOwnProperty(f)&&l[f]!=null)if(f==="style"){var n=l[f];for(m in n)n.hasOwnProperty(m)&&(r||(r={}),r[m]="")}else f!=="dangerouslySetInnerHTML"&&f!=="children"&&f!=="suppressContentEditableWarning"&&f!=="suppressHydrationWarning"&&f!=="autoFocus"&&(fi.hasOwnProperty(f)?d||(d=[]):(d=d||[]).push(f,null));for(f in i){var c=i[f];if(n=l!=null?l[f]:void 0,i.hasOwnProperty(f)&&c!==n&&(c!=null||n!=null))if(f==="style")if(n){for(m in n)!n.hasOwnProperty(m)||c&&c.hasOwnProperty(m)||(r||(r={}),r[m]="");for(m in c)c.hasOwnProperty(m)&&n[m]!==c[m]&&(r||(r={}),r[m]=c[m])}else r||(d||(d=[]),d.push(f,r)),r=c;else f==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,n=n?n.__html:void 0,c!=null&&n!==c&&(d=d||[]).push(f,c)):f==="children"?typeof c!="string"&&typeof c!="number"||(d=d||[]).push(f,""+c):f!=="suppressContentEditableWarning"&&f!=="suppressHydrationWarning"&&(fi.hasOwnProperty(f)?(c!=null&&f==="onScroll"&&ne("scroll",e),d||n===c||(d=[])):(d=d||[]).push(f,c))}r&&(d=d||[]).push("style",r);var f=d;(t.updateQueue=f)&&(t.flags|=4)}};Yh=function(e,t,r,i){r!==i&&(t.flags|=4)};function Yr(e,t){if(!le)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var i=null;r!==null;)r.alternate!==null&&(i=r),r=r.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function Le(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,i=0;if(t)for(var l=e.child;l!==null;)r|=l.lanes|l.childLanes,i|=l.subtreeFlags&14680064,i|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)r|=l.lanes|l.childLanes,i|=l.subtreeFlags,i|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=i,e.childLanes=r,t}function Uf(e,t,r){var i=t.pendingProps;switch(al(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Le(t),null;case 1:return Fe(t.type)&&Ls(),Le(t),null;case 3:return i=t.stateNode,Or(),oe(He),oe(De),gl(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(e===null||e.child===null)&&(Xi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,nt!==null&&(Po(nt),nt=null))),bo(e,t),Le(t),null;case 5:vl(t);var l=er(xi.current);if(r=t.type,e!==null&&t.stateNode!=null)Gh(e,t,r,i,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!i){if(t.stateNode===null)throw Error(U(166));return Le(t),null}if(e=er(_t.current),Xi(t)){i=t.stateNode,r=t.type;var d=t.memoizedProps;switch(i[dt]=t,i[Ei]=d,e=(t.mode&1)!==0,r){case"dialog":ne("cancel",i),ne("close",i);break;case"iframe":case"object":case"embed":ne("load",i);break;case"video":case"audio":for(l=0;l<ri.length;l++)ne(ri[l],i);break;case"source":ne("error",i);break;case"img":case"image":case"link":ne("error",i),ne("load",i);break;case"details":ne("toggle",i);break;case"input":ql(i,d),ne("invalid",i);break;case"select":i._wrapperState={wasMultiple:!!d.multiple},ne("invalid",i);break;case"textarea":Gl(i,d),ne("invalid",i)}Jn(r,d),l=null;for(var m in d)if(d.hasOwnProperty(m)){var n=d[m];m==="children"?typeof n=="string"?i.textContent!==n&&(d.suppressHydrationWarning!==!0&&qi(i.textContent,n,e),l=["children",n]):typeof n=="number"&&i.textContent!==""+n&&(d.suppressHydrationWarning!==!0&&qi(i.textContent,n,e),l=["children",""+n]):fi.hasOwnProperty(m)&&n!=null&&m==="onScroll"&&ne("scroll",i)}switch(r){case"input":Fi(i),Xl(i,d,!0);break;case"textarea":Fi(i),Yl(i);break;case"select":case"option":break;default:typeof d.onClick=="function"&&(i.onclick=bs)}i=l,t.updateQueue=i,i!==null&&(t.flags|=4)}else{m=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=kc(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=m.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof i.is=="string"?e=m.createElement(r,{is:i.is}):(e=m.createElement(r),r==="select"&&(m=e,i.multiple?m.multiple=!0:i.size&&(m.size=i.size))):e=m.createElementNS(e,r),e[dt]=t,e[Ei]=i,Xh(e,t,!1,!1),t.stateNode=e;e:{switch(m=Zn(r,i),r){case"dialog":ne("cancel",e),ne("close",e),l=i;break;case"iframe":case"object":case"embed":ne("load",e),l=i;break;case"video":case"audio":for(l=0;l<ri.length;l++)ne(ri[l],e);l=i;break;case"source":ne("error",e),l=i;break;case"img":case"image":case"link":ne("error",e),ne("load",e),l=i;break;case"details":ne("toggle",e),l=i;break;case"input":ql(e,i),l=qn(e,i),ne("invalid",e);break;case"option":l=i;break;case"select":e._wrapperState={wasMultiple:!!i.multiple},l=ue({},i,{value:void 0}),ne("invalid",e);break;case"textarea":Gl(e,i),l=Yn(e,i),ne("invalid",e);break;default:l=i}Jn(r,l),n=l;for(d in n)if(n.hasOwnProperty(d)){var c=n[d];d==="style"?Lc(e,c):d==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&xc(e,c)):d==="children"?typeof c=="string"?(r!=="textarea"||c!=="")&&_i(e,c):typeof c=="number"&&_i(e,""+c):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(fi.hasOwnProperty(d)?c!=null&&d==="onScroll"&&ne("scroll",e):c!=null&&qo(e,d,c,m))}switch(r){case"input":Fi(e),Xl(e,i,!1);break;case"textarea":Fi(e),Yl(e);break;case"option":i.value!=null&&e.setAttribute("value",""+Vt(i.value));break;case"select":e.multiple=!!i.multiple,d=i.value,d!=null?Er(e,!!i.multiple,d,!1):i.defaultValue!=null&&Er(e,!!i.multiple,i.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=bs)}switch(r){case"button":case"input":case"select":case"textarea":i=!!i.autoFocus;break e;case"img":i=!0;break e;default:i=!1}}i&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Le(t),null;case 6:if(e&&t.stateNode!=null)Yh(e,t,e.memoizedProps,i);else{if(typeof i!="string"&&t.stateNode===null)throw Error(U(166));if(r=er(xi.current),er(_t.current),Xi(t)){if(i=t.stateNode,r=t.memoizedProps,i[dt]=t,(d=i.nodeValue!==r)&&(e=Ue,e!==null))switch(e.tag){case 3:qi(i.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&qi(i.nodeValue,r,(e.mode&1)!==0)}d&&(t.flags|=4)}else i=(r.nodeType===9?r:r.ownerDocument).createTextNode(i),i[dt]=t,t.stateNode=i}return Le(t),null;case 13:if(oe(ce),i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(le&&We!==null&&t.mode&1&&!(t.flags&128))_h(),Ar(),t.flags|=98560,d=!1;else if(d=Xi(t),i!==null&&i.dehydrated!==null){if(e===null){if(!d)throw Error(U(318));if(d=t.memoizedState,d=d!==null?d.dehydrated:null,!d)throw Error(U(317));d[dt]=t}else Ar(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Le(t),d=!1}else nt!==null&&(Po(nt),nt=null),d=!0;if(!d)return t.flags&65536?t:null}return t.flags&128?(t.lanes=r,t):(i=i!==null,i!==(e!==null&&e.memoizedState!==null)&&i&&(t.child.flags|=8192,t.mode&1&&(e===null||ce.current&1?ye===0&&(ye=3):Dl())),t.updateQueue!==null&&(t.flags|=4),Le(t),null);case 4:return Or(),bo(e,t),e===null&&wi(t.stateNode.containerInfo),Le(t),null;case 10:return dl(t.type._context),Le(t),null;case 17:return Fe(t.type)&&Ls(),Le(t),null;case 19:if(oe(ce),d=t.memoizedState,d===null)return Le(t),null;if(i=(t.flags&128)!==0,m=d.rendering,m===null)if(i)Yr(d,!1);else{if(ye!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(m=Ps(e),m!==null){for(t.flags|=128,Yr(d,!1),i=m.updateQueue,i!==null&&(t.updateQueue=i,t.flags|=4),t.subtreeFlags=0,i=r,r=t.child;r!==null;)d=r,e=i,d.flags&=14680066,m=d.alternate,m===null?(d.childLanes=0,d.lanes=e,d.child=null,d.subtreeFlags=0,d.memoizedProps=null,d.memoizedState=null,d.updateQueue=null,d.dependencies=null,d.stateNode=null):(d.childLanes=m.childLanes,d.lanes=m.lanes,d.child=m.child,d.subtreeFlags=0,d.deletions=null,d.memoizedProps=m.memoizedProps,d.memoizedState=m.memoizedState,d.updateQueue=m.updateQueue,d.type=m.type,e=m.dependencies,d.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return se(ce,ce.current&1|2),t.child}e=e.sibling}d.tail!==null&&ve()>Mr&&(t.flags|=128,i=!0,Yr(d,!1),t.lanes=4194304)}else{if(!i)if(e=Ps(m),e!==null){if(t.flags|=128,i=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),Yr(d,!0),d.tail===null&&d.tailMode==="hidden"&&!m.alternate&&!le)return Le(t),null}else 2*ve()-d.renderingStartTime>Mr&&r!==1073741824&&(t.flags|=128,i=!0,Yr(d,!1),t.lanes=4194304);d.isBackwards?(m.sibling=t.child,t.child=m):(r=d.last,r!==null?r.sibling=m:t.child=m,d.last=m)}return d.tail!==null?(t=d.tail,d.rendering=t,d.tail=t.sibling,d.renderingStartTime=ve(),t.sibling=null,r=ce.current,se(ce,i?r&1|2:r&1),t):(Le(t),null);case 22:case 23:return Rl(),i=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==i&&(t.flags|=8192),i&&t.mode&1?ze&1073741824&&(Le(t),t.subtreeFlags&6&&(t.flags|=8192)):Le(t),null;case 24:return null;case 25:return null}throw Error(U(156,t.tag))}function $f(e,t){switch(al(t),t.tag){case 1:return Fe(t.type)&&Ls(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Or(),oe(He),oe(De),gl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return vl(t),null;case 13:if(oe(ce),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(U(340));Ar()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return oe(ce),null;case 4:return Or(),null;case 10:return dl(t.type._context),null;case 22:case 23:return Rl(),null;case 24:return null;default:return null}}var Qi=!1,Re=!1,Vf=typeof WeakSet=="function"?WeakSet:Set,G=null;function wr(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(i){_e(e,t,i)}else r.current=null}function Lo(e,t,r){try{r()}catch(i){_e(e,t,i)}}var Fa=!1;function Kf(e,t){if(co=Es,e=th(),ol(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var i=r.getSelection&&r.getSelection();if(i&&i.rangeCount!==0){r=i.anchorNode;var l=i.anchorOffset,d=i.focusNode;i=i.focusOffset;try{r.nodeType,d.nodeType}catch{r=null;break e}var m=0,n=-1,c=-1,f=0,S=0,u=e,_=null;t:for(;;){for(var g;u!==r||l!==0&&u.nodeType!==3||(n=m+l),u!==d||i!==0&&u.nodeType!==3||(c=m+i),u.nodeType===3&&(m+=u.nodeValue.length),(g=u.firstChild)!==null;)_=u,u=g;for(;;){if(u===e)break t;if(_===r&&++f===l&&(n=m),_===d&&++S===i&&(c=m),(g=u.nextSibling)!==null)break;u=_,_=u.parentNode}u=g}r=n===-1||c===-1?null:{start:n,end:c}}else r=null}r=r||{start:0,end:0}}else r=null;for(ho={focusedElem:e,selectionRange:r},Es=!1,G=t;G!==null;)if(t=G,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,G=e;else for(;G!==null;){t=G;try{var C=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(C!==null){var v=C.memoizedProps,h=C.memoizedState,a=t.stateNode,s=a.getSnapshotBeforeUpdate(t.elementType===t.type?v:rt(t.type,v),h);a.__reactInternalSnapshotBeforeUpdate=s}break;case 3:var o=t.stateNode.containerInfo;o.nodeType===1?o.textContent="":o.nodeType===9&&o.documentElement&&o.removeChild(o.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(U(163))}}catch(p){_e(t,t.return,p)}if(e=t.sibling,e!==null){e.return=t.return,G=e;break}G=t.return}return C=Fa,Fa=!1,C}function hi(e,t,r){var i=t.updateQueue;if(i=i!==null?i.lastEffect:null,i!==null){var l=i=i.next;do{if((l.tag&e)===e){var d=l.destroy;l.destroy=void 0,d!==void 0&&Lo(t,r,d)}l=l.next}while(l!==i)}}function Js(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var i=r.create;r.destroy=i()}r=r.next}while(r!==t)}}function Ro(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function Qh(e){var t=e.alternate;t!==null&&(e.alternate=null,Qh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[dt],delete t[Ei],delete t[_o],delete t[Rf],delete t[Df])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Jh(e){return e.tag===5||e.tag===3||e.tag===4}function ja(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Jh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Do(e,t,r){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=bs));else if(i!==4&&(e=e.child,e!==null))for(Do(e,t,r),e=e.sibling;e!==null;)Do(e,t,r),e=e.sibling}function To(e,t,r){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(i!==4&&(e=e.child,e!==null))for(To(e,t,r),e=e.sibling;e!==null;)To(e,t,r),e=e.sibling}var Ee=null,it=!1;function Dt(e,t,r){for(r=r.child;r!==null;)Zh(e,t,r),r=r.sibling}function Zh(e,t,r){if(ft&&typeof ft.onCommitFiberUnmount=="function")try{ft.onCommitFiberUnmount($s,r)}catch{}switch(r.tag){case 5:Re||wr(r,t);case 6:var i=Ee,l=it;Ee=null,Dt(e,t,r),Ee=i,it=l,Ee!==null&&(it?(e=Ee,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):Ee.removeChild(r.stateNode));break;case 18:Ee!==null&&(it?(e=Ee,r=r.stateNode,e.nodeType===8?Rn(e.parentNode,r):e.nodeType===1&&Rn(e,r),mi(e)):Rn(Ee,r.stateNode));break;case 4:i=Ee,l=it,Ee=r.stateNode.containerInfo,it=!0,Dt(e,t,r),Ee=i,it=l;break;case 0:case 11:case 14:case 15:if(!Re&&(i=r.updateQueue,i!==null&&(i=i.lastEffect,i!==null))){l=i=i.next;do{var d=l,m=d.destroy;d=d.tag,m!==void 0&&(d&2||d&4)&&Lo(r,t,m),l=l.next}while(l!==i)}Dt(e,t,r);break;case 1:if(!Re&&(wr(r,t),i=r.stateNode,typeof i.componentWillUnmount=="function"))try{i.props=r.memoizedProps,i.state=r.memoizedState,i.componentWillUnmount()}catch(n){_e(r,t,n)}Dt(e,t,r);break;case 21:Dt(e,t,r);break;case 22:r.mode&1?(Re=(i=Re)||r.memoizedState!==null,Dt(e,t,r),Re=i):Dt(e,t,r);break;default:Dt(e,t,r)}}function za(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new Vf),t.forEach(function(i){var l=t_.bind(null,e,i);r.has(i)||(r.add(i),i.then(l,l))})}}function tt(e,t){var r=t.deletions;if(r!==null)for(var i=0;i<r.length;i++){var l=r[i];try{var d=e,m=t,n=m;e:for(;n!==null;){switch(n.tag){case 5:Ee=n.stateNode,it=!1;break e;case 3:Ee=n.stateNode.containerInfo,it=!0;break e;case 4:Ee=n.stateNode.containerInfo,it=!0;break e}n=n.return}if(Ee===null)throw Error(U(160));Zh(d,m,l),Ee=null,it=!1;var c=l.alternate;c!==null&&(c.return=null),l.return=null}catch(f){_e(l,t,f)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)eu(t,e),t=t.sibling}function eu(e,t){var r=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(tt(t,e),ht(e),i&4){try{hi(3,e,e.return),Js(3,e)}catch(v){_e(e,e.return,v)}try{hi(5,e,e.return)}catch(v){_e(e,e.return,v)}}break;case 1:tt(t,e),ht(e),i&512&&r!==null&&wr(r,r.return);break;case 5:if(tt(t,e),ht(e),i&512&&r!==null&&wr(r,r.return),e.flags&32){var l=e.stateNode;try{_i(l,"")}catch(v){_e(e,e.return,v)}}if(i&4&&(l=e.stateNode,l!=null)){var d=e.memoizedProps,m=r!==null?r.memoizedProps:d,n=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{n==="input"&&d.type==="radio"&&d.name!=null&&Cc(l,d),Zn(n,m);var f=Zn(n,d);for(m=0;m<c.length;m+=2){var S=c[m],u=c[m+1];S==="style"?Lc(l,u):S==="dangerouslySetInnerHTML"?xc(l,u):S==="children"?_i(l,u):qo(l,S,u,f)}switch(n){case"input":Xn(l,d);break;case"textarea":Ec(l,d);break;case"select":var _=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!d.multiple;var g=d.value;g!=null?Er(l,!!d.multiple,g,!1):_!==!!d.multiple&&(d.defaultValue!=null?Er(l,!!d.multiple,d.defaultValue,!0):Er(l,!!d.multiple,d.multiple?[]:"",!1))}l[Ei]=d}catch(v){_e(e,e.return,v)}}break;case 6:if(tt(t,e),ht(e),i&4){if(e.stateNode===null)throw Error(U(162));l=e.stateNode,d=e.memoizedProps;try{l.nodeValue=d}catch(v){_e(e,e.return,v)}}break;case 3:if(tt(t,e),ht(e),i&4&&r!==null&&r.memoizedState.isDehydrated)try{mi(t.containerInfo)}catch(v){_e(e,e.return,v)}break;case 4:tt(t,e),ht(e);break;case 13:tt(t,e),ht(e),l=e.child,l.flags&8192&&(d=l.memoizedState!==null,l.stateNode.isHidden=d,!d||l.alternate!==null&&l.alternate.memoizedState!==null||(bl=ve())),i&4&&za(e);break;case 22:if(S=r!==null&&r.memoizedState!==null,e.mode&1?(Re=(f=Re)||S,tt(t,e),Re=f):tt(t,e),ht(e),i&8192){if(f=e.memoizedState!==null,(e.stateNode.isHidden=f)&&!S&&e.mode&1)for(G=e,S=e.child;S!==null;){for(u=G=S;G!==null;){switch(_=G,g=_.child,_.tag){case 0:case 11:case 14:case 15:hi(4,_,_.return);break;case 1:wr(_,_.return);var C=_.stateNode;if(typeof C.componentWillUnmount=="function"){i=_,r=_.return;try{t=i,C.props=t.memoizedProps,C.state=t.memoizedState,C.componentWillUnmount()}catch(v){_e(i,r,v)}}break;case 5:wr(_,_.return);break;case 22:if(_.memoizedState!==null){Ua(u);continue}}g!==null?(g.return=_,G=g):Ua(u)}S=S.sibling}e:for(S=null,u=e;;){if(u.tag===5){if(S===null){S=u;try{l=u.stateNode,f?(d=l.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none"):(n=u.stateNode,c=u.memoizedProps.style,m=c!=null&&c.hasOwnProperty("display")?c.display:null,n.style.display=bc("display",m))}catch(v){_e(e,e.return,v)}}}else if(u.tag===6){if(S===null)try{u.stateNode.nodeValue=f?"":u.memoizedProps}catch(v){_e(e,e.return,v)}}else if((u.tag!==22&&u.tag!==23||u.memoizedState===null||u===e)&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===e)break e;for(;u.sibling===null;){if(u.return===null||u.return===e)break e;S===u&&(S=null),u=u.return}S===u&&(S=null),u.sibling.return=u.return,u=u.sibling}}break;case 19:tt(t,e),ht(e),i&4&&za(e);break;case 21:break;default:tt(t,e),ht(e)}}function ht(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(Jh(r)){var i=r;break e}r=r.return}throw Error(U(160))}switch(i.tag){case 5:var l=i.stateNode;i.flags&32&&(_i(l,""),i.flags&=-33);var d=ja(e);To(e,d,l);break;case 3:case 4:var m=i.stateNode.containerInfo,n=ja(e);Do(e,n,m);break;default:throw Error(U(161))}}catch(c){_e(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function qf(e,t,r){G=e,tu(e)}function tu(e,t,r){for(var i=(e.mode&1)!==0;G!==null;){var l=G,d=l.child;if(l.tag===22&&i){var m=l.memoizedState!==null||Qi;if(!m){var n=l.alternate,c=n!==null&&n.memoizedState!==null||Re;n=Qi;var f=Re;if(Qi=m,(Re=c)&&!f)for(G=l;G!==null;)m=G,c=m.child,m.tag===22&&m.memoizedState!==null?$a(l):c!==null?(c.return=m,G=c):$a(l);for(;d!==null;)G=d,tu(d),d=d.sibling;G=l,Qi=n,Re=f}Wa(e)}else l.subtreeFlags&8772&&d!==null?(d.return=l,G=d):Wa(e)}}function Wa(e){for(;G!==null;){var t=G;if(t.flags&8772){var r=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Re||Js(5,t);break;case 1:var i=t.stateNode;if(t.flags&4&&!Re)if(r===null)i.componentDidMount();else{var l=t.elementType===t.type?r.memoizedProps:rt(t.type,r.memoizedProps);i.componentDidUpdate(l,r.memoizedState,i.__reactInternalSnapshotBeforeUpdate)}var d=t.updateQueue;d!==null&&ba(t,d,i);break;case 3:var m=t.updateQueue;if(m!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}ba(t,m,r)}break;case 5:var n=t.stateNode;if(r===null&&t.flags&4){r=n;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&r.focus();break;case"img":c.src&&(r.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var f=t.alternate;if(f!==null){var S=f.memoizedState;if(S!==null){var u=S.dehydrated;u!==null&&mi(u)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(U(163))}Re||t.flags&512&&Ro(t)}catch(_){_e(t,t.return,_)}}if(t===e){G=null;break}if(r=t.sibling,r!==null){r.return=t.return,G=r;break}G=t.return}}function Ua(e){for(;G!==null;){var t=G;if(t===e){G=null;break}var r=t.sibling;if(r!==null){r.return=t.return,G=r;break}G=t.return}}function $a(e){for(;G!==null;){var t=G;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{Js(4,t)}catch(c){_e(t,r,c)}break;case 1:var i=t.stateNode;if(typeof i.componentDidMount=="function"){var l=t.return;try{i.componentDidMount()}catch(c){_e(t,l,c)}}var d=t.return;try{Ro(t)}catch(c){_e(t,d,c)}break;case 5:var m=t.return;try{Ro(t)}catch(c){_e(t,m,c)}}}catch(c){_e(t,t.return,c)}if(t===e){G=null;break}var n=t.sibling;if(n!==null){n.return=t.return,G=n;break}G=t.return}}var Xf=Math.ceil,Ns=bt.ReactCurrentDispatcher,kl=bt.ReactCurrentOwner,Je=bt.ReactCurrentBatchConfig,re=0,Ce=null,ge=null,ke=0,ze=0,Cr=Xt(0),ye=0,Di=null,or=0,Zs=0,xl=0,ui=null,Ie=null,bl=0,Mr=1/0,gt=null,Hs=!1,Ao=null,zt=null,Ji=!1,Mt=null,Fs=0,di=0,Bo=null,us=-1,ds=0;function Ae(){return re&6?ve():us!==-1?us:us=ve()}function Wt(e){return e.mode&1?re&2&&ke!==0?ke&-ke:Af.transition!==null?(ds===0&&(ds=Fc()),ds):(e=ie,e!==0||(e=window.event,e=e===void 0?16:Kc(e.type)),e):1}function lt(e,t,r,i){if(50<di)throw di=0,Bo=null,Error(U(185));Ai(e,r,i),(!(re&2)||e!==Ce)&&(e===Ce&&(!(re&2)&&(Zs|=r),ye===4&&Ot(e,ke)),je(e,i),r===1&&re===0&&!(t.mode&1)&&(Mr=ve()+500,Gs&&Gt()))}function je(e,t){var r=e.callbackNode;Td(e,t);var i=Cs(e,e===Ce?ke:0);if(i===0)r!==null&&Zl(r),e.callbackNode=null,e.callbackPriority=0;else if(t=i&-i,e.callbackPriority!==t){if(r!=null&&Zl(r),t===1)e.tag===0?Tf(Va.bind(null,e)):uh(Va.bind(null,e)),bf(function(){!(re&6)&&Gt()}),r=null;else{switch(jc(i)){case 1:r=Jo;break;case 4:r=Nc;break;case 16:r=ws;break;case 536870912:r=Hc;break;default:r=ws}r=cu(r,ru.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function ru(e,t){if(us=-1,ds=0,re&6)throw Error(U(327));var r=e.callbackNode;if(Rr()&&e.callbackNode!==r)return null;var i=Cs(e,e===Ce?ke:0);if(i===0)return null;if(i&30||i&e.expiredLanes||t)t=js(e,i);else{t=i;var l=re;re|=2;var d=su();(Ce!==e||ke!==t)&&(gt=null,Mr=ve()+500,tr(e,t));do try{Qf();break}catch(n){iu(e,n)}while(!0);ul(),Ns.current=d,re=l,ge!==null?t=0:(Ce=null,ke=0,t=ye)}if(t!==0){if(t===2&&(l=so(e),l!==0&&(i=l,t=Oo(e,l))),t===1)throw r=Di,tr(e,0),Ot(e,i),je(e,ve()),r;if(t===6)Ot(e,i);else{if(l=e.current.alternate,!(i&30)&&!Gf(l)&&(t=js(e,i),t===2&&(d=so(e),d!==0&&(i=d,t=Oo(e,d))),t===1))throw r=Di,tr(e,0),Ot(e,i),je(e,ve()),r;switch(e.finishedWork=l,e.finishedLanes=i,t){case 0:case 1:throw Error(U(345));case 2:Qt(e,Ie,gt);break;case 3:if(Ot(e,i),(i&130023424)===i&&(t=bl+500-ve(),10<t)){if(Cs(e,0)!==0)break;if(l=e.suspendedLanes,(l&i)!==i){Ae(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=fo(Qt.bind(null,e,Ie,gt),t);break}Qt(e,Ie,gt);break;case 4:if(Ot(e,i),(i&4194240)===i)break;for(t=e.eventTimes,l=-1;0<i;){var m=31-ot(i);d=1<<m,m=t[m],m>l&&(l=m),i&=~d}if(i=l,i=ve()-i,i=(120>i?120:480>i?480:1080>i?1080:1920>i?1920:3e3>i?3e3:4320>i?4320:1960*Xf(i/1960))-i,10<i){e.timeoutHandle=fo(Qt.bind(null,e,Ie,gt),i);break}Qt(e,Ie,gt);break;case 5:Qt(e,Ie,gt);break;default:throw Error(U(329))}}}return je(e,ve()),e.callbackNode===r?ru.bind(null,e):null}function Oo(e,t){var r=ui;return e.current.memoizedState.isDehydrated&&(tr(e,t).flags|=256),e=js(e,t),e!==2&&(t=Ie,Ie=r,t!==null&&Po(t)),e}function Po(e){Ie===null?Ie=e:Ie.push.apply(Ie,e)}function Gf(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var i=0;i<r.length;i++){var l=r[i],d=l.getSnapshot;l=l.value;try{if(!at(d(),l))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ot(e,t){for(t&=~xl,t&=~Zs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-ot(t),i=1<<r;e[r]=-1,t&=~i}}function Va(e){if(re&6)throw Error(U(327));Rr();var t=Cs(e,0);if(!(t&1))return je(e,ve()),null;var r=js(e,t);if(e.tag!==0&&r===2){var i=so(e);i!==0&&(t=i,r=Oo(e,i))}if(r===1)throw r=Di,tr(e,0),Ot(e,t),je(e,ve()),r;if(r===6)throw Error(U(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Qt(e,Ie,gt),je(e,ve()),null}function Ll(e,t){var r=re;re|=1;try{return e(t)}finally{re=r,re===0&&(Mr=ve()+500,Gs&&Gt())}}function lr(e){Mt!==null&&Mt.tag===0&&!(re&6)&&Rr();var t=re;re|=1;var r=Je.transition,i=ie;try{if(Je.transition=null,ie=1,e)return e()}finally{ie=i,Je.transition=r,re=t,!(re&6)&&Gt()}}function Rl(){ze=Cr.current,oe(Cr)}function tr(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,xf(r)),ge!==null)for(r=ge.return;r!==null;){var i=r;switch(al(i),i.tag){case 1:i=i.type.childContextTypes,i!=null&&Ls();break;case 3:Or(),oe(He),oe(De),gl();break;case 5:vl(i);break;case 4:Or();break;case 13:oe(ce);break;case 19:oe(ce);break;case 10:dl(i.type._context);break;case 22:case 23:Rl()}r=r.return}if(Ce=e,ge=e=Ut(e.current,null),ke=ze=t,ye=0,Di=null,xl=Zs=or=0,Ie=ui=null,Zt!==null){for(t=0;t<Zt.length;t++)if(r=Zt[t],i=r.interleaved,i!==null){r.interleaved=null;var l=i.next,d=r.pending;if(d!==null){var m=d.next;d.next=l,i.next=m}r.pending=i}Zt=null}return e}function iu(e,t){do{var r=ge;try{if(ul(),as.current=Is,Ms){for(var i=he.memoizedState;i!==null;){var l=i.queue;l!==null&&(l.pending=null),i=i.next}Ms=!1}if(nr=0,we=Se=he=null,ci=!1,bi=0,kl.current=null,r===null||r.return===null){ye=1,Di=t,ge=null;break}e:{var d=e,m=r.return,n=r,c=t;if(t=ke,n.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var f=c,S=n,u=S.tag;if(!(S.mode&1)&&(u===0||u===11||u===15)){var _=S.alternate;_?(S.updateQueue=_.updateQueue,S.memoizedState=_.memoizedState,S.lanes=_.lanes):(S.updateQueue=null,S.memoizedState=null)}var g=Ba(m);if(g!==null){g.flags&=-257,Oa(g,m,n,d,t),g.mode&1&&Aa(d,f,t),t=g,c=f;var C=t.updateQueue;if(C===null){var v=new Set;v.add(c),t.updateQueue=v}else C.add(c);break e}else{if(!(t&1)){Aa(d,f,t),Dl();break e}c=Error(U(426))}}else if(le&&n.mode&1){var h=Ba(m);if(h!==null){!(h.flags&65536)&&(h.flags|=256),Oa(h,m,n,d,t),cl(Pr(c,n));break e}}d=c=Pr(c,n),ye!==4&&(ye=2),ui===null?ui=[d]:ui.push(d),d=m;do{switch(d.tag){case 3:d.flags|=65536,t&=-t,d.lanes|=t;var a=jh(d,c,t);xa(d,a);break e;case 1:n=c;var s=d.type,o=d.stateNode;if(!(d.flags&128)&&(typeof s.getDerivedStateFromError=="function"||o!==null&&typeof o.componentDidCatch=="function"&&(zt===null||!zt.has(o)))){d.flags|=65536,t&=-t,d.lanes|=t;var p=zh(d,n,t);xa(d,p);break e}}d=d.return}while(d!==null)}ou(r)}catch(w){t=w,ge===r&&r!==null&&(ge=r=r.return);continue}break}while(!0)}function su(){var e=Ns.current;return Ns.current=Is,e===null?Is:e}function Dl(){(ye===0||ye===3||ye===2)&&(ye=4),Ce===null||!(or&268435455)&&!(Zs&268435455)||Ot(Ce,ke)}function js(e,t){var r=re;re|=2;var i=su();(Ce!==e||ke!==t)&&(gt=null,tr(e,t));do try{Yf();break}catch(l){iu(e,l)}while(!0);if(ul(),re=r,Ns.current=i,ge!==null)throw Error(U(261));return Ce=null,ke=0,ye}function Yf(){for(;ge!==null;)nu(ge)}function Qf(){for(;ge!==null&&!wd();)nu(ge)}function nu(e){var t=au(e.alternate,e,ze);e.memoizedProps=e.pendingProps,t===null?ou(e):ge=t,kl.current=null}function ou(e){var t=e;do{var r=t.alternate;if(e=t.return,t.flags&32768){if(r=$f(r,t),r!==null){r.flags&=32767,ge=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ye=6,ge=null;return}}else if(r=Uf(r,t,ze),r!==null){ge=r;return}if(t=t.sibling,t!==null){ge=t;return}ge=t=e}while(t!==null);ye===0&&(ye=5)}function Qt(e,t,r){var i=ie,l=Je.transition;try{Je.transition=null,ie=1,Jf(e,t,r,i)}finally{Je.transition=l,ie=i}return null}function Jf(e,t,r,i){do Rr();while(Mt!==null);if(re&6)throw Error(U(327));r=e.finishedWork;var l=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(U(177));e.callbackNode=null,e.callbackPriority=0;var d=r.lanes|r.childLanes;if(Ad(e,d),e===Ce&&(ge=Ce=null,ke=0),!(r.subtreeFlags&2064)&&!(r.flags&2064)||Ji||(Ji=!0,cu(ws,function(){return Rr(),null})),d=(r.flags&15990)!==0,r.subtreeFlags&15990||d){d=Je.transition,Je.transition=null;var m=ie;ie=1;var n=re;re|=4,kl.current=null,Kf(e,r),eu(r,e),mf(ho),Es=!!co,ho=co=null,e.current=r,qf(r),Cd(),re=n,ie=m,Je.transition=d}else e.current=r;if(Ji&&(Ji=!1,Mt=e,Fs=l),d=e.pendingLanes,d===0&&(zt=null),xd(r.stateNode),je(e,ve()),t!==null)for(i=e.onRecoverableError,r=0;r<t.length;r++)l=t[r],i(l.value,{componentStack:l.stack,digest:l.digest});if(Hs)throw Hs=!1,e=Ao,Ao=null,e;return Fs&1&&e.tag!==0&&Rr(),d=e.pendingLanes,d&1?e===Bo?di++:(di=0,Bo=e):di=0,Gt(),null}function Rr(){if(Mt!==null){var e=jc(Fs),t=Je.transition,r=ie;try{if(Je.transition=null,ie=16>e?16:e,Mt===null)var i=!1;else{if(e=Mt,Mt=null,Fs=0,re&6)throw Error(U(331));var l=re;for(re|=4,G=e.current;G!==null;){var d=G,m=d.child;if(G.flags&16){var n=d.deletions;if(n!==null){for(var c=0;c<n.length;c++){var f=n[c];for(G=f;G!==null;){var S=G;switch(S.tag){case 0:case 11:case 15:hi(8,S,d)}var u=S.child;if(u!==null)u.return=S,G=u;else for(;G!==null;){S=G;var _=S.sibling,g=S.return;if(Qh(S),S===f){G=null;break}if(_!==null){_.return=g,G=_;break}G=g}}}var C=d.alternate;if(C!==null){var v=C.child;if(v!==null){C.child=null;do{var h=v.sibling;v.sibling=null,v=h}while(v!==null)}}G=d}}if(d.subtreeFlags&2064&&m!==null)m.return=d,G=m;else e:for(;G!==null;){if(d=G,d.flags&2048)switch(d.tag){case 0:case 11:case 15:hi(9,d,d.return)}var a=d.sibling;if(a!==null){a.return=d.return,G=a;break e}G=d.return}}var s=e.current;for(G=s;G!==null;){m=G;var o=m.child;if(m.subtreeFlags&2064&&o!==null)o.return=m,G=o;else e:for(m=s;G!==null;){if(n=G,n.flags&2048)try{switch(n.tag){case 0:case 11:case 15:Js(9,n)}}catch(w){_e(n,n.return,w)}if(n===m){G=null;break e}var p=n.sibling;if(p!==null){p.return=n.return,G=p;break e}G=n.return}}if(re=l,Gt(),ft&&typeof ft.onPostCommitFiberRoot=="function")try{ft.onPostCommitFiberRoot($s,e)}catch{}i=!0}return i}finally{ie=r,Je.transition=t}}return!1}function Ka(e,t,r){t=Pr(r,t),t=jh(e,t,1),e=jt(e,t,1),t=Ae(),e!==null&&(Ai(e,1,t),je(e,t))}function _e(e,t,r){if(e.tag===3)Ka(e,e,r);else for(;t!==null;){if(t.tag===3){Ka(t,e,r);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(zt===null||!zt.has(i))){e=Pr(r,e),e=zh(t,e,1),t=jt(t,e,1),e=Ae(),t!==null&&(Ai(t,1,e),je(t,e));break}}t=t.return}}function Zf(e,t,r){var i=e.pingCache;i!==null&&i.delete(t),t=Ae(),e.pingedLanes|=e.suspendedLanes&r,Ce===e&&(ke&r)===r&&(ye===4||ye===3&&(ke&130023424)===ke&&500>ve()-bl?tr(e,0):xl|=r),je(e,t)}function lu(e,t){t===0&&(e.mode&1?(t=Wi,Wi<<=1,!(Wi&130023424)&&(Wi=4194304)):t=1);var r=Ae();e=kt(e,t),e!==null&&(Ai(e,t,r),je(e,r))}function e_(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),lu(e,r)}function t_(e,t){var r=0;switch(e.tag){case 13:var i=e.stateNode,l=e.memoizedState;l!==null&&(r=l.retryLane);break;case 19:i=e.stateNode;break;default:throw Error(U(314))}i!==null&&i.delete(t),lu(e,r)}var au;au=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||He.current)Ne=!0;else{if(!(e.lanes&r)&&!(t.flags&128))return Ne=!1,Wf(e,t,r);Ne=!!(e.flags&131072)}else Ne=!1,le&&t.flags&1048576&&dh(t,Ts,t.index);switch(t.lanes=0,t.tag){case 2:var i=t.type;hs(e,t),e=t.pendingProps;var l=Tr(t,De.current);Lr(t,r),l=Sl(null,t,i,e,l,r);var d=yl();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Fe(i)?(d=!0,Rs(t)):d=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,_l(t),l.updater=Qs,t.stateNode=l,l._reactInternals=t,yo(t,i,e,r),t=Eo(null,t,i,!0,d,r)):(t.tag=0,le&&d&&ll(t),Te(null,t,l,r),t=t.child),t;case 16:i=t.elementType;e:{switch(hs(e,t),e=t.pendingProps,l=i._init,i=l(i._payload),t.type=i,l=t.tag=i_(i),e=rt(i,e),l){case 0:t=Co(null,t,i,e,r);break e;case 1:t=Ia(null,t,i,e,r);break e;case 11:t=Pa(null,t,i,e,r);break e;case 14:t=Ma(null,t,i,rt(i.type,e),r);break e}throw Error(U(306,i,""))}return t;case 0:return i=t.type,l=t.pendingProps,l=t.elementType===i?l:rt(i,l),Co(e,t,i,l,r);case 1:return i=t.type,l=t.pendingProps,l=t.elementType===i?l:rt(i,l),Ia(e,t,i,l,r);case 3:e:{if(Vh(t),e===null)throw Error(U(387));i=t.pendingProps,d=t.memoizedState,l=d.element,mh(e,t),Os(t,i,null,r);var m=t.memoizedState;if(i=m.element,d.isDehydrated)if(d={element:i,isDehydrated:!1,cache:m.cache,pendingSuspenseBoundaries:m.pendingSuspenseBoundaries,transitions:m.transitions},t.updateQueue.baseState=d,t.memoizedState=d,t.flags&256){l=Pr(Error(U(423)),t),t=Na(e,t,i,r,l);break e}else if(i!==l){l=Pr(Error(U(424)),t),t=Na(e,t,i,r,l);break e}else for(We=Ft(t.stateNode.containerInfo.firstChild),Ue=t,le=!0,nt=null,r=vh(t,null,i,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(Ar(),i===l){t=xt(e,t,r);break e}Te(e,t,i,r)}t=t.child}return t;case 5:return Sh(t),e===null&&go(t),i=t.type,l=t.pendingProps,d=e!==null?e.memoizedProps:null,m=l.children,uo(i,l)?m=null:d!==null&&uo(i,d)&&(t.flags|=32),$h(e,t),Te(e,t,m,r),t.child;case 6:return e===null&&go(t),null;case 13:return Kh(e,t,r);case 4:return pl(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=Br(t,null,i,r):Te(e,t,i,r),t.child;case 11:return i=t.type,l=t.pendingProps,l=t.elementType===i?l:rt(i,l),Pa(e,t,i,l,r);case 7:return Te(e,t,t.pendingProps,r),t.child;case 8:return Te(e,t,t.pendingProps.children,r),t.child;case 12:return Te(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(i=t.type._context,l=t.pendingProps,d=t.memoizedProps,m=l.value,se(As,i._currentValue),i._currentValue=m,d!==null)if(at(d.value,m)){if(d.children===l.children&&!He.current){t=xt(e,t,r);break e}}else for(d=t.child,d!==null&&(d.return=t);d!==null;){var n=d.dependencies;if(n!==null){m=d.child;for(var c=n.firstContext;c!==null;){if(c.context===i){if(d.tag===1){c=wt(-1,r&-r),c.tag=2;var f=d.updateQueue;if(f!==null){f=f.shared;var S=f.pending;S===null?c.next=c:(c.next=S.next,S.next=c),f.pending=c}}d.lanes|=r,c=d.alternate,c!==null&&(c.lanes|=r),mo(d.return,r,t),n.lanes|=r;break}c=c.next}}else if(d.tag===10)m=d.type===t.type?null:d.child;else if(d.tag===18){if(m=d.return,m===null)throw Error(U(341));m.lanes|=r,n=m.alternate,n!==null&&(n.lanes|=r),mo(m,r,t),m=d.sibling}else m=d.child;if(m!==null)m.return=d;else for(m=d;m!==null;){if(m===t){m=null;break}if(d=m.sibling,d!==null){d.return=m.return,m=d;break}m=m.return}d=m}Te(e,t,l.children,r),t=t.child}return t;case 9:return l=t.type,i=t.pendingProps.children,Lr(t,r),l=Ze(l),i=i(l),t.flags|=1,Te(e,t,i,r),t.child;case 14:return i=t.type,l=rt(i,t.pendingProps),l=rt(i.type,l),Ma(e,t,i,l,r);case 15:return Wh(e,t,t.type,t.pendingProps,r);case 17:return i=t.type,l=t.pendingProps,l=t.elementType===i?l:rt(i,l),hs(e,t),t.tag=1,Fe(i)?(e=!0,Rs(t)):e=!1,Lr(t,r),Fh(t,i,l),yo(t,i,l,r),Eo(null,t,i,!0,e,r);case 19:return qh(e,t,r);case 22:return Uh(e,t,r)}throw Error(U(156,t.tag))};function cu(e,t){return Ic(e,t)}function r_(e,t,r,i){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Qe(e,t,r,i){return new r_(e,t,r,i)}function Tl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function i_(e){if(typeof e=="function")return Tl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Go)return 11;if(e===Yo)return 14}return 2}function Ut(e,t){var r=e.alternate;return r===null?(r=Qe(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function fs(e,t,r,i,l,d){var m=2;if(i=e,typeof e=="function")Tl(e)&&(m=1);else if(typeof e=="string")m=5;else e:switch(e){case dr:return rr(r.children,l,d,t);case Xo:m=8,l|=8;break;case Un:return e=Qe(12,r,t,l|2),e.elementType=Un,e.lanes=d,e;case $n:return e=Qe(13,r,t,l),e.elementType=$n,e.lanes=d,e;case Vn:return e=Qe(19,r,t,l),e.elementType=Vn,e.lanes=d,e;case Sc:return en(r,l,d,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case gc:m=10;break e;case mc:m=9;break e;case Go:m=11;break e;case Yo:m=14;break e;case Tt:m=16,i=null;break e}throw Error(U(130,e==null?e:typeof e,""))}return t=Qe(m,r,t,l),t.elementType=e,t.type=i,t.lanes=d,t}function rr(e,t,r,i){return e=Qe(7,e,i,t),e.lanes=r,e}function en(e,t,r,i){return e=Qe(22,e,i,t),e.elementType=Sc,e.lanes=r,e.stateNode={isHidden:!1},e}function In(e,t,r){return e=Qe(6,e,null,t),e.lanes=r,e}function Nn(e,t,r){return t=Qe(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function s_(e,t,r,i,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gn(0),this.expirationTimes=gn(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gn(0),this.identifierPrefix=i,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function Al(e,t,r,i,l,d,m,n,c){return e=new s_(e,t,r,n,c),t===1?(t=1,d===!0&&(t|=8)):t=0,d=Qe(3,null,null,t),e.current=d,d.stateNode=e,d.memoizedState={element:i,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},_l(d),e}function n_(e,t,r){var i=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ur,key:i==null?null:""+i,children:e,containerInfo:t,implementation:r}}function hu(e){if(!e)return Kt;e=e._reactInternals;e:{if(cr(e)!==e||e.tag!==1)throw Error(U(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Fe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(U(171))}if(e.tag===1){var r=e.type;if(Fe(r))return hh(e,r,t)}return t}function uu(e,t,r,i,l,d,m,n,c){return e=Al(r,i,!0,e,l,d,m,n,c),e.context=hu(null),r=e.current,i=Ae(),l=Wt(r),d=wt(i,l),d.callback=t??null,jt(r,d,l),e.current.lanes=l,Ai(e,l,i),je(e,i),e}function tn(e,t,r,i){var l=t.current,d=Ae(),m=Wt(l);return r=hu(r),t.context===null?t.context=r:t.pendingContext=r,t=wt(d,m),t.payload={element:e},i=i===void 0?null:i,i!==null&&(t.callback=i),e=jt(l,t,m),e!==null&&(lt(e,l,m,d),ls(e,l,m)),m}function zs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function qa(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function Bl(e,t){qa(e,t),(e=e.alternate)&&qa(e,t)}function o_(){return null}var du=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ol(e){this._internalRoot=e}rn.prototype.render=Ol.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(U(409));tn(e,t,null,null)};rn.prototype.unmount=Ol.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;lr(function(){tn(null,e,null,null)}),t[Et]=null}};function rn(e){this._internalRoot=e}rn.prototype.unstable_scheduleHydration=function(e){if(e){var t=Uc();e={blockedOn:null,target:e,priority:t};for(var r=0;r<Bt.length&&t!==0&&t<Bt[r].priority;r++);Bt.splice(r,0,e),r===0&&Vc(e)}};function Pl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function sn(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Xa(){}function l_(e,t,r,i,l){if(l){if(typeof i=="function"){var d=i;i=function(){var f=zs(m);d.call(f)}}var m=uu(t,i,e,0,null,!1,!1,"",Xa);return e._reactRootContainer=m,e[Et]=m.current,wi(e.nodeType===8?e.parentNode:e),lr(),m}for(;l=e.lastChild;)e.removeChild(l);if(typeof i=="function"){var n=i;i=function(){var f=zs(c);n.call(f)}}var c=Al(e,0,!1,null,null,!1,!1,"",Xa);return e._reactRootContainer=c,e[Et]=c.current,wi(e.nodeType===8?e.parentNode:e),lr(function(){tn(t,c,r,i)}),c}function nn(e,t,r,i,l){var d=r._reactRootContainer;if(d){var m=d;if(typeof l=="function"){var n=l;l=function(){var c=zs(m);n.call(c)}}tn(t,m,e,l)}else m=l_(r,t,e,l,i);return zs(m)}zc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=ti(t.pendingLanes);r!==0&&(Zo(t,r|1),je(t,ve()),!(re&6)&&(Mr=ve()+500,Gt()))}break;case 13:lr(function(){var i=kt(e,1);if(i!==null){var l=Ae();lt(i,e,1,l)}}),Bl(e,1)}};el=function(e){if(e.tag===13){var t=kt(e,134217728);if(t!==null){var r=Ae();lt(t,e,134217728,r)}Bl(e,134217728)}};Wc=function(e){if(e.tag===13){var t=Wt(e),r=kt(e,t);if(r!==null){var i=Ae();lt(r,e,t,i)}Bl(e,t)}};Uc=function(){return ie};$c=function(e,t){var r=ie;try{return ie=e,t()}finally{ie=r}};to=function(e,t,r){switch(t){case"input":if(Xn(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var i=r[t];if(i!==e&&i.form===e.form){var l=Xs(i);if(!l)throw Error(U(90));wc(i),Xn(i,l)}}}break;case"textarea":Ec(e,r);break;case"select":t=r.value,t!=null&&Er(e,!!r.multiple,t,!1)}};Tc=Ll;Ac=lr;var a_={usingClientEntryPoint:!1,Events:[Oi,vr,Xs,Rc,Dc,Ll]},Qr={findFiberByHostInstance:Jt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},c_={bundleType:Qr.bundleType,version:Qr.version,rendererPackageName:Qr.rendererPackageName,rendererConfig:Qr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:bt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Pc(e),e===null?null:e.stateNode},findFiberByHostInstance:Qr.findFiberByHostInstance||o_,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Zi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Zi.isDisabled&&Zi.supportsFiber)try{$s=Zi.inject(c_),ft=Zi}catch{}}Ve.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=a_;Ve.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Pl(t))throw Error(U(200));return n_(e,t,null,r)};Ve.createRoot=function(e,t){if(!Pl(e))throw Error(U(299));var r=!1,i="",l=du;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=Al(e,1,!1,null,null,r,!1,i,l),e[Et]=t.current,wi(e.nodeType===8?e.parentNode:e),new Ol(t)};Ve.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(U(188)):(e=Object.keys(e).join(","),Error(U(268,e)));return e=Pc(t),e=e===null?null:e.stateNode,e};Ve.flushSync=function(e){return lr(e)};Ve.hydrate=function(e,t,r){if(!sn(t))throw Error(U(200));return nn(null,e,t,!0,r)};Ve.hydrateRoot=function(e,t,r){if(!Pl(e))throw Error(U(405));var i=r!=null&&r.hydratedSources||null,l=!1,d="",m=du;if(r!=null&&(r.unstable_strictMode===!0&&(l=!0),r.identifierPrefix!==void 0&&(d=r.identifierPrefix),r.onRecoverableError!==void 0&&(m=r.onRecoverableError)),t=uu(t,null,e,1,r??null,l,!1,d,m),e[Et]=t.current,wi(e),i)for(e=0;e<i.length;e++)r=i[e],l=r._getVersion,l=l(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,l]:t.mutableSourceEagerHydrationData.push(r,l);return new rn(t)};Ve.render=function(e,t,r){if(!sn(t))throw Error(U(200));return nn(null,e,t,!1,r)};Ve.unmountComponentAtNode=function(e){if(!sn(e))throw Error(U(40));return e._reactRootContainer?(lr(function(){nn(null,null,e,!1,function(){e._reactRootContainer=null,e[Et]=null})}),!0):!1};Ve.unstable_batchedUpdates=Ll;Ve.unstable_renderSubtreeIntoContainer=function(e,t,r,i){if(!sn(r))throw Error(U(200));if(e==null||e._reactInternals===void 0)throw Error(U(38));return nn(e,t,r,!1,i)};Ve.version="18.3.1-next-f1338f8080-20240426";function fu(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(fu)}catch(e){console.error(e)}}fu(),fc.exports=Ve;var h_=fc.exports,Ga=h_;zn.createRoot=Ga.createRoot,zn.hydrateRoot=Ga.hydrateRoot;const vt=Object.create(null);vt.open="0";vt.close="1";vt.ping="2";vt.pong="3";vt.message="4";vt.upgrade="5";vt.noop="6";const _s=Object.create(null);Object.keys(vt).forEach(e=>{_s[vt[e]]=e});const Mo={type:"error",data:"parser error"},_u=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",pu=typeof ArrayBuffer=="function",vu=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,Ml=({type:e,data:t},r,i)=>_u&&t instanceof Blob?r?i(t):Ya(t,i):pu&&(t instanceof ArrayBuffer||vu(t))?r?i(t):Ya(new Blob([t]),i):i(vt[e]+(t||"")),Ya=(e,t)=>{const r=new FileReader;return r.onload=function(){const i=r.result.split(",")[1];t("b"+(i||""))},r.readAsDataURL(e)};function Qa(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let Hn;function u_(e,t){if(_u&&e.data instanceof Blob)return e.data.arrayBuffer().then(Qa).then(t);if(pu&&(e.data instanceof ArrayBuffer||vu(e.data)))return t(Qa(e.data));Ml(e,!1,r=>{Hn||(Hn=new TextEncoder),t(Hn.encode(r))})}const Ja="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ii=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<Ja.length;e++)ii[Ja.charCodeAt(e)]=e;const d_=e=>{let t=e.length*.75,r=e.length,i,l=0,d,m,n,c;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);const f=new ArrayBuffer(t),S=new Uint8Array(f);for(i=0;i<r;i+=4)d=ii[e.charCodeAt(i)],m=ii[e.charCodeAt(i+1)],n=ii[e.charCodeAt(i+2)],c=ii[e.charCodeAt(i+3)],S[l++]=d<<2|m>>4,S[l++]=(m&15)<<4|n>>2,S[l++]=(n&3)<<6|c&63;return f},f_=typeof ArrayBuffer=="function",Il=(e,t)=>{if(typeof e!="string")return{type:"message",data:gu(e,t)};const r=e.charAt(0);return r==="b"?{type:"message",data:__(e.substring(1),t)}:_s[r]?e.length>1?{type:_s[r],data:e.substring(1)}:{type:_s[r]}:Mo},__=(e,t)=>{if(f_){const r=d_(e);return gu(r,t)}else return{base64:!0,data:e}},gu=(e,t)=>{switch(t){case"blob":return e instanceof Blob?e:new Blob([e]);case"arraybuffer":default:return e instanceof ArrayBuffer?e:e.buffer}},mu="",p_=(e,t)=>{const r=e.length,i=new Array(r);let l=0;e.forEach((d,m)=>{Ml(d,!1,n=>{i[m]=n,++l===r&&t(i.join(mu))})})},v_=(e,t)=>{const r=e.split(mu),i=[];for(let l=0;l<r.length;l++){const d=Il(r[l],t);if(i.push(d),d.type==="error")break}return i};function g_(){return new TransformStream({transform(e,t){u_(e,r=>{const i=r.length;let l;if(i<126)l=new Uint8Array(1),new DataView(l.buffer).setUint8(0,i);else if(i<65536){l=new Uint8Array(3);const d=new DataView(l.buffer);d.setUint8(0,126),d.setUint16(1,i)}else{l=new Uint8Array(9);const d=new DataView(l.buffer);d.setUint8(0,127),d.setBigUint64(1,BigInt(i))}e.data&&typeof e.data!="string"&&(l[0]|=128),t.enqueue(l),t.enqueue(r)})}})}let Fn;function es(e){return e.reduce((t,r)=>t+r.length,0)}function ts(e,t){if(e[0].length===t)return e.shift();const r=new Uint8Array(t);let i=0;for(let l=0;l<t;l++)r[l]=e[0][i++],i===e[0].length&&(e.shift(),i=0);return e.length&&i<e[0].length&&(e[0]=e[0].slice(i)),r}function m_(e,t){Fn||(Fn=new TextDecoder);const r=[];let i=0,l=-1,d=!1;return new TransformStream({transform(m,n){for(r.push(m);;){if(i===0){if(es(r)<1)break;const c=ts(r,1);d=(c[0]&128)===128,l=c[0]&127,l<126?i=3:l===126?i=1:i=2}else if(i===1){if(es(r)<2)break;const c=ts(r,2);l=new DataView(c.buffer,c.byteOffset,c.length).getUint16(0),i=3}else if(i===2){if(es(r)<8)break;const c=ts(r,8),f=new DataView(c.buffer,c.byteOffset,c.length),S=f.getUint32(0);if(S>Math.pow(2,21)-1){n.enqueue(Mo);break}l=S*Math.pow(2,32)+f.getUint32(4),i=3}else{if(es(r)<l)break;const c=ts(r,l);n.enqueue(Il(d?c:Fn.decode(c),t)),i=0}if(l===0||l>e){n.enqueue(Mo);break}}}})}const Su=4;function me(e){if(e)return S_(e)}function S_(e){for(var t in me.prototype)e[t]=me.prototype[t];return e}me.prototype.on=me.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this};me.prototype.once=function(e,t){function r(){this.off(e,r),t.apply(this,arguments)}return r.fn=t,this.on(e,r),this};me.prototype.off=me.prototype.removeListener=me.prototype.removeAllListeners=me.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var r=this._callbacks["$"+e];if(!r)return this;if(arguments.length==1)return delete this._callbacks["$"+e],this;for(var i,l=0;l<r.length;l++)if(i=r[l],i===t||i.fn===t){r.splice(l,1);break}return r.length===0&&delete this._callbacks["$"+e],this};me.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),r=this._callbacks["$"+e],i=1;i<arguments.length;i++)t[i-1]=arguments[i];if(r){r=r.slice(0);for(var i=0,l=r.length;i<l;++i)r[i].apply(this,t)}return this};me.prototype.emitReserved=me.prototype.emit;me.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]};me.prototype.hasListeners=function(e){return!!this.listeners(e).length};const on=typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,r)=>r(t,0),Ye=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),y_="arraybuffer";function yu(e,...t){return t.reduce((r,i)=>(e.hasOwnProperty(i)&&(r[i]=e[i]),r),{})}const w_=Ye.setTimeout,C_=Ye.clearTimeout;function ln(e,t){t.useNativeTimers?(e.setTimeoutFn=w_.bind(Ye),e.clearTimeoutFn=C_.bind(Ye)):(e.setTimeoutFn=Ye.setTimeout.bind(Ye),e.clearTimeoutFn=Ye.clearTimeout.bind(Ye))}const E_=1.33;function k_(e){return typeof e=="string"?x_(e):Math.ceil((e.byteLength||e.size)*E_)}function x_(e){let t=0,r=0;for(let i=0,l=e.length;i<l;i++)t=e.charCodeAt(i),t<128?r+=1:t<2048?r+=2:t<55296||t>=57344?r+=3:(i++,r+=4);return r}function wu(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function b_(e){let t="";for(let r in e)e.hasOwnProperty(r)&&(t.length&&(t+="&"),t+=encodeURIComponent(r)+"="+encodeURIComponent(e[r]));return t}function L_(e){let t={},r=e.split("&");for(let i=0,l=r.length;i<l;i++){let d=r[i].split("=");t[decodeURIComponent(d[0])]=decodeURIComponent(d[1])}return t}class R_ extends Error{constructor(t,r,i){super(t),this.description=r,this.context=i,this.type="TransportError"}}class Nl extends me{constructor(t){super(),this.writable=!1,ln(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,r,i){return super.emitReserved("error",new R_(t,r,i)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const r=Il(t,this.socket.binaryType);this.onPacket(r)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,r={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(r)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const r=b_(t);return r.length?"?"+r:""}}class D_ extends Nl{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";const r=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let i=0;this._polling&&(i++,this.once("pollComplete",function(){--i||r()})),this.writable||(i++,this.once("drain",function(){--i||r()}))}else r()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const r=i=>{if(this.readyState==="opening"&&i.type==="open"&&this.onOpen(),i.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(i)};v_(t,this.socket.binaryType).forEach(r),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,p_(t,r=>{this.doWrite(r,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",r=this.query||{};return this.opts.timestampRequests!==!1&&(r[this.opts.timestampParam]=wu()),!this.supportsBinary&&!r.sid&&(r.b64=1),this.createUri(t,r)}}let Cu=!1;try{Cu=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const T_=Cu;function A_(){}class B_ extends D_{constructor(t){if(super(t),typeof location<"u"){const r=location.protocol==="https:";let i=location.port;i||(i=r?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||i!==t.port}}doWrite(t,r){const i=this.request({method:"POST",data:t});i.on("success",r),i.on("error",(l,d)=>{this.onError("xhr post error",l,d)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(r,i)=>{this.onError("xhr poll error",r,i)}),this.pollXhr=t}}class pt extends me{constructor(t,r,i){super(),this.createRequest=t,ln(this,i),this._opts=i,this._method=i.method||"GET",this._uri=r,this._data=i.data!==void 0?i.data:null,this._create()}_create(){var t;const r=yu(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");r.xdomain=!!this._opts.xd;const i=this._xhr=this.createRequest(r);try{i.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){i.setDisableHeaderCheck&&i.setDisableHeaderCheck(!0);for(let l in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(l)&&i.setRequestHeader(l,this._opts.extraHeaders[l])}}catch{}if(this._method==="POST")try{i.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{i.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(i),"withCredentials"in i&&(i.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(i.timeout=this._opts.requestTimeout),i.onreadystatechange=()=>{var l;i.readyState===3&&((l=this._opts.cookieJar)===null||l===void 0||l.parseCookies(i.getResponseHeader("set-cookie"))),i.readyState===4&&(i.status===200||i.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof i.status=="number"?i.status:0)},0))},i.send(this._data)}catch(l){this.setTimeoutFn(()=>{this._onError(l)},0);return}typeof document<"u"&&(this._index=pt.requestsCount++,pt.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=A_,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete pt.requests[this._index],this._xhr=null}}_onLoad(){const t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}pt.requestsCount=0;pt.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Za);else if(typeof addEventListener=="function"){const e="onpagehide"in Ye?"pagehide":"unload";addEventListener(e,Za,!1)}}function Za(){for(let e in pt.requests)pt.requests.hasOwnProperty(e)&&pt.requests[e].abort()}const O_=function(){const e=Eu({xdomain:!1});return e&&e.responseType!==null}();class P_ extends B_{constructor(t){super(t);const r=t&&t.forceBase64;this.supportsBinary=O_&&!r}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new pt(Eu,this.uri(),t)}}function Eu(e){const t=e.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||T_))return new XMLHttpRequest}catch{}if(!t)try{return new Ye[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const ku=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class M_ extends Nl{get name(){return"websocket"}doOpen(){const t=this.uri(),r=this.opts.protocols,i=ku?{}:yu(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(i.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,r,i)}catch(l){return this.emitReserved("error",l)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let r=0;r<t.length;r++){const i=t[r],l=r===t.length-1;Ml(i,this.supportsBinary,d=>{try{this.doWrite(i,d)}catch{}l&&on(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",r=this.query||{};return this.opts.timestampRequests&&(r[this.opts.timestampParam]=wu()),this.supportsBinary||(r.b64=1),this.createUri(t,r)}}const jn=Ye.WebSocket||Ye.MozWebSocket;class I_ extends M_{createSocket(t,r,i){return ku?new jn(t,r,i):r?new jn(t,r):new jn(t)}doWrite(t,r){this.ws.send(r)}}class N_ extends Nl{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{const r=m_(Number.MAX_SAFE_INTEGER,this.socket.binaryType),i=t.readable.pipeThrough(r).getReader(),l=g_();l.readable.pipeTo(t.writable),this._writer=l.writable.getWriter();const d=()=>{i.read().then(({done:n,value:c})=>{n||(this.onPacket(c),d())}).catch(n=>{})};d();const m={type:"open"};this.query.sid&&(m.data=`{"sid":"${this.query.sid}"}`),this._writer.write(m).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let r=0;r<t.length;r++){const i=t[r],l=r===t.length-1;this._writer.write(i).then(()=>{l&&on(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this._transport)===null||t===void 0||t.close()}}const H_={websocket:I_,webtransport:N_,polling:P_},F_=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,j_=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Io(e){if(e.length>8e3)throw"URI too long";const t=e,r=e.indexOf("["),i=e.indexOf("]");r!=-1&&i!=-1&&(e=e.substring(0,r)+e.substring(r,i).replace(/:/g,";")+e.substring(i,e.length));let l=F_.exec(e||""),d={},m=14;for(;m--;)d[j_[m]]=l[m]||"";return r!=-1&&i!=-1&&(d.source=t,d.host=d.host.substring(1,d.host.length-1).replace(/;/g,":"),d.authority=d.authority.replace("[","").replace("]","").replace(/;/g,":"),d.ipv6uri=!0),d.pathNames=z_(d,d.path),d.queryKey=W_(d,d.query),d}function z_(e,t){const r=/\/{2,9}/g,i=t.replace(r,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&i.splice(0,1),t.slice(-1)=="/"&&i.splice(i.length-1,1),i}function W_(e,t){const r={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(i,l,d){l&&(r[l]=d)}),r}const No=typeof addEventListener=="function"&&typeof removeEventListener=="function",ps=[];No&&addEventListener("offline",()=>{ps.forEach(e=>e())},!1);class $t extends me{constructor(t,r){if(super(),this.binaryType=y_,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(r=t,t=null),t){const i=Io(t);r.hostname=i.host,r.secure=i.protocol==="https"||i.protocol==="wss",r.port=i.port,i.query&&(r.query=i.query)}else r.host&&(r.hostname=Io(r.host).host);ln(this,r),this.secure=r.secure!=null?r.secure:typeof location<"u"&&location.protocol==="https:",r.hostname&&!r.port&&(r.port=this.secure?"443":"80"),this.hostname=r.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=r.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},r.transports.forEach(i=>{const l=i.prototype.name;this.transports.push(l),this._transportsByName[l]=i}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},r),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=L_(this.opts.query)),No&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},ps.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){const r=Object.assign({},this.opts.query);r.EIO=Su,r.transport=t,this.id&&(r.sid=this.id);const i=Object.assign({},this.opts,{query:r,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](i)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const t=this.opts.rememberUpgrade&&$t.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const r=this.createTransport(t);r.open(),this.setTransport(r)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",r=>this._onClose("transport close",r))}onOpen(){this.readyState="open",$t.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const r=new Error("server error");r.code=t.data,this._onError(r);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let r=1;for(let i=0;i<this.writeBuffer.length;i++){const l=this.writeBuffer[i].data;if(l&&(r+=k_(l)),i>0&&r>this._maxPayload)return this.writeBuffer.slice(0,i);r+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,on(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,r,i){return this._sendPacket("message",t,r,i),this}send(t,r,i){return this._sendPacket("message",t,r,i),this}_sendPacket(t,r,i,l){if(typeof r=="function"&&(l=r,r=void 0),typeof i=="function"&&(l=i,i=null),this.readyState==="closing"||this.readyState==="closed")return;i=i||{},i.compress=i.compress!==!1;const d={type:t,data:r,options:i};this.emitReserved("packetCreate",d),this.writeBuffer.push(d),l&&this.once("flush",l),this.flush()}close(){const t=()=>{this._onClose("forced close"),this.transport.close()},r=()=>{this.off("upgrade",r),this.off("upgradeError",r),t()},i=()=>{this.once("upgrade",r),this.once("upgradeError",r)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?i():t()}):this.upgrading?i():t()),this}_onError(t){if($t.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,r){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),No&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const i=ps.indexOf(this._offlineEventListener);i!==-1&&ps.splice(i,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,r),this.writeBuffer=[],this._prevBufferLen=0}}}$t.protocol=Su;class U_ extends $t{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let r=this.createTransport(t),i=!1;$t.priorWebsocketSuccess=!1;const l=()=>{i||(r.send([{type:"ping",data:"probe"}]),r.once("packet",u=>{if(!i)if(u.type==="pong"&&u.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",r),!r)return;$t.priorWebsocketSuccess=r.name==="websocket",this.transport.pause(()=>{i||this.readyState!=="closed"&&(S(),this.setTransport(r),r.send([{type:"upgrade"}]),this.emitReserved("upgrade",r),r=null,this.upgrading=!1,this.flush())})}else{const _=new Error("probe error");_.transport=r.name,this.emitReserved("upgradeError",_)}}))};function d(){i||(i=!0,S(),r.close(),r=null)}const m=u=>{const _=new Error("probe error: "+u);_.transport=r.name,d(),this.emitReserved("upgradeError",_)};function n(){m("transport closed")}function c(){m("socket closed")}function f(u){r&&u.name!==r.name&&d()}const S=()=>{r.removeListener("open",l),r.removeListener("error",m),r.removeListener("close",n),this.off("close",c),this.off("upgrading",f)};r.once("open",l),r.once("error",m),r.once("close",n),this.once("close",c),this.once("upgrading",f),this._upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{i||r.open()},200):r.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){const r=[];for(let i=0;i<t.length;i++)~this.transports.indexOf(t[i])&&r.push(t[i]);return r}}let $_=class extends U_{constructor(t,r={}){const i=typeof t=="object"?t:r;(!i.transports||i.transports&&typeof i.transports[0]=="string")&&(i.transports=(i.transports||["polling","websocket","webtransport"]).map(l=>H_[l]).filter(l=>!!l)),super(t,i)}};function V_(e,t="",r){let i=e;r=r||typeof location<"u"&&location,e==null&&(e=r.protocol+"//"+r.host),typeof e=="string"&&(e.charAt(0)==="/"&&(e.charAt(1)==="/"?e=r.protocol+e:e=r.host+e),/^(https?|wss?):\/\//.test(e)||(typeof r<"u"?e=r.protocol+"//"+e:e="https://"+e),i=Io(e)),i.port||(/^(http|ws)$/.test(i.protocol)?i.port="80":/^(http|ws)s$/.test(i.protocol)&&(i.port="443")),i.path=i.path||"/";const d=i.host.indexOf(":")!==-1?"["+i.host+"]":i.host;return i.id=i.protocol+"://"+d+":"+i.port+t,i.href=i.protocol+"://"+d+(r&&r.port===i.port?"":":"+i.port),i}const K_=typeof ArrayBuffer=="function",q_=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,xu=Object.prototype.toString,X_=typeof Blob=="function"||typeof Blob<"u"&&xu.call(Blob)==="[object BlobConstructor]",G_=typeof File=="function"||typeof File<"u"&&xu.call(File)==="[object FileConstructor]";function Hl(e){return K_&&(e instanceof ArrayBuffer||q_(e))||X_&&e instanceof Blob||G_&&e instanceof File}function vs(e,t){if(!e||typeof e!="object")return!1;if(Array.isArray(e)){for(let r=0,i=e.length;r<i;r++)if(vs(e[r]))return!0;return!1}if(Hl(e))return!0;if(e.toJSON&&typeof e.toJSON=="function"&&arguments.length===1)return vs(e.toJSON(),!0);for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&vs(e[r]))return!0;return!1}function Y_(e){const t=[],r=e.data,i=e;return i.data=Ho(r,t),i.attachments=t.length,{packet:i,buffers:t}}function Ho(e,t){if(!e)return e;if(Hl(e)){const r={_placeholder:!0,num:t.length};return t.push(e),r}else if(Array.isArray(e)){const r=new Array(e.length);for(let i=0;i<e.length;i++)r[i]=Ho(e[i],t);return r}else if(typeof e=="object"&&!(e instanceof Date)){const r={};for(const i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=Ho(e[i],t));return r}return e}function Q_(e,t){return e.data=Fo(e.data,t),delete e.attachments,e}function Fo(e,t){if(!e)return e;if(e&&e._placeholder===!0){if(typeof e.num=="number"&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}else if(Array.isArray(e))for(let r=0;r<e.length;r++)e[r]=Fo(e[r],t);else if(typeof e=="object")for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(e[r]=Fo(e[r],t));return e}const J_=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],Z_=5;var ee;(function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"})(ee||(ee={}));class ep{constructor(t){this.replacer=t}encode(t){return(t.type===ee.EVENT||t.type===ee.ACK)&&vs(t)?this.encodeAsBinary({type:t.type===ee.EVENT?ee.BINARY_EVENT:ee.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let r=""+t.type;return(t.type===ee.BINARY_EVENT||t.type===ee.BINARY_ACK)&&(r+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(r+=t.nsp+","),t.id!=null&&(r+=t.id),t.data!=null&&(r+=JSON.stringify(t.data,this.replacer)),r}encodeAsBinary(t){const r=Y_(t),i=this.encodeAsString(r.packet),l=r.buffers;return l.unshift(i),l}}function ec(e){return Object.prototype.toString.call(e)==="[object Object]"}class Fl extends me{constructor(t){super(),this.reviver=t}add(t){let r;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");r=this.decodeString(t);const i=r.type===ee.BINARY_EVENT;i||r.type===ee.BINARY_ACK?(r.type=i?ee.EVENT:ee.ACK,this.reconstructor=new tp(r),r.attachments===0&&super.emitReserved("decoded",r)):super.emitReserved("decoded",r)}else if(Hl(t)||t.base64)if(this.reconstructor)r=this.reconstructor.takeBinaryData(t),r&&(this.reconstructor=null,super.emitReserved("decoded",r));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let r=0;const i={type:Number(t.charAt(0))};if(ee[i.type]===void 0)throw new Error("unknown packet type "+i.type);if(i.type===ee.BINARY_EVENT||i.type===ee.BINARY_ACK){const d=r+1;for(;t.charAt(++r)!=="-"&&r!=t.length;);const m=t.substring(d,r);if(m!=Number(m)||t.charAt(r)!=="-")throw new Error("Illegal attachments");i.attachments=Number(m)}if(t.charAt(r+1)==="/"){const d=r+1;for(;++r&&!(t.charAt(r)===","||r===t.length););i.nsp=t.substring(d,r)}else i.nsp="/";const l=t.charAt(r+1);if(l!==""&&Number(l)==l){const d=r+1;for(;++r;){const m=t.charAt(r);if(m==null||Number(m)!=m){--r;break}if(r===t.length)break}i.id=Number(t.substring(d,r+1))}if(t.charAt(++r)){const d=this.tryParse(t.substr(r));if(Fl.isPayloadValid(i.type,d))i.data=d;else throw new Error("invalid payload")}return i}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,r){switch(t){case ee.CONNECT:return ec(r);case ee.DISCONNECT:return r===void 0;case ee.CONNECT_ERROR:return typeof r=="string"||ec(r);case ee.EVENT:case ee.BINARY_EVENT:return Array.isArray(r)&&(typeof r[0]=="number"||typeof r[0]=="string"&&J_.indexOf(r[0])===-1);case ee.ACK:case ee.BINARY_ACK:return Array.isArray(r)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class tp{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const r=Q_(this.reconPack,this.buffers);return this.finishedReconstruction(),r}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const rp=Object.freeze(Object.defineProperty({__proto__:null,Decoder:Fl,Encoder:ep,get PacketType(){return ee},protocol:Z_},Symbol.toStringTag,{value:"Module"}));function st(e,t,r){return e.on(t,r),function(){e.off(t,r)}}const ip=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class bu extends me{constructor(t,r,i){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=r,i&&i.auth&&(this.auth=i.auth),this._opts=Object.assign({},i),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[st(t,"open",this.onopen.bind(this)),st(t,"packet",this.onpacket.bind(this)),st(t,"error",this.onerror.bind(this)),st(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...r){var i,l,d;if(ip.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(r.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(r),this;const m={type:ee.EVENT,data:r};if(m.options={},m.options.compress=this.flags.compress!==!1,typeof r[r.length-1]=="function"){const S=this.ids++,u=r.pop();this._registerAckCallback(S,u),m.id=S}const n=(l=(i=this.io.engine)===null||i===void 0?void 0:i.transport)===null||l===void 0?void 0:l.writable,c=this.connected&&!(!((d=this.io.engine)===null||d===void 0)&&d._hasPingExpired());return this.flags.volatile&&!n||(c?(this.notifyOutgoingListeners(m),this.packet(m)):this.sendBuffer.push(m)),this.flags={},this}_registerAckCallback(t,r){var i;const l=(i=this.flags.timeout)!==null&&i!==void 0?i:this._opts.ackTimeout;if(l===void 0){this.acks[t]=r;return}const d=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let n=0;n<this.sendBuffer.length;n++)this.sendBuffer[n].id===t&&this.sendBuffer.splice(n,1);r.call(this,new Error("operation has timed out"))},l),m=(...n)=>{this.io.clearTimeoutFn(d),r.apply(this,n)};m.withError=!0,this.acks[t]=m}emitWithAck(t,...r){return new Promise((i,l)=>{const d=(m,n)=>m?l(m):i(n);d.withError=!0,r.push(d),this.emit(t,...r)})}_addToQueue(t){let r;typeof t[t.length-1]=="function"&&(r=t.pop());const i={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((l,...d)=>i!==this._queue[0]?void 0:(l!==null?i.tryCount>this._opts.retries&&(this._queue.shift(),r&&r(l)):(this._queue.shift(),r&&r(null,...d)),i.pending=!1,this._drainQueue())),this._queue.push(i),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||this._queue.length===0)return;const r=this._queue[0];r.pending&&!t||(r.pending=!0,r.tryCount++,this.flags=r.flags,this.emit.apply(this,r.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:ee.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,r){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,r),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(i=>String(i.id)===t)){const i=this.acks[t];delete this.acks[t],i.withError&&i.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case ee.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case ee.EVENT:case ee.BINARY_EVENT:this.onevent(t);break;case ee.ACK:case ee.BINARY_ACK:this.onack(t);break;case ee.DISCONNECT:this.ondisconnect();break;case ee.CONNECT_ERROR:this.destroy();const i=new Error(t.data.message);i.data=t.data.data,this.emitReserved("connect_error",i);break}}onevent(t){const r=t.data||[];t.id!=null&&r.push(this.ack(t.id)),this.connected?this.emitEvent(r):this.receiveBuffer.push(Object.freeze(r))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const r=this._anyListeners.slice();for(const i of r)i.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const r=this;let i=!1;return function(...l){i||(i=!0,r.packet({type:ee.ACK,id:t,data:l}))}}onack(t){const r=this.acks[t.id];typeof r=="function"&&(delete this.acks[t.id],r.withError&&t.data.unshift(null),r.apply(this,t.data))}onconnect(t,r){this.id=t,this.recovered=r&&this._pid===r,this._pid=r,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:ee.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const r=this._anyListeners;for(let i=0;i<r.length;i++)if(t===r[i])return r.splice(i,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const r=this._anyOutgoingListeners;for(let i=0;i<r.length;i++)if(t===r[i])return r.splice(i,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const r=this._anyOutgoingListeners.slice();for(const i of r)i.apply(this,t.data)}}}function Fr(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}Fr.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),r=Math.floor(t*this.jitter*e);e=Math.floor(t*10)&1?e+r:e-r}return Math.min(e,this.max)|0};Fr.prototype.reset=function(){this.attempts=0};Fr.prototype.setMin=function(e){this.ms=e};Fr.prototype.setMax=function(e){this.max=e};Fr.prototype.setJitter=function(e){this.jitter=e};class jo extends me{constructor(t,r){var i;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(r=t,t=void 0),r=r||{},r.path=r.path||"/socket.io",this.opts=r,ln(this,r),this.reconnection(r.reconnection!==!1),this.reconnectionAttempts(r.reconnectionAttempts||1/0),this.reconnectionDelay(r.reconnectionDelay||1e3),this.reconnectionDelayMax(r.reconnectionDelayMax||5e3),this.randomizationFactor((i=r.randomizationFactor)!==null&&i!==void 0?i:.5),this.backoff=new Fr({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(r.timeout==null?2e4:r.timeout),this._readyState="closed",this.uri=t;const l=r.parser||rp;this.encoder=new l.Encoder,this.decoder=new l.Decoder,this._autoConnect=r.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var r;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(r=this.backoff)===null||r===void 0||r.setMin(t),this)}randomizationFactor(t){var r;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(r=this.backoff)===null||r===void 0||r.setJitter(t),this)}reconnectionDelayMax(t){var r;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(r=this.backoff)===null||r===void 0||r.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new $_(this.uri,this.opts);const r=this.engine,i=this;this._readyState="opening",this.skipReconnect=!1;const l=st(r,"open",function(){i.onopen(),t&&t()}),d=n=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",n),t?t(n):this.maybeReconnectOnOpen()},m=st(r,"error",d);if(this._timeout!==!1){const n=this._timeout,c=this.setTimeoutFn(()=>{l(),d(new Error("timeout")),r.close()},n);this.opts.autoUnref&&c.unref(),this.subs.push(()=>{this.clearTimeoutFn(c)})}return this.subs.push(l),this.subs.push(m),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(st(t,"ping",this.onping.bind(this)),st(t,"data",this.ondata.bind(this)),st(t,"error",this.onerror.bind(this)),st(t,"close",this.onclose.bind(this)),st(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(r){this.onclose("parse error",r)}}ondecoded(t){on(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,r){let i=this.nsps[t];return i?this._autoConnect&&!i.active&&i.connect():(i=new bu(this,t,r),this.nsps[t]=i),i}_destroy(t){const r=Object.keys(this.nsps);for(const i of r)if(this.nsps[i].active)return;this._close()}_packet(t){const r=this.encoder.encode(t);for(let i=0;i<r.length;i++)this.engine.write(r[i],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,r){var i;this.cleanup(),(i=this.engine)===null||i===void 0||i.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,r),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const r=this.backoff.duration();this._reconnecting=!0;const i=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(l=>{l?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",l)):t.onreconnect()}))},r);this.opts.autoUnref&&i.unref(),this.subs.push(()=>{this.clearTimeoutFn(i)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const Jr={};function gs(e,t){typeof e=="object"&&(t=e,e=void 0),t=t||{};const r=V_(e,t.path||"/socket.io"),i=r.source,l=r.id,d=r.path,m=Jr[l]&&d in Jr[l].nsps,n=t.forceNew||t["force new connection"]||t.multiplex===!1||m;let c;return n?c=new jo(i,t):(Jr[l]||(Jr[l]=new jo(i,t)),c=Jr[l]),r.query&&!t.query&&(t.query=r.queryKey),c.socket(r.path,t)}Object.assign(gs,{Manager:jo,Socket:bu,io:gs,connect:gs});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var sp={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const np=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),Pe=(e,t)=>{const r=pe.forwardRef(({color:i="currentColor",size:l=24,strokeWidth:d=2,absoluteStrokeWidth:m,className:n="",children:c,...f},S)=>pe.createElement("svg",{ref:S,...sp,width:l,height:l,stroke:i,strokeWidth:m?Number(d)*24/Number(l):d,className:["lucide",`lucide-${np(e)}`,n].join(" "),...f},[...t.map(([u,_])=>pe.createElement(u,_)),...Array.isArray(c)?c:[c]]));return r.displayName=`${e}`,r};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const op=Pe("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lu=Pe("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lp=Pe("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ap=Pe("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cp=Pe("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hp=Pe("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const up=Pe("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dp=Pe("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ru=Pe("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fp=Pe("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tc=Pe("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jl=Pe("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ws=Pe("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _p=Pe("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Du=Pe("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);function pp({programs:e,terminals:t,onStartProgram:r,onStopProgram:i,onViewTerminal:l}){const d=n=>t.find(c=>c.programId===n&&c.status==="running"),m=n=>{const c=new Date(n),S=Math.floor((new Date-c)/1e3);return S<60?`${S}s`:S<3600?`${Math.floor(S/60)}m`:`${Math.floor(S/3600)}h ${Math.floor(S%3600/60)}m`};return I.jsxs("div",{children:[I.jsxs("div",{className:"flex items-center justify-between mb-6",children:[I.jsx("h2",{className:"text-xl font-semibold",children:"Available Programs"}),I.jsxs("div",{className:"text-sm text-terminal-muted",children:[e.length," programs configured"]})]}),I.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(n=>{const c=d(n.id),f=!!c;return I.jsxs("div",{className:`program-card ${f?"running":""}`,children:[I.jsxs("div",{className:"flex items-start justify-between mb-4",children:[I.jsxs("div",{className:"flex items-center gap-3",children:[I.jsx("div",{className:"p-3 rounded-lg text-2xl",style:{backgroundColor:`${n.color}20`,color:n.color},children:n.icon}),I.jsxs("div",{children:[I.jsx("h3",{className:"font-semibold text-lg",children:n.name}),I.jsx("p",{className:"text-sm text-terminal-muted capitalize",children:n.type})]})]}),I.jsx("div",{className:"flex items-center gap-2",children:f?I.jsx("div",{className:"status-dot status-running"}):I.jsx("div",{className:"status-dot status-stopped"})})]}),I.jsx("p",{className:"text-terminal-muted text-sm mb-4 leading-relaxed",children:n.description}),f&&I.jsx("div",{className:"bg-terminal-bg rounded-lg p-3 mb-4 border border-green-500/20",children:I.jsxs("div",{className:"flex items-center justify-between text-sm",children:[I.jsxs("div",{className:"flex items-center gap-2",children:[I.jsx(Lu,{className:"w-4 h-4 text-green-400"}),I.jsx("span",{className:"text-terminal-muted",children:"Uptime:"}),I.jsx("span",{className:"text-green-400 font-medium",children:m(c.startTime)})]}),I.jsxs("div",{className:"flex items-center gap-2",children:[I.jsx(Du,{className:"w-4 h-4 text-blue-400"}),I.jsxs("span",{className:"text-blue-400 font-medium",children:["PID: ",c.pid]})]})]})}),I.jsx("div",{className:"flex items-center gap-2",children:f?I.jsxs(I.Fragment,{children:[I.jsxs("button",{onClick:()=>i&&i(c.id),className:"btn-danger flex-1 flex items-center justify-center gap-2",title:"Stop Program",children:[I.jsx(jl,{className:"w-4 h-4"}),"Stop"]}),I.jsx("button",{className:"btn-primary px-4",onClick:()=>l&&l(c.id),title:"View Terminal",children:"View"})]}):I.jsxs("button",{onClick:()=>r(n.id),className:"btn-primary w-full flex items-center justify-center gap-2",title:"Start Program",children:[I.jsx(Ru,{className:"w-4 h-4"}),"Start Program"]})}),I.jsx("div",{className:"mt-3 pt-3 border-t border-terminal-border",children:I.jsxs("div",{className:"flex items-center justify-between text-xs text-terminal-muted",children:[I.jsxs("span",{children:["Command: ",n.command," ",n.args.join(" ")]}),I.jsxs("span",{children:["Dir: ",n.directory]})]})})]},n.id)})}),e.length===0&&I.jsxs("div",{className:"text-center py-12",children:[I.jsx("div",{className:"text-6xl mb-4",children:"🤖"}),I.jsx("h3",{className:"text-xl font-semibold mb-2",children:"No Programs Configured"}),I.jsx("p",{className:"text-terminal-muted",children:"Add your testnet programs to the configuration to get started."})]})]})}function vp({terminals:e,activeTerminal:t,onSelectTerminal:r,onCloseTerminal:i,onStopTerminal:l}){const d=m=>{const n=new Date(m),f=Math.floor((new Date-n)/1e3);return f<60?`${f}s`:f<3600?`${Math.floor(f/60)}m`:`${Math.floor(f/3600)}h ${Math.floor(f%3600/60)}m`};return e.length===0?I.jsxs("div",{className:"bg-terminal-surface border border-terminal-border rounded-lg p-8 text-center",children:[I.jsx("div",{className:"text-6xl mb-4",children:"💻"}),I.jsx("h3",{className:"text-xl font-semibold mb-2",children:"No Active Terminals"}),I.jsx("p",{className:"text-terminal-muted",children:"Start a program from the dashboard to create a terminal session."})]}):I.jsxs("div",{className:"bg-terminal-surface border border-terminal-border rounded-lg",children:[I.jsx("div",{className:"flex items-center border-b border-terminal-border overflow-x-auto",children:e.map(m=>I.jsxs("div",{className:`flex items-center gap-2 px-4 py-3 border-r border-terminal-border cursor-pointer transition-all min-w-0 ${t===m.id?"bg-terminal-bg border-b-2 border-b-blue-500":"hover:bg-terminal-bg/50"}`,onClick:()=>r(m.id),children:[I.jsxs("div",{className:"flex items-center gap-2 min-w-0 flex-1",children:[I.jsx("div",{className:`status-dot ${m.status==="running"?"status-running":"status-stopped"}`}),I.jsxs("div",{className:"min-w-0 flex-1",children:[I.jsx("div",{className:"font-medium text-sm truncate",children:m.programName}),I.jsxs("div",{className:"text-xs text-terminal-muted flex items-center gap-1",children:[I.jsx(Lu,{className:"w-3 h-3"}),m.status==="running"?d(m.startTime):`Stopped ${m.exitCode!==void 0?`(${m.exitCode})`:""}`]})]})]}),I.jsxs("div",{className:"flex items-center gap-1",children:[m.status==="running"&&I.jsx("button",{onClick:n=>{n.stopPropagation(),l(m.id)},className:"p-1 hover:bg-red-500/20 rounded text-red-400 hover:text-red-300 transition-colors",title:"Stop program",children:I.jsx(jl,{className:"w-3 h-3"})}),I.jsx("button",{onClick:n=>{n.stopPropagation(),i(m.id)},className:"p-1 hover:bg-terminal-border rounded text-terminal-muted hover:text-terminal-text transition-colors",title:"Close terminal",children:I.jsx(_p,{className:"w-3 h-3"})})]})]},m.id))}),I.jsx("div",{className:"p-4 bg-terminal-bg/30",children:I.jsxs("div",{className:"flex items-center justify-between text-sm",children:[I.jsxs("div",{className:"flex items-center gap-4",children:[I.jsxs("span",{className:"text-terminal-muted",children:["Active Terminals: ",I.jsx("span",{className:"text-terminal-text font-medium",children:e.length})]}),I.jsxs("span",{className:"text-terminal-muted",children:["Running: ",I.jsx("span",{className:"text-green-400 font-medium",children:e.filter(m=>m.status==="running").length})]})]}),I.jsxs("div",{className:"flex items-center gap-2",children:[I.jsx("button",{onClick:()=>{e.forEach(m=>{m.status==="running"&&l(m.id)})},className:"btn-danger text-xs px-3 py-1",disabled:e.filter(m=>m.status==="running").length===0,children:"Stop All"}),I.jsx("button",{onClick:()=>{e.forEach(m=>{i(m.id)})},className:"btn-secondary text-xs px-3 py-1",children:"Close All"})]})]})})]})}var Tu={exports:{}};(function(e,t){(function(r,i){e.exports=i()})(globalThis,()=>(()=>{var r={4567:function(m,n,c){var f=this&&this.__decorate||function(s,o,p,w){var E,k=arguments.length,y=k<3?o:w===null?w=Object.getOwnPropertyDescriptor(o,p):w;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")y=Reflect.decorate(s,o,p,w);else for(var b=s.length-1;b>=0;b--)(E=s[b])&&(y=(k<3?E(y):k>3?E(o,p,y):E(o,p))||y);return k>3&&y&&Object.defineProperty(o,p,y),y},S=this&&this.__param||function(s,o){return function(p,w){o(p,w,s)}};Object.defineProperty(n,"__esModule",{value:!0}),n.AccessibilityManager=void 0;const u=c(9042),_=c(9924),g=c(844),C=c(4725),v=c(2585),h=c(3656);let a=n.AccessibilityManager=class extends g.Disposable{constructor(s,o,p,w){super(),this._terminal=s,this._coreBrowserService=p,this._renderService=w,this._rowColumns=new WeakMap,this._liveRegionLineCount=0,this._charsToConsume=[],this._charsToAnnounce="",this._accessibilityContainer=this._coreBrowserService.mainDocument.createElement("div"),this._accessibilityContainer.classList.add("xterm-accessibility"),this._rowContainer=this._coreBrowserService.mainDocument.createElement("div"),this._rowContainer.setAttribute("role","list"),this._rowContainer.classList.add("xterm-accessibility-tree"),this._rowElements=[];for(let E=0;E<this._terminal.rows;E++)this._rowElements[E]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[E]);if(this._topBoundaryFocusListener=E=>this._handleBoundaryFocus(E,0),this._bottomBoundaryFocusListener=E=>this._handleBoundaryFocus(E,1),this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions(),this._accessibilityContainer.appendChild(this._rowContainer),this._liveRegion=this._coreBrowserService.mainDocument.createElement("div"),this._liveRegion.classList.add("live-region"),this._liveRegion.setAttribute("aria-live","assertive"),this._accessibilityContainer.appendChild(this._liveRegion),this._liveRegionDebouncer=this.register(new _.TimeBasedDebouncer(this._renderRows.bind(this))),!this._terminal.element)throw new Error("Cannot enable accessibility before Terminal.open");this._terminal.element.insertAdjacentElement("afterbegin",this._accessibilityContainer),this.register(this._terminal.onResize(E=>this._handleResize(E.rows))),this.register(this._terminal.onRender(E=>this._refreshRows(E.start,E.end))),this.register(this._terminal.onScroll(()=>this._refreshRows())),this.register(this._terminal.onA11yChar(E=>this._handleChar(E))),this.register(this._terminal.onLineFeed(()=>this._handleChar(`
`))),this.register(this._terminal.onA11yTab(E=>this._handleTab(E))),this.register(this._terminal.onKey(E=>this._handleKey(E.key))),this.register(this._terminal.onBlur(()=>this._clearLiveRegion())),this.register(this._renderService.onDimensionsChange(()=>this._refreshRowsDimensions())),this.register((0,h.addDisposableDomListener)(document,"selectionchange",()=>this._handleSelectionChange())),this.register(this._coreBrowserService.onDprChange(()=>this._refreshRowsDimensions())),this._refreshRows(),this.register((0,g.toDisposable)(()=>{this._accessibilityContainer.remove(),this._rowElements.length=0}))}_handleTab(s){for(let o=0;o<s;o++)this._handleChar(" ")}_handleChar(s){this._liveRegionLineCount<21&&(this._charsToConsume.length>0?this._charsToConsume.shift()!==s&&(this._charsToAnnounce+=s):this._charsToAnnounce+=s,s===`
`&&(this._liveRegionLineCount++,this._liveRegionLineCount===21&&(this._liveRegion.textContent+=u.tooMuchOutput)))}_clearLiveRegion(){this._liveRegion.textContent="",this._liveRegionLineCount=0}_handleKey(s){this._clearLiveRegion(),new RegExp("\\p{Control}","u").test(s)||this._charsToConsume.push(s)}_refreshRows(s,o){this._liveRegionDebouncer.refresh(s,o,this._terminal.rows)}_renderRows(s,o){const p=this._terminal.buffer,w=p.lines.length.toString();for(let E=s;E<=o;E++){const k=p.lines.get(p.ydisp+E),y=[],b=(k==null?void 0:k.translateToString(!0,void 0,void 0,y))||"",D=(p.ydisp+E+1).toString(),B=this._rowElements[E];B&&(b.length===0?(B.innerText=" ",this._rowColumns.set(B,[0,1])):(B.textContent=b,this._rowColumns.set(B,y)),B.setAttribute("aria-posinset",D),B.setAttribute("aria-setsize",w))}this._announceCharacters()}_announceCharacters(){this._charsToAnnounce.length!==0&&(this._liveRegion.textContent+=this._charsToAnnounce,this._charsToAnnounce="")}_handleBoundaryFocus(s,o){const p=s.target,w=this._rowElements[o===0?1:this._rowElements.length-2];if(p.getAttribute("aria-posinset")===(o===0?"1":`${this._terminal.buffer.lines.length}`)||s.relatedTarget!==w)return;let E,k;if(o===0?(E=p,k=this._rowElements.pop(),this._rowContainer.removeChild(k)):(E=this._rowElements.shift(),k=p,this._rowContainer.removeChild(E)),E.removeEventListener("focus",this._topBoundaryFocusListener),k.removeEventListener("focus",this._bottomBoundaryFocusListener),o===0){const y=this._createAccessibilityTreeNode();this._rowElements.unshift(y),this._rowContainer.insertAdjacentElement("afterbegin",y)}else{const y=this._createAccessibilityTreeNode();this._rowElements.push(y),this._rowContainer.appendChild(y)}this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._terminal.scrollLines(o===0?-1:1),this._rowElements[o===0?1:this._rowElements.length-2].focus(),s.preventDefault(),s.stopImmediatePropagation()}_handleSelectionChange(){var b;if(this._rowElements.length===0)return;const s=document.getSelection();if(!s)return;if(s.isCollapsed)return void(this._rowContainer.contains(s.anchorNode)&&this._terminal.clearSelection());if(!s.anchorNode||!s.focusNode)return void console.error("anchorNode and/or focusNode are null");let o={node:s.anchorNode,offset:s.anchorOffset},p={node:s.focusNode,offset:s.focusOffset};if((o.node.compareDocumentPosition(p.node)&Node.DOCUMENT_POSITION_PRECEDING||o.node===p.node&&o.offset>p.offset)&&([o,p]=[p,o]),o.node.compareDocumentPosition(this._rowElements[0])&(Node.DOCUMENT_POSITION_CONTAINED_BY|Node.DOCUMENT_POSITION_FOLLOWING)&&(o={node:this._rowElements[0].childNodes[0],offset:0}),!this._rowContainer.contains(o.node))return;const w=this._rowElements.slice(-1)[0];if(p.node.compareDocumentPosition(w)&(Node.DOCUMENT_POSITION_CONTAINED_BY|Node.DOCUMENT_POSITION_PRECEDING)&&(p={node:w,offset:((b=w.textContent)==null?void 0:b.length)??0}),!this._rowContainer.contains(p.node))return;const E=({node:D,offset:B})=>{const O=D instanceof Text?D.parentNode:D;let T=parseInt(O==null?void 0:O.getAttribute("aria-posinset"),10)-1;if(isNaN(T))return console.warn("row is invalid. Race condition?"),null;const H=this._rowColumns.get(O);if(!H)return console.warn("columns is null. Race condition?"),null;let j=B<H.length?H[B]:H.slice(-1)[0]+1;return j>=this._terminal.cols&&(++T,j=0),{row:T,column:j}},k=E(o),y=E(p);if(k&&y){if(k.row>y.row||k.row===y.row&&k.column>=y.column)throw new Error("invalid range");this._terminal.select(k.column,k.row,(y.row-k.row)*this._terminal.cols-k.column+y.column)}}_handleResize(s){this._rowElements[this._rowElements.length-1].removeEventListener("focus",this._bottomBoundaryFocusListener);for(let o=this._rowContainer.children.length;o<this._terminal.rows;o++)this._rowElements[o]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[o]);for(;this._rowElements.length>s;)this._rowContainer.removeChild(this._rowElements.pop());this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions()}_createAccessibilityTreeNode(){const s=this._coreBrowserService.mainDocument.createElement("div");return s.setAttribute("role","listitem"),s.tabIndex=-1,this._refreshRowDimensions(s),s}_refreshRowsDimensions(){if(this._renderService.dimensions.css.cell.height){this._accessibilityContainer.style.width=`${this._renderService.dimensions.css.canvas.width}px`,this._rowElements.length!==this._terminal.rows&&this._handleResize(this._terminal.rows);for(let s=0;s<this._terminal.rows;s++)this._refreshRowDimensions(this._rowElements[s])}}_refreshRowDimensions(s){s.style.height=`${this._renderService.dimensions.css.cell.height}px`}};n.AccessibilityManager=a=f([S(1,v.IInstantiationService),S(2,C.ICoreBrowserService),S(3,C.IRenderService)],a)},3614:(m,n)=>{function c(_){return _.replace(/\r?\n/g,"\r")}function f(_,g){return g?"\x1B[200~"+_+"\x1B[201~":_}function S(_,g,C,v){_=f(_=c(_),C.decPrivateModes.bracketedPasteMode&&v.rawOptions.ignoreBracketedPasteMode!==!0),C.triggerDataEvent(_,!0),g.value=""}function u(_,g,C){const v=C.getBoundingClientRect(),h=_.clientX-v.left-10,a=_.clientY-v.top-10;g.style.width="20px",g.style.height="20px",g.style.left=`${h}px`,g.style.top=`${a}px`,g.style.zIndex="1000",g.focus()}Object.defineProperty(n,"__esModule",{value:!0}),n.rightClickHandler=n.moveTextAreaUnderMouseCursor=n.paste=n.handlePasteEvent=n.copyHandler=n.bracketTextForPaste=n.prepareTextForTerminal=void 0,n.prepareTextForTerminal=c,n.bracketTextForPaste=f,n.copyHandler=function(_,g){_.clipboardData&&_.clipboardData.setData("text/plain",g.selectionText),_.preventDefault()},n.handlePasteEvent=function(_,g,C,v){_.stopPropagation(),_.clipboardData&&S(_.clipboardData.getData("text/plain"),g,C,v)},n.paste=S,n.moveTextAreaUnderMouseCursor=u,n.rightClickHandler=function(_,g,C,v,h){u(_,g,C),h&&v.rightClickSelect(_),g.value=v.selectionText,g.select()}},7239:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.ColorContrastCache=void 0;const f=c(1505);n.ColorContrastCache=class{constructor(){this._color=new f.TwoKeyMap,this._css=new f.TwoKeyMap}setCss(S,u,_){this._css.set(S,u,_)}getCss(S,u){return this._css.get(S,u)}setColor(S,u,_){this._color.set(S,u,_)}getColor(S,u){return this._color.get(S,u)}clear(){this._color.clear(),this._css.clear()}}},3656:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.addDisposableDomListener=void 0,n.addDisposableDomListener=function(c,f,S,u){c.addEventListener(f,S,u);let _=!1;return{dispose:()=>{_||(_=!0,c.removeEventListener(f,S,u))}}}},3551:function(m,n,c){var f=this&&this.__decorate||function(a,s,o,p){var w,E=arguments.length,k=E<3?s:p===null?p=Object.getOwnPropertyDescriptor(s,o):p;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")k=Reflect.decorate(a,s,o,p);else for(var y=a.length-1;y>=0;y--)(w=a[y])&&(k=(E<3?w(k):E>3?w(s,o,k):w(s,o))||k);return E>3&&k&&Object.defineProperty(s,o,k),k},S=this&&this.__param||function(a,s){return function(o,p){s(o,p,a)}};Object.defineProperty(n,"__esModule",{value:!0}),n.Linkifier=void 0;const u=c(3656),_=c(8460),g=c(844),C=c(2585),v=c(4725);let h=n.Linkifier=class extends g.Disposable{get currentLink(){return this._currentLink}constructor(a,s,o,p,w){super(),this._element=a,this._mouseService=s,this._renderService=o,this._bufferService=p,this._linkProviderService=w,this._linkCacheDisposables=[],this._isMouseOut=!0,this._wasResized=!1,this._activeLine=-1,this._onShowLinkUnderline=this.register(new _.EventEmitter),this.onShowLinkUnderline=this._onShowLinkUnderline.event,this._onHideLinkUnderline=this.register(new _.EventEmitter),this.onHideLinkUnderline=this._onHideLinkUnderline.event,this.register((0,g.getDisposeArrayDisposable)(this._linkCacheDisposables)),this.register((0,g.toDisposable)(()=>{var E;this._lastMouseEvent=void 0,(E=this._activeProviderReplies)==null||E.clear()})),this.register(this._bufferService.onResize(()=>{this._clearCurrentLink(),this._wasResized=!0})),this.register((0,u.addDisposableDomListener)(this._element,"mouseleave",()=>{this._isMouseOut=!0,this._clearCurrentLink()})),this.register((0,u.addDisposableDomListener)(this._element,"mousemove",this._handleMouseMove.bind(this))),this.register((0,u.addDisposableDomListener)(this._element,"mousedown",this._handleMouseDown.bind(this))),this.register((0,u.addDisposableDomListener)(this._element,"mouseup",this._handleMouseUp.bind(this)))}_handleMouseMove(a){this._lastMouseEvent=a;const s=this._positionFromMouseEvent(a,this._element,this._mouseService);if(!s)return;this._isMouseOut=!1;const o=a.composedPath();for(let p=0;p<o.length;p++){const w=o[p];if(w.classList.contains("xterm"))break;if(w.classList.contains("xterm-hover"))return}this._lastBufferCell&&s.x===this._lastBufferCell.x&&s.y===this._lastBufferCell.y||(this._handleHover(s),this._lastBufferCell=s)}_handleHover(a){if(this._activeLine!==a.y||this._wasResized)return this._clearCurrentLink(),this._askForLink(a,!1),void(this._wasResized=!1);this._currentLink&&this._linkAtPosition(this._currentLink.link,a)||(this._clearCurrentLink(),this._askForLink(a,!0))}_askForLink(a,s){var p,w;this._activeProviderReplies&&s||((p=this._activeProviderReplies)==null||p.forEach(E=>{E==null||E.forEach(k=>{k.link.dispose&&k.link.dispose()})}),this._activeProviderReplies=new Map,this._activeLine=a.y);let o=!1;for(const[E,k]of this._linkProviderService.linkProviders.entries())s?(w=this._activeProviderReplies)!=null&&w.get(E)&&(o=this._checkLinkProviderResult(E,a,o)):k.provideLinks(a.y,y=>{var D,B;if(this._isMouseOut)return;const b=y==null?void 0:y.map(O=>({link:O}));(D=this._activeProviderReplies)==null||D.set(E,b),o=this._checkLinkProviderResult(E,a,o),((B=this._activeProviderReplies)==null?void 0:B.size)===this._linkProviderService.linkProviders.length&&this._removeIntersectingLinks(a.y,this._activeProviderReplies)})}_removeIntersectingLinks(a,s){const o=new Set;for(let p=0;p<s.size;p++){const w=s.get(p);if(w)for(let E=0;E<w.length;E++){const k=w[E],y=k.link.range.start.y<a?0:k.link.range.start.x,b=k.link.range.end.y>a?this._bufferService.cols:k.link.range.end.x;for(let D=y;D<=b;D++){if(o.has(D)){w.splice(E--,1);break}o.add(D)}}}}_checkLinkProviderResult(a,s,o){var E;if(!this._activeProviderReplies)return o;const p=this._activeProviderReplies.get(a);let w=!1;for(let k=0;k<a;k++)this._activeProviderReplies.has(k)&&!this._activeProviderReplies.get(k)||(w=!0);if(!w&&p){const k=p.find(y=>this._linkAtPosition(y.link,s));k&&(o=!0,this._handleNewLink(k))}if(this._activeProviderReplies.size===this._linkProviderService.linkProviders.length&&!o)for(let k=0;k<this._activeProviderReplies.size;k++){const y=(E=this._activeProviderReplies.get(k))==null?void 0:E.find(b=>this._linkAtPosition(b.link,s));if(y){o=!0,this._handleNewLink(y);break}}return o}_handleMouseDown(){this._mouseDownLink=this._currentLink}_handleMouseUp(a){if(!this._currentLink)return;const s=this._positionFromMouseEvent(a,this._element,this._mouseService);s&&this._mouseDownLink===this._currentLink&&this._linkAtPosition(this._currentLink.link,s)&&this._currentLink.link.activate(a,this._currentLink.link.text)}_clearCurrentLink(a,s){this._currentLink&&this._lastMouseEvent&&(!a||!s||this._currentLink.link.range.start.y>=a&&this._currentLink.link.range.end.y<=s)&&(this._linkLeave(this._element,this._currentLink.link,this._lastMouseEvent),this._currentLink=void 0,(0,g.disposeArray)(this._linkCacheDisposables))}_handleNewLink(a){if(!this._lastMouseEvent)return;const s=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);s&&this._linkAtPosition(a.link,s)&&(this._currentLink=a,this._currentLink.state={decorations:{underline:a.link.decorations===void 0||a.link.decorations.underline,pointerCursor:a.link.decorations===void 0||a.link.decorations.pointerCursor},isHovered:!0},this._linkHover(this._element,a.link,this._lastMouseEvent),a.link.decorations={},Object.defineProperties(a.link.decorations,{pointerCursor:{get:()=>{var o,p;return(p=(o=this._currentLink)==null?void 0:o.state)==null?void 0:p.decorations.pointerCursor},set:o=>{var p;(p=this._currentLink)!=null&&p.state&&this._currentLink.state.decorations.pointerCursor!==o&&(this._currentLink.state.decorations.pointerCursor=o,this._currentLink.state.isHovered&&this._element.classList.toggle("xterm-cursor-pointer",o))}},underline:{get:()=>{var o,p;return(p=(o=this._currentLink)==null?void 0:o.state)==null?void 0:p.decorations.underline},set:o=>{var p,w,E;(p=this._currentLink)!=null&&p.state&&((E=(w=this._currentLink)==null?void 0:w.state)==null?void 0:E.decorations.underline)!==o&&(this._currentLink.state.decorations.underline=o,this._currentLink.state.isHovered&&this._fireUnderlineEvent(a.link,o))}}}),this._linkCacheDisposables.push(this._renderService.onRenderedViewportChange(o=>{if(!this._currentLink)return;const p=o.start===0?0:o.start+1+this._bufferService.buffer.ydisp,w=this._bufferService.buffer.ydisp+1+o.end;if(this._currentLink.link.range.start.y>=p&&this._currentLink.link.range.end.y<=w&&(this._clearCurrentLink(p,w),this._lastMouseEvent)){const E=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);E&&this._askForLink(E,!1)}})))}_linkHover(a,s,o){var p;(p=this._currentLink)!=null&&p.state&&(this._currentLink.state.isHovered=!0,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(s,!0),this._currentLink.state.decorations.pointerCursor&&a.classList.add("xterm-cursor-pointer")),s.hover&&s.hover(o,s.text)}_fireUnderlineEvent(a,s){const o=a.range,p=this._bufferService.buffer.ydisp,w=this._createLinkUnderlineEvent(o.start.x-1,o.start.y-p-1,o.end.x,o.end.y-p-1,void 0);(s?this._onShowLinkUnderline:this._onHideLinkUnderline).fire(w)}_linkLeave(a,s,o){var p;(p=this._currentLink)!=null&&p.state&&(this._currentLink.state.isHovered=!1,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(s,!1),this._currentLink.state.decorations.pointerCursor&&a.classList.remove("xterm-cursor-pointer")),s.leave&&s.leave(o,s.text)}_linkAtPosition(a,s){const o=a.range.start.y*this._bufferService.cols+a.range.start.x,p=a.range.end.y*this._bufferService.cols+a.range.end.x,w=s.y*this._bufferService.cols+s.x;return o<=w&&w<=p}_positionFromMouseEvent(a,s,o){const p=o.getCoords(a,s,this._bufferService.cols,this._bufferService.rows);if(p)return{x:p[0],y:p[1]+this._bufferService.buffer.ydisp}}_createLinkUnderlineEvent(a,s,o,p,w){return{x1:a,y1:s,x2:o,y2:p,cols:this._bufferService.cols,fg:w}}};n.Linkifier=h=f([S(1,v.IMouseService),S(2,v.IRenderService),S(3,C.IBufferService),S(4,v.ILinkProviderService)],h)},9042:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.tooMuchOutput=n.promptLabel=void 0,n.promptLabel="Terminal input",n.tooMuchOutput="Too much output to announce, navigate to rows manually to read"},3730:function(m,n,c){var f=this&&this.__decorate||function(v,h,a,s){var o,p=arguments.length,w=p<3?h:s===null?s=Object.getOwnPropertyDescriptor(h,a):s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")w=Reflect.decorate(v,h,a,s);else for(var E=v.length-1;E>=0;E--)(o=v[E])&&(w=(p<3?o(w):p>3?o(h,a,w):o(h,a))||w);return p>3&&w&&Object.defineProperty(h,a,w),w},S=this&&this.__param||function(v,h){return function(a,s){h(a,s,v)}};Object.defineProperty(n,"__esModule",{value:!0}),n.OscLinkProvider=void 0;const u=c(511),_=c(2585);let g=n.OscLinkProvider=class{constructor(v,h,a){this._bufferService=v,this._optionsService=h,this._oscLinkService=a}provideLinks(v,h){var b;const a=this._bufferService.buffer.lines.get(v-1);if(!a)return void h(void 0);const s=[],o=this._optionsService.rawOptions.linkHandler,p=new u.CellData,w=a.getTrimmedLength();let E=-1,k=-1,y=!1;for(let D=0;D<w;D++)if(k!==-1||a.hasContent(D)){if(a.loadCell(D,p),p.hasExtendedAttrs()&&p.extended.urlId){if(k===-1){k=D,E=p.extended.urlId;continue}y=p.extended.urlId!==E}else k!==-1&&(y=!0);if(y||k!==-1&&D===w-1){const B=(b=this._oscLinkService.getLinkData(E))==null?void 0:b.uri;if(B){const O={start:{x:k+1,y:v},end:{x:D+(y||D!==w-1?0:1),y:v}};let T=!1;if(!(o!=null&&o.allowNonHttpProtocols))try{const H=new URL(B);["http:","https:"].includes(H.protocol)||(T=!0)}catch{T=!0}T||s.push({text:B,range:O,activate:(H,j)=>o?o.activate(H,j,O):C(0,j),hover:(H,j)=>{var $;return($=o==null?void 0:o.hover)==null?void 0:$.call(o,H,j,O)},leave:(H,j)=>{var $;return($=o==null?void 0:o.leave)==null?void 0:$.call(o,H,j,O)}})}y=!1,p.hasExtendedAttrs()&&p.extended.urlId?(k=D,E=p.extended.urlId):(k=-1,E=-1)}}h(s)}};function C(v,h){if(confirm(`Do you want to navigate to ${h}?

WARNING: This link could potentially be dangerous`)){const a=window.open();if(a){try{a.opener=null}catch{}a.location.href=h}else console.warn("Opening link blocked as opener could not be cleared")}}n.OscLinkProvider=g=f([S(0,_.IBufferService),S(1,_.IOptionsService),S(2,_.IOscLinkService)],g)},6193:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.RenderDebouncer=void 0,n.RenderDebouncer=class{constructor(c,f){this._renderCallback=c,this._coreBrowserService=f,this._refreshCallbacks=[]}dispose(){this._animationFrame&&(this._coreBrowserService.window.cancelAnimationFrame(this._animationFrame),this._animationFrame=void 0)}addRefreshCallback(c){return this._refreshCallbacks.push(c),this._animationFrame||(this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh())),this._animationFrame}refresh(c,f,S){this._rowCount=S,c=c!==void 0?c:0,f=f!==void 0?f:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,c):c,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,f):f,this._animationFrame||(this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._animationFrame=void 0,this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return void this._runRefreshCallbacks();const c=Math.max(this._rowStart,0),f=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(c,f),this._runRefreshCallbacks()}_runRefreshCallbacks(){for(const c of this._refreshCallbacks)c(0);this._refreshCallbacks=[]}}},3236:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.Terminal=void 0;const f=c(3614),S=c(3656),u=c(3551),_=c(9042),g=c(3730),C=c(1680),v=c(3107),h=c(5744),a=c(2950),s=c(1296),o=c(428),p=c(4269),w=c(5114),E=c(8934),k=c(3230),y=c(9312),b=c(4725),D=c(6731),B=c(8055),O=c(8969),T=c(8460),H=c(844),j=c(6114),$=c(8437),q=c(2584),N=c(7399),x=c(5941),R=c(9074),A=c(2585),P=c(5435),z=c(4567),V=c(779);class Y extends O.CoreTerminal{get onFocus(){return this._onFocus.event}get onBlur(){return this._onBlur.event}get onA11yChar(){return this._onA11yCharEmitter.event}get onA11yTab(){return this._onA11yTabEmitter.event}get onWillOpen(){return this._onWillOpen.event}constructor(M={}){super(M),this.browser=j,this._keyDownHandled=!1,this._keyDownSeen=!1,this._keyPressHandled=!1,this._unprocessedDeadKey=!1,this._accessibilityManager=this.register(new H.MutableDisposable),this._onCursorMove=this.register(new T.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onKey=this.register(new T.EventEmitter),this.onKey=this._onKey.event,this._onRender=this.register(new T.EventEmitter),this.onRender=this._onRender.event,this._onSelectionChange=this.register(new T.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onTitleChange=this.register(new T.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onBell=this.register(new T.EventEmitter),this.onBell=this._onBell.event,this._onFocus=this.register(new T.EventEmitter),this._onBlur=this.register(new T.EventEmitter),this._onA11yCharEmitter=this.register(new T.EventEmitter),this._onA11yTabEmitter=this.register(new T.EventEmitter),this._onWillOpen=this.register(new T.EventEmitter),this._setup(),this._decorationService=this._instantiationService.createInstance(R.DecorationService),this._instantiationService.setService(A.IDecorationService,this._decorationService),this._linkProviderService=this._instantiationService.createInstance(V.LinkProviderService),this._instantiationService.setService(b.ILinkProviderService,this._linkProviderService),this._linkProviderService.registerLinkProvider(this._instantiationService.createInstance(g.OscLinkProvider)),this.register(this._inputHandler.onRequestBell(()=>this._onBell.fire())),this.register(this._inputHandler.onRequestRefreshRows((L,W)=>this.refresh(L,W))),this.register(this._inputHandler.onRequestSendFocus(()=>this._reportFocus())),this.register(this._inputHandler.onRequestReset(()=>this.reset())),this.register(this._inputHandler.onRequestWindowsOptionsReport(L=>this._reportWindowsOptions(L))),this.register(this._inputHandler.onColor(L=>this._handleColorEvent(L))),this.register((0,T.forwardEvent)(this._inputHandler.onCursorMove,this._onCursorMove)),this.register((0,T.forwardEvent)(this._inputHandler.onTitleChange,this._onTitleChange)),this.register((0,T.forwardEvent)(this._inputHandler.onA11yChar,this._onA11yCharEmitter)),this.register((0,T.forwardEvent)(this._inputHandler.onA11yTab,this._onA11yTabEmitter)),this.register(this._bufferService.onResize(L=>this._afterResize(L.cols,L.rows))),this.register((0,H.toDisposable)(()=>{var L,W;this._customKeyEventHandler=void 0,(W=(L=this.element)==null?void 0:L.parentNode)==null||W.removeChild(this.element)}))}_handleColorEvent(M){if(this._themeService)for(const L of M){let W,F="";switch(L.index){case 256:W="foreground",F="10";break;case 257:W="background",F="11";break;case 258:W="cursor",F="12";break;default:W="ansi",F="4;"+L.index}switch(L.type){case 0:const X=B.color.toColorRGB(W==="ansi"?this._themeService.colors.ansi[L.index]:this._themeService.colors[W]);this.coreService.triggerDataEvent(`${q.C0.ESC}]${F};${(0,x.toRgbString)(X)}${q.C1_ESCAPED.ST}`);break;case 1:if(W==="ansi")this._themeService.modifyColors(K=>K.ansi[L.index]=B.channels.toColor(...L.color));else{const K=W;this._themeService.modifyColors(te=>te[K]=B.channels.toColor(...L.color))}break;case 2:this._themeService.restoreColor(L.index)}}}_setup(){super._setup(),this._customKeyEventHandler=void 0}get buffer(){return this.buffers.active}focus(){this.textarea&&this.textarea.focus({preventScroll:!0})}_handleScreenReaderModeOptionChange(M){M?!this._accessibilityManager.value&&this._renderService&&(this._accessibilityManager.value=this._instantiationService.createInstance(z.AccessibilityManager,this)):this._accessibilityManager.clear()}_handleTextAreaFocus(M){this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(q.C0.ESC+"[I"),this.element.classList.add("focus"),this._showCursor(),this._onFocus.fire()}blur(){var M;return(M=this.textarea)==null?void 0:M.blur()}_handleTextAreaBlur(){this.textarea.value="",this.refresh(this.buffer.y,this.buffer.y),this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(q.C0.ESC+"[O"),this.element.classList.remove("focus"),this._onBlur.fire()}_syncTextArea(){if(!this.textarea||!this.buffer.isCursorInViewport||this._compositionHelper.isComposing||!this._renderService)return;const M=this.buffer.ybase+this.buffer.y,L=this.buffer.lines.get(M);if(!L)return;const W=Math.min(this.buffer.x,this.cols-1),F=this._renderService.dimensions.css.cell.height,X=L.getWidth(W),K=this._renderService.dimensions.css.cell.width*X,te=this.buffer.y*this._renderService.dimensions.css.cell.height,de=W*this._renderService.dimensions.css.cell.width;this.textarea.style.left=de+"px",this.textarea.style.top=te+"px",this.textarea.style.width=K+"px",this.textarea.style.height=F+"px",this.textarea.style.lineHeight=F+"px",this.textarea.style.zIndex="-5"}_initGlobal(){this._bindKeys(),this.register((0,S.addDisposableDomListener)(this.element,"copy",L=>{this.hasSelection()&&(0,f.copyHandler)(L,this._selectionService)}));const M=L=>(0,f.handlePasteEvent)(L,this.textarea,this.coreService,this.optionsService);this.register((0,S.addDisposableDomListener)(this.textarea,"paste",M)),this.register((0,S.addDisposableDomListener)(this.element,"paste",M)),j.isFirefox?this.register((0,S.addDisposableDomListener)(this.element,"mousedown",L=>{L.button===2&&(0,f.rightClickHandler)(L,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})):this.register((0,S.addDisposableDomListener)(this.element,"contextmenu",L=>{(0,f.rightClickHandler)(L,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})),j.isLinux&&this.register((0,S.addDisposableDomListener)(this.element,"auxclick",L=>{L.button===1&&(0,f.moveTextAreaUnderMouseCursor)(L,this.textarea,this.screenElement)}))}_bindKeys(){this.register((0,S.addDisposableDomListener)(this.textarea,"keyup",M=>this._keyUp(M),!0)),this.register((0,S.addDisposableDomListener)(this.textarea,"keydown",M=>this._keyDown(M),!0)),this.register((0,S.addDisposableDomListener)(this.textarea,"keypress",M=>this._keyPress(M),!0)),this.register((0,S.addDisposableDomListener)(this.textarea,"compositionstart",()=>this._compositionHelper.compositionstart())),this.register((0,S.addDisposableDomListener)(this.textarea,"compositionupdate",M=>this._compositionHelper.compositionupdate(M))),this.register((0,S.addDisposableDomListener)(this.textarea,"compositionend",()=>this._compositionHelper.compositionend())),this.register((0,S.addDisposableDomListener)(this.textarea,"input",M=>this._inputEvent(M),!0)),this.register(this.onRender(()=>this._compositionHelper.updateCompositionElements()))}open(M){var W;if(!M)throw new Error("Terminal requires a parent element.");if(M.isConnected||this._logService.debug("Terminal.open was called on an element that was not attached to the DOM"),((W=this.element)==null?void 0:W.ownerDocument.defaultView)&&this._coreBrowserService)return void(this.element.ownerDocument.defaultView!==this._coreBrowserService.window&&(this._coreBrowserService.window=this.element.ownerDocument.defaultView));this._document=M.ownerDocument,this.options.documentOverride&&this.options.documentOverride instanceof Document&&(this._document=this.optionsService.rawOptions.documentOverride),this.element=this._document.createElement("div"),this.element.dir="ltr",this.element.classList.add("terminal"),this.element.classList.add("xterm"),M.appendChild(this.element);const L=this._document.createDocumentFragment();this._viewportElement=this._document.createElement("div"),this._viewportElement.classList.add("xterm-viewport"),L.appendChild(this._viewportElement),this._viewportScrollArea=this._document.createElement("div"),this._viewportScrollArea.classList.add("xterm-scroll-area"),this._viewportElement.appendChild(this._viewportScrollArea),this.screenElement=this._document.createElement("div"),this.screenElement.classList.add("xterm-screen"),this.register((0,S.addDisposableDomListener)(this.screenElement,"mousemove",F=>this.updateCursorStyle(F))),this._helperContainer=this._document.createElement("div"),this._helperContainer.classList.add("xterm-helpers"),this.screenElement.appendChild(this._helperContainer),L.appendChild(this.screenElement),this.textarea=this._document.createElement("textarea"),this.textarea.classList.add("xterm-helper-textarea"),this.textarea.setAttribute("aria-label",_.promptLabel),j.isChromeOS||this.textarea.setAttribute("aria-multiline","false"),this.textarea.setAttribute("autocorrect","off"),this.textarea.setAttribute("autocapitalize","off"),this.textarea.setAttribute("spellcheck","false"),this.textarea.tabIndex=0,this._coreBrowserService=this.register(this._instantiationService.createInstance(w.CoreBrowserService,this.textarea,M.ownerDocument.defaultView??window,this._document??typeof window<"u"?window.document:null)),this._instantiationService.setService(b.ICoreBrowserService,this._coreBrowserService),this.register((0,S.addDisposableDomListener)(this.textarea,"focus",F=>this._handleTextAreaFocus(F))),this.register((0,S.addDisposableDomListener)(this.textarea,"blur",()=>this._handleTextAreaBlur())),this._helperContainer.appendChild(this.textarea),this._charSizeService=this._instantiationService.createInstance(o.CharSizeService,this._document,this._helperContainer),this._instantiationService.setService(b.ICharSizeService,this._charSizeService),this._themeService=this._instantiationService.createInstance(D.ThemeService),this._instantiationService.setService(b.IThemeService,this._themeService),this._characterJoinerService=this._instantiationService.createInstance(p.CharacterJoinerService),this._instantiationService.setService(b.ICharacterJoinerService,this._characterJoinerService),this._renderService=this.register(this._instantiationService.createInstance(k.RenderService,this.rows,this.screenElement)),this._instantiationService.setService(b.IRenderService,this._renderService),this.register(this._renderService.onRenderedViewportChange(F=>this._onRender.fire(F))),this.onResize(F=>this._renderService.resize(F.cols,F.rows)),this._compositionView=this._document.createElement("div"),this._compositionView.classList.add("composition-view"),this._compositionHelper=this._instantiationService.createInstance(a.CompositionHelper,this.textarea,this._compositionView),this._helperContainer.appendChild(this._compositionView),this._mouseService=this._instantiationService.createInstance(E.MouseService),this._instantiationService.setService(b.IMouseService,this._mouseService),this.linkifier=this.register(this._instantiationService.createInstance(u.Linkifier,this.screenElement)),this.element.appendChild(L);try{this._onWillOpen.fire(this.element)}catch{}this._renderService.hasRenderer()||this._renderService.setRenderer(this._createRenderer()),this.viewport=this._instantiationService.createInstance(C.Viewport,this._viewportElement,this._viewportScrollArea),this.viewport.onRequestScrollLines(F=>this.scrollLines(F.amount,F.suppressScrollEvent,1)),this.register(this._inputHandler.onRequestSyncScrollBar(()=>this.viewport.syncScrollArea())),this.register(this.viewport),this.register(this.onCursorMove(()=>{this._renderService.handleCursorMove(),this._syncTextArea()})),this.register(this.onResize(()=>this._renderService.handleResize(this.cols,this.rows))),this.register(this.onBlur(()=>this._renderService.handleBlur())),this.register(this.onFocus(()=>this._renderService.handleFocus())),this.register(this._renderService.onDimensionsChange(()=>this.viewport.syncScrollArea())),this._selectionService=this.register(this._instantiationService.createInstance(y.SelectionService,this.element,this.screenElement,this.linkifier)),this._instantiationService.setService(b.ISelectionService,this._selectionService),this.register(this._selectionService.onRequestScrollLines(F=>this.scrollLines(F.amount,F.suppressScrollEvent))),this.register(this._selectionService.onSelectionChange(()=>this._onSelectionChange.fire())),this.register(this._selectionService.onRequestRedraw(F=>this._renderService.handleSelectionChanged(F.start,F.end,F.columnSelectMode))),this.register(this._selectionService.onLinuxMouseSelection(F=>{this.textarea.value=F,this.textarea.focus(),this.textarea.select()})),this.register(this._onScroll.event(F=>{this.viewport.syncScrollArea(),this._selectionService.refresh()})),this.register((0,S.addDisposableDomListener)(this._viewportElement,"scroll",()=>this._selectionService.refresh())),this.register(this._instantiationService.createInstance(v.BufferDecorationRenderer,this.screenElement)),this.register((0,S.addDisposableDomListener)(this.element,"mousedown",F=>this._selectionService.handleMouseDown(F))),this.coreMouseService.areMouseEventsActive?(this._selectionService.disable(),this.element.classList.add("enable-mouse-events")):this._selectionService.enable(),this.options.screenReaderMode&&(this._accessibilityManager.value=this._instantiationService.createInstance(z.AccessibilityManager,this)),this.register(this.optionsService.onSpecificOptionChange("screenReaderMode",F=>this._handleScreenReaderModeOptionChange(F))),this.options.overviewRulerWidth&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(h.OverviewRulerRenderer,this._viewportElement,this.screenElement))),this.optionsService.onSpecificOptionChange("overviewRulerWidth",F=>{!this._overviewRulerRenderer&&F&&this._viewportElement&&this.screenElement&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(h.OverviewRulerRenderer,this._viewportElement,this.screenElement)))}),this._charSizeService.measure(),this.refresh(0,this.rows-1),this._initGlobal(),this.bindMouse()}_createRenderer(){return this._instantiationService.createInstance(s.DomRenderer,this,this._document,this.element,this.screenElement,this._viewportElement,this._helperContainer,this.linkifier)}bindMouse(){const M=this,L=this.element;function W(K){const te=M._mouseService.getMouseReportCoords(K,M.screenElement);if(!te)return!1;let de,fe;switch(K.overrideType||K.type){case"mousemove":fe=32,K.buttons===void 0?(de=3,K.button!==void 0&&(de=K.button<3?K.button:3)):de=1&K.buttons?0:4&K.buttons?1:2&K.buttons?2:3;break;case"mouseup":fe=0,de=K.button<3?K.button:3;break;case"mousedown":fe=1,de=K.button<3?K.button:3;break;case"wheel":if(M._customWheelEventHandler&&M._customWheelEventHandler(K)===!1||M.viewport.getLinesScrolled(K)===0)return!1;fe=K.deltaY<0?0:1,de=4;break;default:return!1}return!(fe===void 0||de===void 0||de>4)&&M.coreMouseService.triggerMouseEvent({col:te.col,row:te.row,x:te.x,y:te.y,button:de,action:fe,ctrl:K.ctrlKey,alt:K.altKey,shift:K.shiftKey})}const F={mouseup:null,wheel:null,mousedrag:null,mousemove:null},X={mouseup:K=>(W(K),K.buttons||(this._document.removeEventListener("mouseup",F.mouseup),F.mousedrag&&this._document.removeEventListener("mousemove",F.mousedrag)),this.cancel(K)),wheel:K=>(W(K),this.cancel(K,!0)),mousedrag:K=>{K.buttons&&W(K)},mousemove:K=>{K.buttons||W(K)}};this.register(this.coreMouseService.onProtocolChange(K=>{K?(this.optionsService.rawOptions.logLevel==="debug"&&this._logService.debug("Binding to mouse events:",this.coreMouseService.explainEvents(K)),this.element.classList.add("enable-mouse-events"),this._selectionService.disable()):(this._logService.debug("Unbinding from mouse events."),this.element.classList.remove("enable-mouse-events"),this._selectionService.enable()),8&K?F.mousemove||(L.addEventListener("mousemove",X.mousemove),F.mousemove=X.mousemove):(L.removeEventListener("mousemove",F.mousemove),F.mousemove=null),16&K?F.wheel||(L.addEventListener("wheel",X.wheel,{passive:!1}),F.wheel=X.wheel):(L.removeEventListener("wheel",F.wheel),F.wheel=null),2&K?F.mouseup||(F.mouseup=X.mouseup):(this._document.removeEventListener("mouseup",F.mouseup),F.mouseup=null),4&K?F.mousedrag||(F.mousedrag=X.mousedrag):(this._document.removeEventListener("mousemove",F.mousedrag),F.mousedrag=null)})),this.coreMouseService.activeProtocol=this.coreMouseService.activeProtocol,this.register((0,S.addDisposableDomListener)(L,"mousedown",K=>{if(K.preventDefault(),this.focus(),this.coreMouseService.areMouseEventsActive&&!this._selectionService.shouldForceSelection(K))return W(K),F.mouseup&&this._document.addEventListener("mouseup",F.mouseup),F.mousedrag&&this._document.addEventListener("mousemove",F.mousedrag),this.cancel(K)})),this.register((0,S.addDisposableDomListener)(L,"wheel",K=>{if(!F.wheel){if(this._customWheelEventHandler&&this._customWheelEventHandler(K)===!1)return!1;if(!this.buffer.hasScrollback){const te=this.viewport.getLinesScrolled(K);if(te===0)return;const de=q.C0.ESC+(this.coreService.decPrivateModes.applicationCursorKeys?"O":"[")+(K.deltaY<0?"A":"B");let fe="";for(let Me=0;Me<Math.abs(te);Me++)fe+=de;return this.coreService.triggerDataEvent(fe,!0),this.cancel(K,!0)}return this.viewport.handleWheel(K)?this.cancel(K):void 0}},{passive:!1})),this.register((0,S.addDisposableDomListener)(L,"touchstart",K=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchStart(K),this.cancel(K)},{passive:!0})),this.register((0,S.addDisposableDomListener)(L,"touchmove",K=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchMove(K)?void 0:this.cancel(K)},{passive:!1}))}refresh(M,L){var W;(W=this._renderService)==null||W.refreshRows(M,L)}updateCursorStyle(M){var L;(L=this._selectionService)!=null&&L.shouldColumnSelect(M)?this.element.classList.add("column-select"):this.element.classList.remove("column-select")}_showCursor(){this.coreService.isCursorInitialized||(this.coreService.isCursorInitialized=!0,this.refresh(this.buffer.y,this.buffer.y))}scrollLines(M,L,W=0){var F;W===1?(super.scrollLines(M,L,W),this.refresh(0,this.rows-1)):(F=this.viewport)==null||F.scrollLines(M)}paste(M){(0,f.paste)(M,this.textarea,this.coreService,this.optionsService)}attachCustomKeyEventHandler(M){this._customKeyEventHandler=M}attachCustomWheelEventHandler(M){this._customWheelEventHandler=M}registerLinkProvider(M){return this._linkProviderService.registerLinkProvider(M)}registerCharacterJoiner(M){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");const L=this._characterJoinerService.register(M);return this.refresh(0,this.rows-1),L}deregisterCharacterJoiner(M){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");this._characterJoinerService.deregister(M)&&this.refresh(0,this.rows-1)}get markers(){return this.buffer.markers}registerMarker(M){return this.buffer.addMarker(this.buffer.ybase+this.buffer.y+M)}registerDecoration(M){return this._decorationService.registerDecoration(M)}hasSelection(){return!!this._selectionService&&this._selectionService.hasSelection}select(M,L,W){this._selectionService.setSelection(M,L,W)}getSelection(){return this._selectionService?this._selectionService.selectionText:""}getSelectionPosition(){if(this._selectionService&&this._selectionService.hasSelection)return{start:{x:this._selectionService.selectionStart[0],y:this._selectionService.selectionStart[1]},end:{x:this._selectionService.selectionEnd[0],y:this._selectionService.selectionEnd[1]}}}clearSelection(){var M;(M=this._selectionService)==null||M.clearSelection()}selectAll(){var M;(M=this._selectionService)==null||M.selectAll()}selectLines(M,L){var W;(W=this._selectionService)==null||W.selectLines(M,L)}_keyDown(M){if(this._keyDownHandled=!1,this._keyDownSeen=!0,this._customKeyEventHandler&&this._customKeyEventHandler(M)===!1)return!1;const L=this.browser.isMac&&this.options.macOptionIsMeta&&M.altKey;if(!L&&!this._compositionHelper.keydown(M))return this.options.scrollOnUserInput&&this.buffer.ybase!==this.buffer.ydisp&&this.scrollToBottom(),!1;L||M.key!=="Dead"&&M.key!=="AltGraph"||(this._unprocessedDeadKey=!0);const W=(0,N.evaluateKeyboardEvent)(M,this.coreService.decPrivateModes.applicationCursorKeys,this.browser.isMac,this.options.macOptionIsMeta);if(this.updateCursorStyle(M),W.type===3||W.type===2){const F=this.rows-1;return this.scrollLines(W.type===2?-F:F),this.cancel(M,!0)}return W.type===1&&this.selectAll(),!!this._isThirdLevelShift(this.browser,M)||(W.cancel&&this.cancel(M,!0),!W.key||!!(M.key&&!M.ctrlKey&&!M.altKey&&!M.metaKey&&M.key.length===1&&M.key.charCodeAt(0)>=65&&M.key.charCodeAt(0)<=90)||(this._unprocessedDeadKey?(this._unprocessedDeadKey=!1,!0):(W.key!==q.C0.ETX&&W.key!==q.C0.CR||(this.textarea.value=""),this._onKey.fire({key:W.key,domEvent:M}),this._showCursor(),this.coreService.triggerDataEvent(W.key,!0),!this.optionsService.rawOptions.screenReaderMode||M.altKey||M.ctrlKey?this.cancel(M,!0):void(this._keyDownHandled=!0))))}_isThirdLevelShift(M,L){const W=M.isMac&&!this.options.macOptionIsMeta&&L.altKey&&!L.ctrlKey&&!L.metaKey||M.isWindows&&L.altKey&&L.ctrlKey&&!L.metaKey||M.isWindows&&L.getModifierState("AltGraph");return L.type==="keypress"?W:W&&(!L.keyCode||L.keyCode>47)}_keyUp(M){this._keyDownSeen=!1,this._customKeyEventHandler&&this._customKeyEventHandler(M)===!1||(function(L){return L.keyCode===16||L.keyCode===17||L.keyCode===18}(M)||this.focus(),this.updateCursorStyle(M),this._keyPressHandled=!1)}_keyPress(M){let L;if(this._keyPressHandled=!1,this._keyDownHandled||this._customKeyEventHandler&&this._customKeyEventHandler(M)===!1)return!1;if(this.cancel(M),M.charCode)L=M.charCode;else if(M.which===null||M.which===void 0)L=M.keyCode;else{if(M.which===0||M.charCode===0)return!1;L=M.which}return!(!L||(M.altKey||M.ctrlKey||M.metaKey)&&!this._isThirdLevelShift(this.browser,M)||(L=String.fromCharCode(L),this._onKey.fire({key:L,domEvent:M}),this._showCursor(),this.coreService.triggerDataEvent(L,!0),this._keyPressHandled=!0,this._unprocessedDeadKey=!1,0))}_inputEvent(M){if(M.data&&M.inputType==="insertText"&&(!M.composed||!this._keyDownSeen)&&!this.optionsService.rawOptions.screenReaderMode){if(this._keyPressHandled)return!1;this._unprocessedDeadKey=!1;const L=M.data;return this.coreService.triggerDataEvent(L,!0),this.cancel(M),!0}return!1}resize(M,L){M!==this.cols||L!==this.rows?super.resize(M,L):this._charSizeService&&!this._charSizeService.hasValidSize&&this._charSizeService.measure()}_afterResize(M,L){var W,F;(W=this._charSizeService)==null||W.measure(),(F=this.viewport)==null||F.syncScrollArea(!0)}clear(){var M;if(this.buffer.ybase!==0||this.buffer.y!==0){this.buffer.clearAllMarkers(),this.buffer.lines.set(0,this.buffer.lines.get(this.buffer.ybase+this.buffer.y)),this.buffer.lines.length=1,this.buffer.ydisp=0,this.buffer.ybase=0,this.buffer.y=0;for(let L=1;L<this.rows;L++)this.buffer.lines.push(this.buffer.getBlankLine($.DEFAULT_ATTR_DATA));this._onScroll.fire({position:this.buffer.ydisp,source:0}),(M=this.viewport)==null||M.reset(),this.refresh(0,this.rows-1)}}reset(){var L,W;this.options.rows=this.rows,this.options.cols=this.cols;const M=this._customKeyEventHandler;this._setup(),super.reset(),(L=this._selectionService)==null||L.reset(),this._decorationService.reset(),(W=this.viewport)==null||W.reset(),this._customKeyEventHandler=M,this.refresh(0,this.rows-1)}clearTextureAtlas(){var M;(M=this._renderService)==null||M.clearTextureAtlas()}_reportFocus(){var M;(M=this.element)!=null&&M.classList.contains("focus")?this.coreService.triggerDataEvent(q.C0.ESC+"[I"):this.coreService.triggerDataEvent(q.C0.ESC+"[O")}_reportWindowsOptions(M){if(this._renderService)switch(M){case P.WindowsOptionsReportType.GET_WIN_SIZE_PIXELS:const L=this._renderService.dimensions.css.canvas.width.toFixed(0),W=this._renderService.dimensions.css.canvas.height.toFixed(0);this.coreService.triggerDataEvent(`${q.C0.ESC}[4;${W};${L}t`);break;case P.WindowsOptionsReportType.GET_CELL_SIZE_PIXELS:const F=this._renderService.dimensions.css.cell.width.toFixed(0),X=this._renderService.dimensions.css.cell.height.toFixed(0);this.coreService.triggerDataEvent(`${q.C0.ESC}[6;${X};${F}t`)}}cancel(M,L){if(this.options.cancelEvents||L)return M.preventDefault(),M.stopPropagation(),!1}}n.Terminal=Y},9924:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.TimeBasedDebouncer=void 0,n.TimeBasedDebouncer=class{constructor(c,f=1e3){this._renderCallback=c,this._debounceThresholdMS=f,this._lastRefreshMs=0,this._additionalRefreshRequested=!1}dispose(){this._refreshTimeoutID&&clearTimeout(this._refreshTimeoutID)}refresh(c,f,S){this._rowCount=S,c=c!==void 0?c:0,f=f!==void 0?f:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,c):c,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,f):f;const u=Date.now();if(u-this._lastRefreshMs>=this._debounceThresholdMS)this._lastRefreshMs=u,this._innerRefresh();else if(!this._additionalRefreshRequested){const _=u-this._lastRefreshMs,g=this._debounceThresholdMS-_;this._additionalRefreshRequested=!0,this._refreshTimeoutID=window.setTimeout(()=>{this._lastRefreshMs=Date.now(),this._innerRefresh(),this._additionalRefreshRequested=!1,this._refreshTimeoutID=void 0},g)}}_innerRefresh(){if(this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return;const c=Math.max(this._rowStart,0),f=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(c,f)}}},1680:function(m,n,c){var f=this&&this.__decorate||function(a,s,o,p){var w,E=arguments.length,k=E<3?s:p===null?p=Object.getOwnPropertyDescriptor(s,o):p;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")k=Reflect.decorate(a,s,o,p);else for(var y=a.length-1;y>=0;y--)(w=a[y])&&(k=(E<3?w(k):E>3?w(s,o,k):w(s,o))||k);return E>3&&k&&Object.defineProperty(s,o,k),k},S=this&&this.__param||function(a,s){return function(o,p){s(o,p,a)}};Object.defineProperty(n,"__esModule",{value:!0}),n.Viewport=void 0;const u=c(3656),_=c(4725),g=c(8460),C=c(844),v=c(2585);let h=n.Viewport=class extends C.Disposable{constructor(a,s,o,p,w,E,k,y){super(),this._viewportElement=a,this._scrollArea=s,this._bufferService=o,this._optionsService=p,this._charSizeService=w,this._renderService=E,this._coreBrowserService=k,this.scrollBarWidth=0,this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._wheelPartialScroll=0,this._refreshAnimationFrame=null,this._ignoreNextScrollEvent=!1,this._smoothScrollState={startTime:0,origin:-1,target:-1},this._onRequestScrollLines=this.register(new g.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this.scrollBarWidth=this._viewportElement.offsetWidth-this._scrollArea.offsetWidth||15,this.register((0,u.addDisposableDomListener)(this._viewportElement,"scroll",this._handleScroll.bind(this))),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(b=>this._activeBuffer=b.activeBuffer)),this._renderDimensions=this._renderService.dimensions,this.register(this._renderService.onDimensionsChange(b=>this._renderDimensions=b)),this._handleThemeChange(y.colors),this.register(y.onChangeColors(b=>this._handleThemeChange(b))),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.syncScrollArea())),setTimeout(()=>this.syncScrollArea())}_handleThemeChange(a){this._viewportElement.style.backgroundColor=a.background.css}reset(){this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._coreBrowserService.window.requestAnimationFrame(()=>this.syncScrollArea())}_refresh(a){if(a)return this._innerRefresh(),void(this._refreshAnimationFrame!==null&&this._coreBrowserService.window.cancelAnimationFrame(this._refreshAnimationFrame));this._refreshAnimationFrame===null&&(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._charSizeService.height>0){this._currentRowHeight=this._renderDimensions.device.cell.height/this._coreBrowserService.dpr,this._currentDeviceCellHeight=this._renderDimensions.device.cell.height,this._lastRecordedViewportHeight=this._viewportElement.offsetHeight;const s=Math.round(this._currentRowHeight*this._lastRecordedBufferLength)+(this._lastRecordedViewportHeight-this._renderDimensions.css.canvas.height);this._lastRecordedBufferHeight!==s&&(this._lastRecordedBufferHeight=s,this._scrollArea.style.height=this._lastRecordedBufferHeight+"px")}const a=this._bufferService.buffer.ydisp*this._currentRowHeight;this._viewportElement.scrollTop!==a&&(this._ignoreNextScrollEvent=!0,this._viewportElement.scrollTop=a),this._refreshAnimationFrame=null}syncScrollArea(a=!1){if(this._lastRecordedBufferLength!==this._bufferService.buffer.lines.length)return this._lastRecordedBufferLength=this._bufferService.buffer.lines.length,void this._refresh(a);this._lastRecordedViewportHeight===this._renderService.dimensions.css.canvas.height&&this._lastScrollTop===this._activeBuffer.ydisp*this._currentRowHeight&&this._renderDimensions.device.cell.height===this._currentDeviceCellHeight||this._refresh(a)}_handleScroll(a){if(this._lastScrollTop=this._viewportElement.scrollTop,!this._viewportElement.offsetParent)return;if(this._ignoreNextScrollEvent)return this._ignoreNextScrollEvent=!1,void this._onRequestScrollLines.fire({amount:0,suppressScrollEvent:!0});const s=Math.round(this._lastScrollTop/this._currentRowHeight)-this._bufferService.buffer.ydisp;this._onRequestScrollLines.fire({amount:s,suppressScrollEvent:!0})}_smoothScroll(){if(this._isDisposed||this._smoothScrollState.origin===-1||this._smoothScrollState.target===-1)return;const a=this._smoothScrollPercent();this._viewportElement.scrollTop=this._smoothScrollState.origin+Math.round(a*(this._smoothScrollState.target-this._smoothScrollState.origin)),a<1?this._coreBrowserService.window.requestAnimationFrame(()=>this._smoothScroll()):this._clearSmoothScrollState()}_smoothScrollPercent(){return this._optionsService.rawOptions.smoothScrollDuration&&this._smoothScrollState.startTime?Math.max(Math.min((Date.now()-this._smoothScrollState.startTime)/this._optionsService.rawOptions.smoothScrollDuration,1),0):1}_clearSmoothScrollState(){this._smoothScrollState.startTime=0,this._smoothScrollState.origin=-1,this._smoothScrollState.target=-1}_bubbleScroll(a,s){const o=this._viewportElement.scrollTop+this._lastRecordedViewportHeight;return!(s<0&&this._viewportElement.scrollTop!==0||s>0&&o<this._lastRecordedBufferHeight)||(a.cancelable&&a.preventDefault(),!1)}handleWheel(a){const s=this._getPixelsScrolled(a);return s!==0&&(this._optionsService.rawOptions.smoothScrollDuration?(this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target===-1?this._smoothScrollState.target=this._viewportElement.scrollTop+s:this._smoothScrollState.target+=s,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()):this._viewportElement.scrollTop+=s,this._bubbleScroll(a,s))}scrollLines(a){if(a!==0)if(this._optionsService.rawOptions.smoothScrollDuration){const s=a*this._currentRowHeight;this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target=this._smoothScrollState.origin+s,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()}else this._onRequestScrollLines.fire({amount:a,suppressScrollEvent:!1})}_getPixelsScrolled(a){if(a.deltaY===0||a.shiftKey)return 0;let s=this._applyScrollModifier(a.deltaY,a);return a.deltaMode===WheelEvent.DOM_DELTA_LINE?s*=this._currentRowHeight:a.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(s*=this._currentRowHeight*this._bufferService.rows),s}getBufferElements(a,s){var y;let o,p="";const w=[],E=s??this._bufferService.buffer.lines.length,k=this._bufferService.buffer.lines;for(let b=a;b<E;b++){const D=k.get(b);if(!D)continue;const B=(y=k.get(b+1))==null?void 0:y.isWrapped;if(p+=D.translateToString(!B),!B||b===k.length-1){const O=document.createElement("div");O.textContent=p,w.push(O),p.length>0&&(o=O),p=""}}return{bufferElements:w,cursorElement:o}}getLinesScrolled(a){if(a.deltaY===0||a.shiftKey)return 0;let s=this._applyScrollModifier(a.deltaY,a);return a.deltaMode===WheelEvent.DOM_DELTA_PIXEL?(s/=this._currentRowHeight+0,this._wheelPartialScroll+=s,s=Math.floor(Math.abs(this._wheelPartialScroll))*(this._wheelPartialScroll>0?1:-1),this._wheelPartialScroll%=1):a.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(s*=this._bufferService.rows),s}_applyScrollModifier(a,s){const o=this._optionsService.rawOptions.fastScrollModifier;return o==="alt"&&s.altKey||o==="ctrl"&&s.ctrlKey||o==="shift"&&s.shiftKey?a*this._optionsService.rawOptions.fastScrollSensitivity*this._optionsService.rawOptions.scrollSensitivity:a*this._optionsService.rawOptions.scrollSensitivity}handleTouchStart(a){this._lastTouchY=a.touches[0].pageY}handleTouchMove(a){const s=this._lastTouchY-a.touches[0].pageY;return this._lastTouchY=a.touches[0].pageY,s!==0&&(this._viewportElement.scrollTop+=s,this._bubbleScroll(a,s))}};n.Viewport=h=f([S(2,v.IBufferService),S(3,v.IOptionsService),S(4,_.ICharSizeService),S(5,_.IRenderService),S(6,_.ICoreBrowserService),S(7,_.IThemeService)],h)},3107:function(m,n,c){var f=this&&this.__decorate||function(v,h,a,s){var o,p=arguments.length,w=p<3?h:s===null?s=Object.getOwnPropertyDescriptor(h,a):s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")w=Reflect.decorate(v,h,a,s);else for(var E=v.length-1;E>=0;E--)(o=v[E])&&(w=(p<3?o(w):p>3?o(h,a,w):o(h,a))||w);return p>3&&w&&Object.defineProperty(h,a,w),w},S=this&&this.__param||function(v,h){return function(a,s){h(a,s,v)}};Object.defineProperty(n,"__esModule",{value:!0}),n.BufferDecorationRenderer=void 0;const u=c(4725),_=c(844),g=c(2585);let C=n.BufferDecorationRenderer=class extends _.Disposable{constructor(v,h,a,s,o){super(),this._screenElement=v,this._bufferService=h,this._coreBrowserService=a,this._decorationService=s,this._renderService=o,this._decorationElements=new Map,this._altBufferIsActive=!1,this._dimensionsChanged=!1,this._container=document.createElement("div"),this._container.classList.add("xterm-decoration-container"),this._screenElement.appendChild(this._container),this.register(this._renderService.onRenderedViewportChange(()=>this._doRefreshDecorations())),this.register(this._renderService.onDimensionsChange(()=>{this._dimensionsChanged=!0,this._queueRefresh()})),this.register(this._coreBrowserService.onDprChange(()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._altBufferIsActive=this._bufferService.buffer===this._bufferService.buffers.alt})),this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh())),this.register(this._decorationService.onDecorationRemoved(p=>this._removeDecoration(p))),this.register((0,_.toDisposable)(()=>{this._container.remove(),this._decorationElements.clear()}))}_queueRefresh(){this._animationFrame===void 0&&(this._animationFrame=this._renderService.addRefreshCallback(()=>{this._doRefreshDecorations(),this._animationFrame=void 0}))}_doRefreshDecorations(){for(const v of this._decorationService.decorations)this._renderDecoration(v);this._dimensionsChanged=!1}_renderDecoration(v){this._refreshStyle(v),this._dimensionsChanged&&this._refreshXPosition(v)}_createElement(v){var s;const h=this._coreBrowserService.mainDocument.createElement("div");h.classList.add("xterm-decoration"),h.classList.toggle("xterm-decoration-top-layer",((s=v==null?void 0:v.options)==null?void 0:s.layer)==="top"),h.style.width=`${Math.round((v.options.width||1)*this._renderService.dimensions.css.cell.width)}px`,h.style.height=(v.options.height||1)*this._renderService.dimensions.css.cell.height+"px",h.style.top=(v.marker.line-this._bufferService.buffers.active.ydisp)*this._renderService.dimensions.css.cell.height+"px",h.style.lineHeight=`${this._renderService.dimensions.css.cell.height}px`;const a=v.options.x??0;return a&&a>this._bufferService.cols&&(h.style.display="none"),this._refreshXPosition(v,h),h}_refreshStyle(v){const h=v.marker.line-this._bufferService.buffers.active.ydisp;if(h<0||h>=this._bufferService.rows)v.element&&(v.element.style.display="none",v.onRenderEmitter.fire(v.element));else{let a=this._decorationElements.get(v);a||(a=this._createElement(v),v.element=a,this._decorationElements.set(v,a),this._container.appendChild(a),v.onDispose(()=>{this._decorationElements.delete(v),a.remove()})),a.style.top=h*this._renderService.dimensions.css.cell.height+"px",a.style.display=this._altBufferIsActive?"none":"block",v.onRenderEmitter.fire(a)}}_refreshXPosition(v,h=v.element){if(!h)return;const a=v.options.x??0;(v.options.anchor||"left")==="right"?h.style.right=a?a*this._renderService.dimensions.css.cell.width+"px":"":h.style.left=a?a*this._renderService.dimensions.css.cell.width+"px":""}_removeDecoration(v){var h;(h=this._decorationElements.get(v))==null||h.remove(),this._decorationElements.delete(v),v.dispose()}};n.BufferDecorationRenderer=C=f([S(1,g.IBufferService),S(2,u.ICoreBrowserService),S(3,g.IDecorationService),S(4,u.IRenderService)],C)},5871:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.ColorZoneStore=void 0,n.ColorZoneStore=class{constructor(){this._zones=[],this._zonePool=[],this._zonePoolIndex=0,this._linePadding={full:0,left:0,center:0,right:0}}get zones(){return this._zonePool.length=Math.min(this._zonePool.length,this._zones.length),this._zones}clear(){this._zones.length=0,this._zonePoolIndex=0}addDecoration(c){if(c.options.overviewRulerOptions){for(const f of this._zones)if(f.color===c.options.overviewRulerOptions.color&&f.position===c.options.overviewRulerOptions.position){if(this._lineIntersectsZone(f,c.marker.line))return;if(this._lineAdjacentToZone(f,c.marker.line,c.options.overviewRulerOptions.position))return void this._addLineToZone(f,c.marker.line)}if(this._zonePoolIndex<this._zonePool.length)return this._zonePool[this._zonePoolIndex].color=c.options.overviewRulerOptions.color,this._zonePool[this._zonePoolIndex].position=c.options.overviewRulerOptions.position,this._zonePool[this._zonePoolIndex].startBufferLine=c.marker.line,this._zonePool[this._zonePoolIndex].endBufferLine=c.marker.line,void this._zones.push(this._zonePool[this._zonePoolIndex++]);this._zones.push({color:c.options.overviewRulerOptions.color,position:c.options.overviewRulerOptions.position,startBufferLine:c.marker.line,endBufferLine:c.marker.line}),this._zonePool.push(this._zones[this._zones.length-1]),this._zonePoolIndex++}}setPadding(c){this._linePadding=c}_lineIntersectsZone(c,f){return f>=c.startBufferLine&&f<=c.endBufferLine}_lineAdjacentToZone(c,f,S){return f>=c.startBufferLine-this._linePadding[S||"full"]&&f<=c.endBufferLine+this._linePadding[S||"full"]}_addLineToZone(c,f){c.startBufferLine=Math.min(c.startBufferLine,f),c.endBufferLine=Math.max(c.endBufferLine,f)}}},5744:function(m,n,c){var f=this&&this.__decorate||function(o,p,w,E){var k,y=arguments.length,b=y<3?p:E===null?E=Object.getOwnPropertyDescriptor(p,w):E;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")b=Reflect.decorate(o,p,w,E);else for(var D=o.length-1;D>=0;D--)(k=o[D])&&(b=(y<3?k(b):y>3?k(p,w,b):k(p,w))||b);return y>3&&b&&Object.defineProperty(p,w,b),b},S=this&&this.__param||function(o,p){return function(w,E){p(w,E,o)}};Object.defineProperty(n,"__esModule",{value:!0}),n.OverviewRulerRenderer=void 0;const u=c(5871),_=c(4725),g=c(844),C=c(2585),v={full:0,left:0,center:0,right:0},h={full:0,left:0,center:0,right:0},a={full:0,left:0,center:0,right:0};let s=n.OverviewRulerRenderer=class extends g.Disposable{get _width(){return this._optionsService.options.overviewRulerWidth||0}constructor(o,p,w,E,k,y,b){var B;super(),this._viewportElement=o,this._screenElement=p,this._bufferService=w,this._decorationService=E,this._renderService=k,this._optionsService=y,this._coreBrowserService=b,this._colorZoneStore=new u.ColorZoneStore,this._shouldUpdateDimensions=!0,this._shouldUpdateAnchor=!0,this._lastKnownBufferLength=0,this._canvas=this._coreBrowserService.mainDocument.createElement("canvas"),this._canvas.classList.add("xterm-decoration-overview-ruler"),this._refreshCanvasDimensions(),(B=this._viewportElement.parentElement)==null||B.insertBefore(this._canvas,this._viewportElement);const D=this._canvas.getContext("2d");if(!D)throw new Error("Ctx cannot be null");this._ctx=D,this._registerDecorationListeners(),this._registerBufferChangeListeners(),this._registerDimensionChangeListeners(),this.register((0,g.toDisposable)(()=>{var O;(O=this._canvas)==null||O.remove()}))}_registerDecorationListeners(){this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh(void 0,!0))),this.register(this._decorationService.onDecorationRemoved(()=>this._queueRefresh(void 0,!0)))}_registerBufferChangeListeners(){this.register(this._renderService.onRenderedViewportChange(()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._canvas.style.display=this._bufferService.buffer===this._bufferService.buffers.alt?"none":"block"})),this.register(this._bufferService.onScroll(()=>{this._lastKnownBufferLength!==this._bufferService.buffers.normal.lines.length&&(this._refreshDrawHeightConstants(),this._refreshColorZonePadding())}))}_registerDimensionChangeListeners(){this.register(this._renderService.onRender(()=>{this._containerHeight&&this._containerHeight===this._screenElement.clientHeight||(this._queueRefresh(!0),this._containerHeight=this._screenElement.clientHeight)})),this.register(this._optionsService.onSpecificOptionChange("overviewRulerWidth",()=>this._queueRefresh(!0))),this.register(this._coreBrowserService.onDprChange(()=>this._queueRefresh(!0))),this._queueRefresh(!0)}_refreshDrawConstants(){const o=Math.floor(this._canvas.width/3),p=Math.ceil(this._canvas.width/3);h.full=this._canvas.width,h.left=o,h.center=p,h.right=o,this._refreshDrawHeightConstants(),a.full=0,a.left=0,a.center=h.left,a.right=h.left+h.center}_refreshDrawHeightConstants(){v.full=Math.round(2*this._coreBrowserService.dpr);const o=this._canvas.height/this._bufferService.buffer.lines.length,p=Math.round(Math.max(Math.min(o,12),6)*this._coreBrowserService.dpr);v.left=p,v.center=p,v.right=p}_refreshColorZonePadding(){this._colorZoneStore.setPadding({full:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*v.full),left:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*v.left),center:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*v.center),right:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*v.right)}),this._lastKnownBufferLength=this._bufferService.buffers.normal.lines.length}_refreshCanvasDimensions(){this._canvas.style.width=`${this._width}px`,this._canvas.width=Math.round(this._width*this._coreBrowserService.dpr),this._canvas.style.height=`${this._screenElement.clientHeight}px`,this._canvas.height=Math.round(this._screenElement.clientHeight*this._coreBrowserService.dpr),this._refreshDrawConstants(),this._refreshColorZonePadding()}_refreshDecorations(){this._shouldUpdateDimensions&&this._refreshCanvasDimensions(),this._ctx.clearRect(0,0,this._canvas.width,this._canvas.height),this._colorZoneStore.clear();for(const p of this._decorationService.decorations)this._colorZoneStore.addDecoration(p);this._ctx.lineWidth=1;const o=this._colorZoneStore.zones;for(const p of o)p.position!=="full"&&this._renderColorZone(p);for(const p of o)p.position==="full"&&this._renderColorZone(p);this._shouldUpdateDimensions=!1,this._shouldUpdateAnchor=!1}_renderColorZone(o){this._ctx.fillStyle=o.color,this._ctx.fillRect(a[o.position||"full"],Math.round((this._canvas.height-1)*(o.startBufferLine/this._bufferService.buffers.active.lines.length)-v[o.position||"full"]/2),h[o.position||"full"],Math.round((this._canvas.height-1)*((o.endBufferLine-o.startBufferLine)/this._bufferService.buffers.active.lines.length)+v[o.position||"full"]))}_queueRefresh(o,p){this._shouldUpdateDimensions=o||this._shouldUpdateDimensions,this._shouldUpdateAnchor=p||this._shouldUpdateAnchor,this._animationFrame===void 0&&(this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>{this._refreshDecorations(),this._animationFrame=void 0}))}};n.OverviewRulerRenderer=s=f([S(2,C.IBufferService),S(3,C.IDecorationService),S(4,_.IRenderService),S(5,C.IOptionsService),S(6,_.ICoreBrowserService)],s)},2950:function(m,n,c){var f=this&&this.__decorate||function(v,h,a,s){var o,p=arguments.length,w=p<3?h:s===null?s=Object.getOwnPropertyDescriptor(h,a):s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")w=Reflect.decorate(v,h,a,s);else for(var E=v.length-1;E>=0;E--)(o=v[E])&&(w=(p<3?o(w):p>3?o(h,a,w):o(h,a))||w);return p>3&&w&&Object.defineProperty(h,a,w),w},S=this&&this.__param||function(v,h){return function(a,s){h(a,s,v)}};Object.defineProperty(n,"__esModule",{value:!0}),n.CompositionHelper=void 0;const u=c(4725),_=c(2585),g=c(2584);let C=n.CompositionHelper=class{get isComposing(){return this._isComposing}constructor(v,h,a,s,o,p){this._textarea=v,this._compositionView=h,this._bufferService=a,this._optionsService=s,this._coreService=o,this._renderService=p,this._isComposing=!1,this._isSendingComposition=!1,this._compositionPosition={start:0,end:0},this._dataAlreadySent=""}compositionstart(){this._isComposing=!0,this._compositionPosition.start=this._textarea.value.length,this._compositionView.textContent="",this._dataAlreadySent="",this._compositionView.classList.add("active")}compositionupdate(v){this._compositionView.textContent=v.data,this.updateCompositionElements(),setTimeout(()=>{this._compositionPosition.end=this._textarea.value.length},0)}compositionend(){this._finalizeComposition(!0)}keydown(v){if(this._isComposing||this._isSendingComposition){if(v.keyCode===229||v.keyCode===16||v.keyCode===17||v.keyCode===18)return!1;this._finalizeComposition(!1)}return v.keyCode!==229||(this._handleAnyTextareaChanges(),!1)}_finalizeComposition(v){if(this._compositionView.classList.remove("active"),this._isComposing=!1,v){const h={start:this._compositionPosition.start,end:this._compositionPosition.end};this._isSendingComposition=!0,setTimeout(()=>{if(this._isSendingComposition){let a;this._isSendingComposition=!1,h.start+=this._dataAlreadySent.length,a=this._isComposing?this._textarea.value.substring(h.start,h.end):this._textarea.value.substring(h.start),a.length>0&&this._coreService.triggerDataEvent(a,!0)}},0)}else{this._isSendingComposition=!1;const h=this._textarea.value.substring(this._compositionPosition.start,this._compositionPosition.end);this._coreService.triggerDataEvent(h,!0)}}_handleAnyTextareaChanges(){const v=this._textarea.value;setTimeout(()=>{if(!this._isComposing){const h=this._textarea.value,a=h.replace(v,"");this._dataAlreadySent=a,h.length>v.length?this._coreService.triggerDataEvent(a,!0):h.length<v.length?this._coreService.triggerDataEvent(`${g.C0.DEL}`,!0):h.length===v.length&&h!==v&&this._coreService.triggerDataEvent(h,!0)}},0)}updateCompositionElements(v){if(this._isComposing){if(this._bufferService.buffer.isCursorInViewport){const h=Math.min(this._bufferService.buffer.x,this._bufferService.cols-1),a=this._renderService.dimensions.css.cell.height,s=this._bufferService.buffer.y*this._renderService.dimensions.css.cell.height,o=h*this._renderService.dimensions.css.cell.width;this._compositionView.style.left=o+"px",this._compositionView.style.top=s+"px",this._compositionView.style.height=a+"px",this._compositionView.style.lineHeight=a+"px",this._compositionView.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._compositionView.style.fontSize=this._optionsService.rawOptions.fontSize+"px";const p=this._compositionView.getBoundingClientRect();this._textarea.style.left=o+"px",this._textarea.style.top=s+"px",this._textarea.style.width=Math.max(p.width,1)+"px",this._textarea.style.height=Math.max(p.height,1)+"px",this._textarea.style.lineHeight=p.height+"px"}v||setTimeout(()=>this.updateCompositionElements(!0),0)}}};n.CompositionHelper=C=f([S(2,_.IBufferService),S(3,_.IOptionsService),S(4,_.ICoreService),S(5,u.IRenderService)],C)},9806:(m,n)=>{function c(f,S,u){const _=u.getBoundingClientRect(),g=f.getComputedStyle(u),C=parseInt(g.getPropertyValue("padding-left")),v=parseInt(g.getPropertyValue("padding-top"));return[S.clientX-_.left-C,S.clientY-_.top-v]}Object.defineProperty(n,"__esModule",{value:!0}),n.getCoords=n.getCoordsRelativeToElement=void 0,n.getCoordsRelativeToElement=c,n.getCoords=function(f,S,u,_,g,C,v,h,a){if(!C)return;const s=c(f,S,u);return s?(s[0]=Math.ceil((s[0]+(a?v/2:0))/v),s[1]=Math.ceil(s[1]/h),s[0]=Math.min(Math.max(s[0],1),_+(a?1:0)),s[1]=Math.min(Math.max(s[1],1),g),s):void 0}},9504:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.moveToCellSequence=void 0;const f=c(2584);function S(h,a,s,o){const p=h-u(h,s),w=a-u(a,s),E=Math.abs(p-w)-function(k,y,b){let D=0;const B=k-u(k,b),O=y-u(y,b);for(let T=0;T<Math.abs(B-O);T++){const H=_(k,y)==="A"?-1:1,j=b.buffer.lines.get(B+H*T);j!=null&&j.isWrapped&&D++}return D}(h,a,s);return v(E,C(_(h,a),o))}function u(h,a){let s=0,o=a.buffer.lines.get(h),p=o==null?void 0:o.isWrapped;for(;p&&h>=0&&h<a.rows;)s++,o=a.buffer.lines.get(--h),p=o==null?void 0:o.isWrapped;return s}function _(h,a){return h>a?"A":"B"}function g(h,a,s,o,p,w){let E=h,k=a,y="";for(;E!==s||k!==o;)E+=p?1:-1,p&&E>w.cols-1?(y+=w.buffer.translateBufferLineToString(k,!1,h,E),E=0,h=0,k++):!p&&E<0&&(y+=w.buffer.translateBufferLineToString(k,!1,0,h+1),E=w.cols-1,h=E,k--);return y+w.buffer.translateBufferLineToString(k,!1,h,E)}function C(h,a){const s=a?"O":"[";return f.C0.ESC+s+h}function v(h,a){h=Math.floor(h);let s="";for(let o=0;o<h;o++)s+=a;return s}n.moveToCellSequence=function(h,a,s,o){const p=s.buffer.x,w=s.buffer.y;if(!s.buffer.hasScrollback)return function(y,b,D,B,O,T){return S(b,B,O,T).length===0?"":v(g(y,b,y,b-u(b,O),!1,O).length,C("D",T))}(p,w,0,a,s,o)+S(w,a,s,o)+function(y,b,D,B,O,T){let H;H=S(b,B,O,T).length>0?B-u(B,O):b;const j=B,$=function(q,N,x,R,A,P){let z;return z=S(x,R,A,P).length>0?R-u(R,A):N,q<x&&z<=R||q>=x&&z<R?"C":"D"}(y,b,D,B,O,T);return v(g(y,H,D,j,$==="C",O).length,C($,T))}(p,w,h,a,s,o);let E;if(w===a)return E=p>h?"D":"C",v(Math.abs(p-h),C(E,o));E=w>a?"D":"C";const k=Math.abs(w-a);return v(function(y,b){return b.cols-y}(w>a?h:p,s)+(k-1)*s.cols+1+((w>a?p:h)-1),C(E,o))}},1296:function(m,n,c){var f=this&&this.__decorate||function(T,H,j,$){var q,N=arguments.length,x=N<3?H:$===null?$=Object.getOwnPropertyDescriptor(H,j):$;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")x=Reflect.decorate(T,H,j,$);else for(var R=T.length-1;R>=0;R--)(q=T[R])&&(x=(N<3?q(x):N>3?q(H,j,x):q(H,j))||x);return N>3&&x&&Object.defineProperty(H,j,x),x},S=this&&this.__param||function(T,H){return function(j,$){H(j,$,T)}};Object.defineProperty(n,"__esModule",{value:!0}),n.DomRenderer=void 0;const u=c(3787),_=c(2550),g=c(2223),C=c(6171),v=c(6052),h=c(4725),a=c(8055),s=c(8460),o=c(844),p=c(2585),w="xterm-dom-renderer-owner-",E="xterm-rows",k="xterm-fg-",y="xterm-bg-",b="xterm-focus",D="xterm-selection";let B=1,O=n.DomRenderer=class extends o.Disposable{constructor(T,H,j,$,q,N,x,R,A,P,z,V,Y){super(),this._terminal=T,this._document=H,this._element=j,this._screenElement=$,this._viewportElement=q,this._helperContainer=N,this._linkifier2=x,this._charSizeService=A,this._optionsService=P,this._bufferService=z,this._coreBrowserService=V,this._themeService=Y,this._terminalClass=B++,this._rowElements=[],this._selectionRenderModel=(0,v.createSelectionRenderModel)(),this.onRequestRedraw=this.register(new s.EventEmitter).event,this._rowContainer=this._document.createElement("div"),this._rowContainer.classList.add(E),this._rowContainer.style.lineHeight="normal",this._rowContainer.setAttribute("aria-hidden","true"),this._refreshRowElements(this._bufferService.cols,this._bufferService.rows),this._selectionContainer=this._document.createElement("div"),this._selectionContainer.classList.add(D),this._selectionContainer.setAttribute("aria-hidden","true"),this.dimensions=(0,C.createRenderDimensions)(),this._updateDimensions(),this.register(this._optionsService.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._themeService.onChangeColors(Q=>this._injectCss(Q))),this._injectCss(this._themeService.colors),this._rowFactory=R.createInstance(u.DomRendererRowFactory,document),this._element.classList.add(w+this._terminalClass),this._screenElement.appendChild(this._rowContainer),this._screenElement.appendChild(this._selectionContainer),this.register(this._linkifier2.onShowLinkUnderline(Q=>this._handleLinkHover(Q))),this.register(this._linkifier2.onHideLinkUnderline(Q=>this._handleLinkLeave(Q))),this.register((0,o.toDisposable)(()=>{this._element.classList.remove(w+this._terminalClass),this._rowContainer.remove(),this._selectionContainer.remove(),this._widthCache.dispose(),this._themeStyleElement.remove(),this._dimensionsStyleElement.remove()})),this._widthCache=new _.WidthCache(this._document,this._helperContainer),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}_updateDimensions(){const T=this._coreBrowserService.dpr;this.dimensions.device.char.width=this._charSizeService.width*T,this.dimensions.device.char.height=Math.ceil(this._charSizeService.height*T),this.dimensions.device.cell.width=this.dimensions.device.char.width+Math.round(this._optionsService.rawOptions.letterSpacing),this.dimensions.device.cell.height=Math.floor(this.dimensions.device.char.height*this._optionsService.rawOptions.lineHeight),this.dimensions.device.char.left=0,this.dimensions.device.char.top=0,this.dimensions.device.canvas.width=this.dimensions.device.cell.width*this._bufferService.cols,this.dimensions.device.canvas.height=this.dimensions.device.cell.height*this._bufferService.rows,this.dimensions.css.canvas.width=Math.round(this.dimensions.device.canvas.width/T),this.dimensions.css.canvas.height=Math.round(this.dimensions.device.canvas.height/T),this.dimensions.css.cell.width=this.dimensions.css.canvas.width/this._bufferService.cols,this.dimensions.css.cell.height=this.dimensions.css.canvas.height/this._bufferService.rows;for(const j of this._rowElements)j.style.width=`${this.dimensions.css.canvas.width}px`,j.style.height=`${this.dimensions.css.cell.height}px`,j.style.lineHeight=`${this.dimensions.css.cell.height}px`,j.style.overflow="hidden";this._dimensionsStyleElement||(this._dimensionsStyleElement=this._document.createElement("style"),this._screenElement.appendChild(this._dimensionsStyleElement));const H=`${this._terminalSelector} .${E} span { display: inline-block; height: 100%; vertical-align: top;}`;this._dimensionsStyleElement.textContent=H,this._selectionContainer.style.height=this._viewportElement.style.height,this._screenElement.style.width=`${this.dimensions.css.canvas.width}px`,this._screenElement.style.height=`${this.dimensions.css.canvas.height}px`}_injectCss(T){this._themeStyleElement||(this._themeStyleElement=this._document.createElement("style"),this._screenElement.appendChild(this._themeStyleElement));let H=`${this._terminalSelector} .${E} { color: ${T.foreground.css}; font-family: ${this._optionsService.rawOptions.fontFamily}; font-size: ${this._optionsService.rawOptions.fontSize}px; font-kerning: none; white-space: pre}`;H+=`${this._terminalSelector} .${E} .xterm-dim { color: ${a.color.multiplyOpacity(T.foreground,.5).css};}`,H+=`${this._terminalSelector} span:not(.xterm-bold) { font-weight: ${this._optionsService.rawOptions.fontWeight};}${this._terminalSelector} span.xterm-bold { font-weight: ${this._optionsService.rawOptions.fontWeightBold};}${this._terminalSelector} span.xterm-italic { font-style: italic;}`;const j=`blink_underline_${this._terminalClass}`,$=`blink_bar_${this._terminalClass}`,q=`blink_block_${this._terminalClass}`;H+=`@keyframes ${j} { 50% {  border-bottom-style: hidden; }}`,H+=`@keyframes ${$} { 50% {  box-shadow: none; }}`,H+=`@keyframes ${q} { 0% {  background-color: ${T.cursor.css};  color: ${T.cursorAccent.css}; } 50% {  background-color: inherit;  color: ${T.cursor.css}; }}`,H+=`${this._terminalSelector} .${E}.${b} .xterm-cursor.xterm-cursor-blink.xterm-cursor-underline { animation: ${j} 1s step-end infinite;}${this._terminalSelector} .${E}.${b} .xterm-cursor.xterm-cursor-blink.xterm-cursor-bar { animation: ${$} 1s step-end infinite;}${this._terminalSelector} .${E}.${b} .xterm-cursor.xterm-cursor-blink.xterm-cursor-block { animation: ${q} 1s step-end infinite;}${this._terminalSelector} .${E} .xterm-cursor.xterm-cursor-block { background-color: ${T.cursor.css}; color: ${T.cursorAccent.css};}${this._terminalSelector} .${E} .xterm-cursor.xterm-cursor-block:not(.xterm-cursor-blink) { background-color: ${T.cursor.css} !important; color: ${T.cursorAccent.css} !important;}${this._terminalSelector} .${E} .xterm-cursor.xterm-cursor-outline { outline: 1px solid ${T.cursor.css}; outline-offset: -1px;}${this._terminalSelector} .${E} .xterm-cursor.xterm-cursor-bar { box-shadow: ${this._optionsService.rawOptions.cursorWidth}px 0 0 ${T.cursor.css} inset;}${this._terminalSelector} .${E} .xterm-cursor.xterm-cursor-underline { border-bottom: 1px ${T.cursor.css}; border-bottom-style: solid; height: calc(100% - 1px);}`,H+=`${this._terminalSelector} .${D} { position: absolute; top: 0; left: 0; z-index: 1; pointer-events: none;}${this._terminalSelector}.focus .${D} div { position: absolute; background-color: ${T.selectionBackgroundOpaque.css};}${this._terminalSelector} .${D} div { position: absolute; background-color: ${T.selectionInactiveBackgroundOpaque.css};}`;for(const[N,x]of T.ansi.entries())H+=`${this._terminalSelector} .${k}${N} { color: ${x.css}; }${this._terminalSelector} .${k}${N}.xterm-dim { color: ${a.color.multiplyOpacity(x,.5).css}; }${this._terminalSelector} .${y}${N} { background-color: ${x.css}; }`;H+=`${this._terminalSelector} .${k}${g.INVERTED_DEFAULT_COLOR} { color: ${a.color.opaque(T.background).css}; }${this._terminalSelector} .${k}${g.INVERTED_DEFAULT_COLOR}.xterm-dim { color: ${a.color.multiplyOpacity(a.color.opaque(T.background),.5).css}; }${this._terminalSelector} .${y}${g.INVERTED_DEFAULT_COLOR} { background-color: ${T.foreground.css}; }`,this._themeStyleElement.textContent=H}_setDefaultSpacing(){const T=this.dimensions.css.cell.width-this._widthCache.get("W",!1,!1);this._rowContainer.style.letterSpacing=`${T}px`,this._rowFactory.defaultSpacing=T}handleDevicePixelRatioChange(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}_refreshRowElements(T,H){for(let j=this._rowElements.length;j<=H;j++){const $=this._document.createElement("div");this._rowContainer.appendChild($),this._rowElements.push($)}for(;this._rowElements.length>H;)this._rowContainer.removeChild(this._rowElements.pop())}handleResize(T,H){this._refreshRowElements(T,H),this._updateDimensions(),this.handleSelectionChanged(this._selectionRenderModel.selectionStart,this._selectionRenderModel.selectionEnd,this._selectionRenderModel.columnSelectMode)}handleCharSizeChanged(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}handleBlur(){this._rowContainer.classList.remove(b),this.renderRows(0,this._bufferService.rows-1)}handleFocus(){this._rowContainer.classList.add(b),this.renderRows(this._bufferService.buffer.y,this._bufferService.buffer.y)}handleSelectionChanged(T,H,j){if(this._selectionContainer.replaceChildren(),this._rowFactory.handleSelectionChanged(T,H,j),this.renderRows(0,this._bufferService.rows-1),!T||!H)return;this._selectionRenderModel.update(this._terminal,T,H,j);const $=this._selectionRenderModel.viewportStartRow,q=this._selectionRenderModel.viewportEndRow,N=this._selectionRenderModel.viewportCappedStartRow,x=this._selectionRenderModel.viewportCappedEndRow;if(N>=this._bufferService.rows||x<0)return;const R=this._document.createDocumentFragment();if(j){const A=T[0]>H[0];R.appendChild(this._createSelectionElement(N,A?H[0]:T[0],A?T[0]:H[0],x-N+1))}else{const A=$===N?T[0]:0,P=N===q?H[0]:this._bufferService.cols;R.appendChild(this._createSelectionElement(N,A,P));const z=x-N-1;if(R.appendChild(this._createSelectionElement(N+1,0,this._bufferService.cols,z)),N!==x){const V=q===x?H[0]:this._bufferService.cols;R.appendChild(this._createSelectionElement(x,0,V))}}this._selectionContainer.appendChild(R)}_createSelectionElement(T,H,j,$=1){const q=this._document.createElement("div"),N=H*this.dimensions.css.cell.width;let x=this.dimensions.css.cell.width*(j-H);return N+x>this.dimensions.css.canvas.width&&(x=this.dimensions.css.canvas.width-N),q.style.height=$*this.dimensions.css.cell.height+"px",q.style.top=T*this.dimensions.css.cell.height+"px",q.style.left=`${N}px`,q.style.width=`${x}px`,q}handleCursorMove(){}_handleOptionsChanged(){this._updateDimensions(),this._injectCss(this._themeService.colors),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}clear(){for(const T of this._rowElements)T.replaceChildren()}renderRows(T,H){const j=this._bufferService.buffer,$=j.ybase+j.y,q=Math.min(j.x,this._bufferService.cols-1),N=this._optionsService.rawOptions.cursorBlink,x=this._optionsService.rawOptions.cursorStyle,R=this._optionsService.rawOptions.cursorInactiveStyle;for(let A=T;A<=H;A++){const P=A+j.ydisp,z=this._rowElements[A],V=j.lines.get(P);if(!z||!V)break;z.replaceChildren(...this._rowFactory.createRow(V,P,P===$,x,R,q,N,this.dimensions.css.cell.width,this._widthCache,-1,-1))}}get _terminalSelector(){return`.${w}${this._terminalClass}`}_handleLinkHover(T){this._setCellUnderline(T.x1,T.x2,T.y1,T.y2,T.cols,!0)}_handleLinkLeave(T){this._setCellUnderline(T.x1,T.x2,T.y1,T.y2,T.cols,!1)}_setCellUnderline(T,H,j,$,q,N){j<0&&(T=0),$<0&&(H=0);const x=this._bufferService.rows-1;j=Math.max(Math.min(j,x),0),$=Math.max(Math.min($,x),0),q=Math.min(q,this._bufferService.cols);const R=this._bufferService.buffer,A=R.ybase+R.y,P=Math.min(R.x,q-1),z=this._optionsService.rawOptions.cursorBlink,V=this._optionsService.rawOptions.cursorStyle,Y=this._optionsService.rawOptions.cursorInactiveStyle;for(let Q=j;Q<=$;++Q){const M=Q+R.ydisp,L=this._rowElements[Q],W=R.lines.get(M);if(!L||!W)break;L.replaceChildren(...this._rowFactory.createRow(W,M,M===A,V,Y,P,z,this.dimensions.css.cell.width,this._widthCache,N?Q===j?T:0:-1,N?(Q===$?H:q)-1:-1))}}};n.DomRenderer=O=f([S(7,p.IInstantiationService),S(8,h.ICharSizeService),S(9,p.IOptionsService),S(10,p.IBufferService),S(11,h.ICoreBrowserService),S(12,h.IThemeService)],O)},3787:function(m,n,c){var f=this&&this.__decorate||function(E,k,y,b){var D,B=arguments.length,O=B<3?k:b===null?b=Object.getOwnPropertyDescriptor(k,y):b;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")O=Reflect.decorate(E,k,y,b);else for(var T=E.length-1;T>=0;T--)(D=E[T])&&(O=(B<3?D(O):B>3?D(k,y,O):D(k,y))||O);return B>3&&O&&Object.defineProperty(k,y,O),O},S=this&&this.__param||function(E,k){return function(y,b){k(y,b,E)}};Object.defineProperty(n,"__esModule",{value:!0}),n.DomRendererRowFactory=void 0;const u=c(2223),_=c(643),g=c(511),C=c(2585),v=c(8055),h=c(4725),a=c(4269),s=c(6171),o=c(3734);let p=n.DomRendererRowFactory=class{constructor(E,k,y,b,D,B,O){this._document=E,this._characterJoinerService=k,this._optionsService=y,this._coreBrowserService=b,this._coreService=D,this._decorationService=B,this._themeService=O,this._workCell=new g.CellData,this._columnSelectMode=!1,this.defaultSpacing=0}handleSelectionChanged(E,k,y){this._selectionStart=E,this._selectionEnd=k,this._columnSelectMode=y}createRow(E,k,y,b,D,B,O,T,H,j,$){const q=[],N=this._characterJoinerService.getJoinedCharacters(k),x=this._themeService.colors;let R,A=E.getNoBgTrimmedLength();y&&A<B+1&&(A=B+1);let P=0,z="",V=0,Y=0,Q=0,M=!1,L=0,W=!1,F=0;const X=[],K=j!==-1&&$!==-1;for(let te=0;te<A;te++){E.loadCell(te,this._workCell);let de=this._workCell.getWidth();if(de===0)continue;let fe=!1,Me=te,J=this._workCell;if(N.length>0&&te===N[0][0]){fe=!0;const ae=N.shift();J=new a.JoinedCellData(this._workCell,E.translateToString(!0,ae[0],ae[1]),ae[1]-ae[0]),Me=ae[1]-1,de=J.getWidth()}const jr=this._isCellInSelection(te,k),an=y&&te===B,cn=K&&te>=j&&te<=$;let hn=!1;this._decorationService.forEachDecorationAtCell(te,k,void 0,ae=>{hn=!0});let Mi=J.getChars()||_.WHITESPACE_CELL_CHAR;if(Mi===" "&&(J.isUnderline()||J.isOverline())&&(Mi=" "),F=de*T-H.get(Mi,J.isBold(),J.isItalic()),R){if(P&&(jr&&W||!jr&&!W&&J.bg===V)&&(jr&&W&&x.selectionForeground||J.fg===Y)&&J.extended.ext===Q&&cn===M&&F===L&&!an&&!fe&&!hn){J.isInvisible()?z+=_.WHITESPACE_CELL_CHAR:z+=Mi,P++;continue}P&&(R.textContent=z),R=this._document.createElement("span"),P=0,z=""}else R=this._document.createElement("span");if(V=J.bg,Y=J.fg,Q=J.extended.ext,M=cn,L=F,W=jr,fe&&B>=te&&B<=Me&&(B=te),!this._coreService.isCursorHidden&&an&&this._coreService.isCursorInitialized){if(X.push("xterm-cursor"),this._coreBrowserService.isFocused)O&&X.push("xterm-cursor-blink"),X.push(b==="bar"?"xterm-cursor-bar":b==="underline"?"xterm-cursor-underline":"xterm-cursor-block");else if(D)switch(D){case"outline":X.push("xterm-cursor-outline");break;case"block":X.push("xterm-cursor-block");break;case"bar":X.push("xterm-cursor-bar");break;case"underline":X.push("xterm-cursor-underline")}}if(J.isBold()&&X.push("xterm-bold"),J.isItalic()&&X.push("xterm-italic"),J.isDim()&&X.push("xterm-dim"),z=J.isInvisible()?_.WHITESPACE_CELL_CHAR:J.getChars()||_.WHITESPACE_CELL_CHAR,J.isUnderline()&&(X.push(`xterm-underline-${J.extended.underlineStyle}`),z===" "&&(z=" "),!J.isUnderlineColorDefault()))if(J.isUnderlineColorRGB())R.style.textDecorationColor=`rgb(${o.AttributeData.toColorRGB(J.getUnderlineColor()).join(",")})`;else{let ae=J.getUnderlineColor();this._optionsService.rawOptions.drawBoldTextInBrightColors&&J.isBold()&&ae<8&&(ae+=8),R.style.textDecorationColor=x.ansi[ae].css}J.isOverline()&&(X.push("xterm-overline"),z===" "&&(z=" ")),J.isStrikethrough()&&X.push("xterm-strikethrough"),cn&&(R.style.textDecoration="underline");let qe=J.getFgColor(),zr=J.getFgColorMode(),ct=J.getBgColor(),Wr=J.getBgColorMode();const un=!!J.isInverse();if(un){const ae=qe;qe=ct,ct=ae;const Mu=zr;zr=Wr,Wr=Mu}let Lt,Ii,Rt,Ur=!1;switch(this._decorationService.forEachDecorationAtCell(te,k,void 0,ae=>{ae.options.layer!=="top"&&Ur||(ae.backgroundColorRGB&&(Wr=50331648,ct=ae.backgroundColorRGB.rgba>>8&16777215,Lt=ae.backgroundColorRGB),ae.foregroundColorRGB&&(zr=50331648,qe=ae.foregroundColorRGB.rgba>>8&16777215,Ii=ae.foregroundColorRGB),Ur=ae.options.layer==="top")}),!Ur&&jr&&(Lt=this._coreBrowserService.isFocused?x.selectionBackgroundOpaque:x.selectionInactiveBackgroundOpaque,ct=Lt.rgba>>8&16777215,Wr=50331648,Ur=!0,x.selectionForeground&&(zr=50331648,qe=x.selectionForeground.rgba>>8&16777215,Ii=x.selectionForeground)),Ur&&X.push("xterm-decoration-top"),Wr){case 16777216:case 33554432:Rt=x.ansi[ct],X.push(`xterm-bg-${ct}`);break;case 50331648:Rt=v.channels.toColor(ct>>16,ct>>8&255,255&ct),this._addStyle(R,`background-color:#${w((ct>>>0).toString(16),"0",6)}`);break;default:un?(Rt=x.foreground,X.push(`xterm-bg-${u.INVERTED_DEFAULT_COLOR}`)):Rt=x.background}switch(Lt||J.isDim()&&(Lt=v.color.multiplyOpacity(Rt,.5)),zr){case 16777216:case 33554432:J.isBold()&&qe<8&&this._optionsService.rawOptions.drawBoldTextInBrightColors&&(qe+=8),this._applyMinimumContrast(R,Rt,x.ansi[qe],J,Lt,void 0)||X.push(`xterm-fg-${qe}`);break;case 50331648:const ae=v.channels.toColor(qe>>16&255,qe>>8&255,255&qe);this._applyMinimumContrast(R,Rt,ae,J,Lt,Ii)||this._addStyle(R,`color:#${w(qe.toString(16),"0",6)}`);break;default:this._applyMinimumContrast(R,Rt,x.foreground,J,Lt,Ii)||un&&X.push(`xterm-fg-${u.INVERTED_DEFAULT_COLOR}`)}X.length&&(R.className=X.join(" "),X.length=0),an||fe||hn?R.textContent=z:P++,F!==this.defaultSpacing&&(R.style.letterSpacing=`${F}px`),q.push(R),te=Me}return R&&P&&(R.textContent=z),q}_applyMinimumContrast(E,k,y,b,D,B){if(this._optionsService.rawOptions.minimumContrastRatio===1||(0,s.treatGlyphAsBackgroundColor)(b.getCode()))return!1;const O=this._getContrastCache(b);let T;if(D||B||(T=O.getColor(k.rgba,y.rgba)),T===void 0){const H=this._optionsService.rawOptions.minimumContrastRatio/(b.isDim()?2:1);T=v.color.ensureContrastRatio(D||k,B||y,H),O.setColor((D||k).rgba,(B||y).rgba,T??null)}return!!T&&(this._addStyle(E,`color:${T.css}`),!0)}_getContrastCache(E){return E.isDim()?this._themeService.colors.halfContrastCache:this._themeService.colors.contrastCache}_addStyle(E,k){E.setAttribute("style",`${E.getAttribute("style")||""}${k};`)}_isCellInSelection(E,k){const y=this._selectionStart,b=this._selectionEnd;return!(!y||!b)&&(this._columnSelectMode?y[0]<=b[0]?E>=y[0]&&k>=y[1]&&E<b[0]&&k<=b[1]:E<y[0]&&k>=y[1]&&E>=b[0]&&k<=b[1]:k>y[1]&&k<b[1]||y[1]===b[1]&&k===y[1]&&E>=y[0]&&E<b[0]||y[1]<b[1]&&k===b[1]&&E<b[0]||y[1]<b[1]&&k===y[1]&&E>=y[0])}};function w(E,k,y){for(;E.length<y;)E=k+E;return E}n.DomRendererRowFactory=p=f([S(1,h.ICharacterJoinerService),S(2,C.IOptionsService),S(3,h.ICoreBrowserService),S(4,C.ICoreService),S(5,C.IDecorationService),S(6,h.IThemeService)],p)},2550:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.WidthCache=void 0,n.WidthCache=class{constructor(c,f){this._flat=new Float32Array(256),this._font="",this._fontSize=0,this._weight="normal",this._weightBold="bold",this._measureElements=[],this._container=c.createElement("div"),this._container.classList.add("xterm-width-cache-measure-container"),this._container.setAttribute("aria-hidden","true"),this._container.style.whiteSpace="pre",this._container.style.fontKerning="none";const S=c.createElement("span");S.classList.add("xterm-char-measure-element");const u=c.createElement("span");u.classList.add("xterm-char-measure-element"),u.style.fontWeight="bold";const _=c.createElement("span");_.classList.add("xterm-char-measure-element"),_.style.fontStyle="italic";const g=c.createElement("span");g.classList.add("xterm-char-measure-element"),g.style.fontWeight="bold",g.style.fontStyle="italic",this._measureElements=[S,u,_,g],this._container.appendChild(S),this._container.appendChild(u),this._container.appendChild(_),this._container.appendChild(g),f.appendChild(this._container),this.clear()}dispose(){this._container.remove(),this._measureElements.length=0,this._holey=void 0}clear(){this._flat.fill(-9999),this._holey=new Map}setFont(c,f,S,u){c===this._font&&f===this._fontSize&&S===this._weight&&u===this._weightBold||(this._font=c,this._fontSize=f,this._weight=S,this._weightBold=u,this._container.style.fontFamily=this._font,this._container.style.fontSize=`${this._fontSize}px`,this._measureElements[0].style.fontWeight=`${S}`,this._measureElements[1].style.fontWeight=`${u}`,this._measureElements[2].style.fontWeight=`${S}`,this._measureElements[3].style.fontWeight=`${u}`,this.clear())}get(c,f,S){let u=0;if(!f&&!S&&c.length===1&&(u=c.charCodeAt(0))<256){if(this._flat[u]!==-9999)return this._flat[u];const C=this._measure(c,0);return C>0&&(this._flat[u]=C),C}let _=c;f&&(_+="B"),S&&(_+="I");let g=this._holey.get(_);if(g===void 0){let C=0;f&&(C|=1),S&&(C|=2),g=this._measure(c,C),g>0&&this._holey.set(_,g)}return g}_measure(c,f){const S=this._measureElements[f];return S.textContent=c.repeat(32),S.offsetWidth/32}}},2223:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.TEXT_BASELINE=n.DIM_OPACITY=n.INVERTED_DEFAULT_COLOR=void 0;const f=c(6114);n.INVERTED_DEFAULT_COLOR=257,n.DIM_OPACITY=.5,n.TEXT_BASELINE=f.isFirefox||f.isLegacyEdge?"bottom":"ideographic"},6171:(m,n)=>{function c(S){return 57508<=S&&S<=57558}function f(S){return S>=128512&&S<=128591||S>=127744&&S<=128511||S>=128640&&S<=128767||S>=9728&&S<=9983||S>=9984&&S<=10175||S>=65024&&S<=65039||S>=129280&&S<=129535||S>=127462&&S<=127487}Object.defineProperty(n,"__esModule",{value:!0}),n.computeNextVariantOffset=n.createRenderDimensions=n.treatGlyphAsBackgroundColor=n.allowRescaling=n.isEmoji=n.isRestrictedPowerlineGlyph=n.isPowerlineGlyph=n.throwIfFalsy=void 0,n.throwIfFalsy=function(S){if(!S)throw new Error("value must not be falsy");return S},n.isPowerlineGlyph=c,n.isRestrictedPowerlineGlyph=function(S){return 57520<=S&&S<=57527},n.isEmoji=f,n.allowRescaling=function(S,u,_,g){return u===1&&_>Math.ceil(1.5*g)&&S!==void 0&&S>255&&!f(S)&&!c(S)&&!function(C){return 57344<=C&&C<=63743}(S)},n.treatGlyphAsBackgroundColor=function(S){return c(S)||function(u){return 9472<=u&&u<=9631}(S)},n.createRenderDimensions=function(){return{css:{canvas:{width:0,height:0},cell:{width:0,height:0}},device:{canvas:{width:0,height:0},cell:{width:0,height:0},char:{width:0,height:0,left:0,top:0}}}},n.computeNextVariantOffset=function(S,u,_=0){return(S-(2*Math.round(u)-_))%(2*Math.round(u))}},6052:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.createSelectionRenderModel=void 0;class c{constructor(){this.clear()}clear(){this.hasSelection=!1,this.columnSelectMode=!1,this.viewportStartRow=0,this.viewportEndRow=0,this.viewportCappedStartRow=0,this.viewportCappedEndRow=0,this.startCol=0,this.endCol=0,this.selectionStart=void 0,this.selectionEnd=void 0}update(S,u,_,g=!1){if(this.selectionStart=u,this.selectionEnd=_,!u||!_||u[0]===_[0]&&u[1]===_[1])return void this.clear();const C=S.buffers.active.ydisp,v=u[1]-C,h=_[1]-C,a=Math.max(v,0),s=Math.min(h,S.rows-1);a>=S.rows||s<0?this.clear():(this.hasSelection=!0,this.columnSelectMode=g,this.viewportStartRow=v,this.viewportEndRow=h,this.viewportCappedStartRow=a,this.viewportCappedEndRow=s,this.startCol=u[0],this.endCol=_[0])}isCellSelected(S,u,_){return!!this.hasSelection&&(_-=S.buffer.active.viewportY,this.columnSelectMode?this.startCol<=this.endCol?u>=this.startCol&&_>=this.viewportCappedStartRow&&u<this.endCol&&_<=this.viewportCappedEndRow:u<this.startCol&&_>=this.viewportCappedStartRow&&u>=this.endCol&&_<=this.viewportCappedEndRow:_>this.viewportStartRow&&_<this.viewportEndRow||this.viewportStartRow===this.viewportEndRow&&_===this.viewportStartRow&&u>=this.startCol&&u<this.endCol||this.viewportStartRow<this.viewportEndRow&&_===this.viewportEndRow&&u<this.endCol||this.viewportStartRow<this.viewportEndRow&&_===this.viewportStartRow&&u>=this.startCol)}}n.createSelectionRenderModel=function(){return new c}},456:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.SelectionModel=void 0,n.SelectionModel=class{constructor(c){this._bufferService=c,this.isSelectAllActive=!1,this.selectionStartLength=0}clearSelection(){this.selectionStart=void 0,this.selectionEnd=void 0,this.isSelectAllActive=!1,this.selectionStartLength=0}get finalSelectionStart(){return this.isSelectAllActive?[0,0]:this.selectionEnd&&this.selectionStart&&this.areSelectionValuesReversed()?this.selectionEnd:this.selectionStart}get finalSelectionEnd(){if(this.isSelectAllActive)return[this._bufferService.cols,this._bufferService.buffer.ybase+this._bufferService.rows-1];if(this.selectionStart){if(!this.selectionEnd||this.areSelectionValuesReversed()){const c=this.selectionStart[0]+this.selectionStartLength;return c>this._bufferService.cols?c%this._bufferService.cols==0?[this._bufferService.cols,this.selectionStart[1]+Math.floor(c/this._bufferService.cols)-1]:[c%this._bufferService.cols,this.selectionStart[1]+Math.floor(c/this._bufferService.cols)]:[c,this.selectionStart[1]]}if(this.selectionStartLength&&this.selectionEnd[1]===this.selectionStart[1]){const c=this.selectionStart[0]+this.selectionStartLength;return c>this._bufferService.cols?[c%this._bufferService.cols,this.selectionStart[1]+Math.floor(c/this._bufferService.cols)]:[Math.max(c,this.selectionEnd[0]),this.selectionEnd[1]]}return this.selectionEnd}}areSelectionValuesReversed(){const c=this.selectionStart,f=this.selectionEnd;return!(!c||!f)&&(c[1]>f[1]||c[1]===f[1]&&c[0]>f[0])}handleTrim(c){return this.selectionStart&&(this.selectionStart[1]-=c),this.selectionEnd&&(this.selectionEnd[1]-=c),this.selectionEnd&&this.selectionEnd[1]<0?(this.clearSelection(),!0):(this.selectionStart&&this.selectionStart[1]<0&&(this.selectionStart[1]=0),!1)}}},428:function(m,n,c){var f=this&&this.__decorate||function(s,o,p,w){var E,k=arguments.length,y=k<3?o:w===null?w=Object.getOwnPropertyDescriptor(o,p):w;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")y=Reflect.decorate(s,o,p,w);else for(var b=s.length-1;b>=0;b--)(E=s[b])&&(y=(k<3?E(y):k>3?E(o,p,y):E(o,p))||y);return k>3&&y&&Object.defineProperty(o,p,y),y},S=this&&this.__param||function(s,o){return function(p,w){o(p,w,s)}};Object.defineProperty(n,"__esModule",{value:!0}),n.CharSizeService=void 0;const u=c(2585),_=c(8460),g=c(844);let C=n.CharSizeService=class extends g.Disposable{get hasValidSize(){return this.width>0&&this.height>0}constructor(s,o,p){super(),this._optionsService=p,this.width=0,this.height=0,this._onCharSizeChange=this.register(new _.EventEmitter),this.onCharSizeChange=this._onCharSizeChange.event;try{this._measureStrategy=this.register(new a(this._optionsService))}catch{this._measureStrategy=this.register(new h(s,o,this._optionsService))}this.register(this._optionsService.onMultipleOptionChange(["fontFamily","fontSize"],()=>this.measure()))}measure(){const s=this._measureStrategy.measure();s.width===this.width&&s.height===this.height||(this.width=s.width,this.height=s.height,this._onCharSizeChange.fire())}};n.CharSizeService=C=f([S(2,u.IOptionsService)],C);class v extends g.Disposable{constructor(){super(...arguments),this._result={width:0,height:0}}_validateAndSet(o,p){o!==void 0&&o>0&&p!==void 0&&p>0&&(this._result.width=o,this._result.height=p)}}class h extends v{constructor(o,p,w){super(),this._document=o,this._parentElement=p,this._optionsService=w,this._measureElement=this._document.createElement("span"),this._measureElement.classList.add("xterm-char-measure-element"),this._measureElement.textContent="W".repeat(32),this._measureElement.setAttribute("aria-hidden","true"),this._measureElement.style.whiteSpace="pre",this._measureElement.style.fontKerning="none",this._parentElement.appendChild(this._measureElement)}measure(){return this._measureElement.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._measureElement.style.fontSize=`${this._optionsService.rawOptions.fontSize}px`,this._validateAndSet(Number(this._measureElement.offsetWidth)/32,Number(this._measureElement.offsetHeight)),this._result}}class a extends v{constructor(o){super(),this._optionsService=o,this._canvas=new OffscreenCanvas(100,100),this._ctx=this._canvas.getContext("2d");const p=this._ctx.measureText("W");if(!("width"in p&&"fontBoundingBoxAscent"in p&&"fontBoundingBoxDescent"in p))throw new Error("Required font metrics not supported")}measure(){this._ctx.font=`${this._optionsService.rawOptions.fontSize}px ${this._optionsService.rawOptions.fontFamily}`;const o=this._ctx.measureText("W");return this._validateAndSet(o.width,o.fontBoundingBoxAscent+o.fontBoundingBoxDescent),this._result}}},4269:function(m,n,c){var f=this&&this.__decorate||function(a,s,o,p){var w,E=arguments.length,k=E<3?s:p===null?p=Object.getOwnPropertyDescriptor(s,o):p;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")k=Reflect.decorate(a,s,o,p);else for(var y=a.length-1;y>=0;y--)(w=a[y])&&(k=(E<3?w(k):E>3?w(s,o,k):w(s,o))||k);return E>3&&k&&Object.defineProperty(s,o,k),k},S=this&&this.__param||function(a,s){return function(o,p){s(o,p,a)}};Object.defineProperty(n,"__esModule",{value:!0}),n.CharacterJoinerService=n.JoinedCellData=void 0;const u=c(3734),_=c(643),g=c(511),C=c(2585);class v extends u.AttributeData{constructor(s,o,p){super(),this.content=0,this.combinedData="",this.fg=s.fg,this.bg=s.bg,this.combinedData=o,this._width=p}isCombined(){return 2097152}getWidth(){return this._width}getChars(){return this.combinedData}getCode(){return 2097151}setFromCharData(s){throw new Error("not implemented")}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}n.JoinedCellData=v;let h=n.CharacterJoinerService=class Au{constructor(s){this._bufferService=s,this._characterJoiners=[],this._nextCharacterJoinerId=0,this._workCell=new g.CellData}register(s){const o={id:this._nextCharacterJoinerId++,handler:s};return this._characterJoiners.push(o),o.id}deregister(s){for(let o=0;o<this._characterJoiners.length;o++)if(this._characterJoiners[o].id===s)return this._characterJoiners.splice(o,1),!0;return!1}getJoinedCharacters(s){if(this._characterJoiners.length===0)return[];const o=this._bufferService.buffer.lines.get(s);if(!o||o.length===0)return[];const p=[],w=o.translateToString(!0);let E=0,k=0,y=0,b=o.getFg(0),D=o.getBg(0);for(let B=0;B<o.getTrimmedLength();B++)if(o.loadCell(B,this._workCell),this._workCell.getWidth()!==0){if(this._workCell.fg!==b||this._workCell.bg!==D){if(B-E>1){const O=this._getJoinedRanges(w,y,k,o,E);for(let T=0;T<O.length;T++)p.push(O[T])}E=B,y=k,b=this._workCell.fg,D=this._workCell.bg}k+=this._workCell.getChars().length||_.WHITESPACE_CELL_CHAR.length}if(this._bufferService.cols-E>1){const B=this._getJoinedRanges(w,y,k,o,E);for(let O=0;O<B.length;O++)p.push(B[O])}return p}_getJoinedRanges(s,o,p,w,E){const k=s.substring(o,p);let y=[];try{y=this._characterJoiners[0].handler(k)}catch(b){console.error(b)}for(let b=1;b<this._characterJoiners.length;b++)try{const D=this._characterJoiners[b].handler(k);for(let B=0;B<D.length;B++)Au._mergeRanges(y,D[B])}catch(D){console.error(D)}return this._stringRangesToCellRanges(y,w,E),y}_stringRangesToCellRanges(s,o,p){let w=0,E=!1,k=0,y=s[w];if(y){for(let b=p;b<this._bufferService.cols;b++){const D=o.getWidth(b),B=o.getString(b).length||_.WHITESPACE_CELL_CHAR.length;if(D!==0){if(!E&&y[0]<=k&&(y[0]=b,E=!0),y[1]<=k){if(y[1]=b,y=s[++w],!y)break;y[0]<=k?(y[0]=b,E=!0):E=!1}k+=B}}y&&(y[1]=this._bufferService.cols)}}static _mergeRanges(s,o){let p=!1;for(let w=0;w<s.length;w++){const E=s[w];if(p){if(o[1]<=E[0])return s[w-1][1]=o[1],s;if(o[1]<=E[1])return s[w-1][1]=Math.max(o[1],E[1]),s.splice(w,1),s;s.splice(w,1),w--}else{if(o[1]<=E[0])return s.splice(w,0,o),s;if(o[1]<=E[1])return E[0]=Math.min(o[0],E[0]),s;o[0]<E[1]&&(E[0]=Math.min(o[0],E[0]),p=!0)}}return p?s[s.length-1][1]=o[1]:s.push(o),s}};n.CharacterJoinerService=h=f([S(0,C.IBufferService)],h)},5114:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.CoreBrowserService=void 0;const f=c(844),S=c(8460),u=c(3656);class _ extends f.Disposable{constructor(v,h,a){super(),this._textarea=v,this._window=h,this.mainDocument=a,this._isFocused=!1,this._cachedIsFocused=void 0,this._screenDprMonitor=new g(this._window),this._onDprChange=this.register(new S.EventEmitter),this.onDprChange=this._onDprChange.event,this._onWindowChange=this.register(new S.EventEmitter),this.onWindowChange=this._onWindowChange.event,this.register(this.onWindowChange(s=>this._screenDprMonitor.setWindow(s))),this.register((0,S.forwardEvent)(this._screenDprMonitor.onDprChange,this._onDprChange)),this._textarea.addEventListener("focus",()=>this._isFocused=!0),this._textarea.addEventListener("blur",()=>this._isFocused=!1)}get window(){return this._window}set window(v){this._window!==v&&(this._window=v,this._onWindowChange.fire(this._window))}get dpr(){return this.window.devicePixelRatio}get isFocused(){return this._cachedIsFocused===void 0&&(this._cachedIsFocused=this._isFocused&&this._textarea.ownerDocument.hasFocus(),queueMicrotask(()=>this._cachedIsFocused=void 0)),this._cachedIsFocused}}n.CoreBrowserService=_;class g extends f.Disposable{constructor(v){super(),this._parentWindow=v,this._windowResizeListener=this.register(new f.MutableDisposable),this._onDprChange=this.register(new S.EventEmitter),this.onDprChange=this._onDprChange.event,this._outerListener=()=>this._setDprAndFireIfDiffers(),this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this._updateDpr(),this._setWindowResizeListener(),this.register((0,f.toDisposable)(()=>this.clearListener()))}setWindow(v){this._parentWindow=v,this._setWindowResizeListener(),this._setDprAndFireIfDiffers()}_setWindowResizeListener(){this._windowResizeListener.value=(0,u.addDisposableDomListener)(this._parentWindow,"resize",()=>this._setDprAndFireIfDiffers())}_setDprAndFireIfDiffers(){this._parentWindow.devicePixelRatio!==this._currentDevicePixelRatio&&this._onDprChange.fire(this._parentWindow.devicePixelRatio),this._updateDpr()}_updateDpr(){var v;this._outerListener&&((v=this._resolutionMediaMatchList)==null||v.removeListener(this._outerListener),this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this._resolutionMediaMatchList=this._parentWindow.matchMedia(`screen and (resolution: ${this._parentWindow.devicePixelRatio}dppx)`),this._resolutionMediaMatchList.addListener(this._outerListener))}clearListener(){this._resolutionMediaMatchList&&this._outerListener&&(this._resolutionMediaMatchList.removeListener(this._outerListener),this._resolutionMediaMatchList=void 0,this._outerListener=void 0)}}},779:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.LinkProviderService=void 0;const f=c(844);class S extends f.Disposable{constructor(){super(),this.linkProviders=[],this.register((0,f.toDisposable)(()=>this.linkProviders.length=0))}registerLinkProvider(_){return this.linkProviders.push(_),{dispose:()=>{const g=this.linkProviders.indexOf(_);g!==-1&&this.linkProviders.splice(g,1)}}}}n.LinkProviderService=S},8934:function(m,n,c){var f=this&&this.__decorate||function(C,v,h,a){var s,o=arguments.length,p=o<3?v:a===null?a=Object.getOwnPropertyDescriptor(v,h):a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")p=Reflect.decorate(C,v,h,a);else for(var w=C.length-1;w>=0;w--)(s=C[w])&&(p=(o<3?s(p):o>3?s(v,h,p):s(v,h))||p);return o>3&&p&&Object.defineProperty(v,h,p),p},S=this&&this.__param||function(C,v){return function(h,a){v(h,a,C)}};Object.defineProperty(n,"__esModule",{value:!0}),n.MouseService=void 0;const u=c(4725),_=c(9806);let g=n.MouseService=class{constructor(C,v){this._renderService=C,this._charSizeService=v}getCoords(C,v,h,a,s){return(0,_.getCoords)(window,C,v,h,a,this._charSizeService.hasValidSize,this._renderService.dimensions.css.cell.width,this._renderService.dimensions.css.cell.height,s)}getMouseReportCoords(C,v){const h=(0,_.getCoordsRelativeToElement)(window,C,v);if(this._charSizeService.hasValidSize)return h[0]=Math.min(Math.max(h[0],0),this._renderService.dimensions.css.canvas.width-1),h[1]=Math.min(Math.max(h[1],0),this._renderService.dimensions.css.canvas.height-1),{col:Math.floor(h[0]/this._renderService.dimensions.css.cell.width),row:Math.floor(h[1]/this._renderService.dimensions.css.cell.height),x:Math.floor(h[0]),y:Math.floor(h[1])}}};n.MouseService=g=f([S(0,u.IRenderService),S(1,u.ICharSizeService)],g)},3230:function(m,n,c){var f=this&&this.__decorate||function(s,o,p,w){var E,k=arguments.length,y=k<3?o:w===null?w=Object.getOwnPropertyDescriptor(o,p):w;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")y=Reflect.decorate(s,o,p,w);else for(var b=s.length-1;b>=0;b--)(E=s[b])&&(y=(k<3?E(y):k>3?E(o,p,y):E(o,p))||y);return k>3&&y&&Object.defineProperty(o,p,y),y},S=this&&this.__param||function(s,o){return function(p,w){o(p,w,s)}};Object.defineProperty(n,"__esModule",{value:!0}),n.RenderService=void 0;const u=c(6193),_=c(4725),g=c(8460),C=c(844),v=c(7226),h=c(2585);let a=n.RenderService=class extends C.Disposable{get dimensions(){return this._renderer.value.dimensions}constructor(s,o,p,w,E,k,y,b){super(),this._rowCount=s,this._charSizeService=w,this._renderer=this.register(new C.MutableDisposable),this._pausedResizeTask=new v.DebouncedIdleTask,this._observerDisposable=this.register(new C.MutableDisposable),this._isPaused=!1,this._needsFullRefresh=!1,this._isNextRenderRedrawOnly=!0,this._needsSelectionRefresh=!1,this._canvasWidth=0,this._canvasHeight=0,this._selectionState={start:void 0,end:void 0,columnSelectMode:!1},this._onDimensionsChange=this.register(new g.EventEmitter),this.onDimensionsChange=this._onDimensionsChange.event,this._onRenderedViewportChange=this.register(new g.EventEmitter),this.onRenderedViewportChange=this._onRenderedViewportChange.event,this._onRender=this.register(new g.EventEmitter),this.onRender=this._onRender.event,this._onRefreshRequest=this.register(new g.EventEmitter),this.onRefreshRequest=this._onRefreshRequest.event,this._renderDebouncer=new u.RenderDebouncer((D,B)=>this._renderRows(D,B),y),this.register(this._renderDebouncer),this.register(y.onDprChange(()=>this.handleDevicePixelRatioChange())),this.register(k.onResize(()=>this._fullRefresh())),this.register(k.buffers.onBufferActivate(()=>{var D;return(D=this._renderer.value)==null?void 0:D.clear()})),this.register(p.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._charSizeService.onCharSizeChange(()=>this.handleCharSizeChanged())),this.register(E.onDecorationRegistered(()=>this._fullRefresh())),this.register(E.onDecorationRemoved(()=>this._fullRefresh())),this.register(p.onMultipleOptionChange(["customGlyphs","drawBoldTextInBrightColors","letterSpacing","lineHeight","fontFamily","fontSize","fontWeight","fontWeightBold","minimumContrastRatio","rescaleOverlappingGlyphs"],()=>{this.clear(),this.handleResize(k.cols,k.rows),this._fullRefresh()})),this.register(p.onMultipleOptionChange(["cursorBlink","cursorStyle"],()=>this.refreshRows(k.buffer.y,k.buffer.y,!0))),this.register(b.onChangeColors(()=>this._fullRefresh())),this._registerIntersectionObserver(y.window,o),this.register(y.onWindowChange(D=>this._registerIntersectionObserver(D,o)))}_registerIntersectionObserver(s,o){if("IntersectionObserver"in s){const p=new s.IntersectionObserver(w=>this._handleIntersectionChange(w[w.length-1]),{threshold:0});p.observe(o),this._observerDisposable.value=(0,C.toDisposable)(()=>p.disconnect())}}_handleIntersectionChange(s){this._isPaused=s.isIntersecting===void 0?s.intersectionRatio===0:!s.isIntersecting,this._isPaused||this._charSizeService.hasValidSize||this._charSizeService.measure(),!this._isPaused&&this._needsFullRefresh&&(this._pausedResizeTask.flush(),this.refreshRows(0,this._rowCount-1),this._needsFullRefresh=!1)}refreshRows(s,o,p=!1){this._isPaused?this._needsFullRefresh=!0:(p||(this._isNextRenderRedrawOnly=!1),this._renderDebouncer.refresh(s,o,this._rowCount))}_renderRows(s,o){this._renderer.value&&(s=Math.min(s,this._rowCount-1),o=Math.min(o,this._rowCount-1),this._renderer.value.renderRows(s,o),this._needsSelectionRefresh&&(this._renderer.value.handleSelectionChanged(this._selectionState.start,this._selectionState.end,this._selectionState.columnSelectMode),this._needsSelectionRefresh=!1),this._isNextRenderRedrawOnly||this._onRenderedViewportChange.fire({start:s,end:o}),this._onRender.fire({start:s,end:o}),this._isNextRenderRedrawOnly=!0)}resize(s,o){this._rowCount=o,this._fireOnCanvasResize()}_handleOptionsChanged(){this._renderer.value&&(this.refreshRows(0,this._rowCount-1),this._fireOnCanvasResize())}_fireOnCanvasResize(){this._renderer.value&&(this._renderer.value.dimensions.css.canvas.width===this._canvasWidth&&this._renderer.value.dimensions.css.canvas.height===this._canvasHeight||this._onDimensionsChange.fire(this._renderer.value.dimensions))}hasRenderer(){return!!this._renderer.value}setRenderer(s){this._renderer.value=s,this._renderer.value&&(this._renderer.value.onRequestRedraw(o=>this.refreshRows(o.start,o.end,!0)),this._needsSelectionRefresh=!0,this._fullRefresh())}addRefreshCallback(s){return this._renderDebouncer.addRefreshCallback(s)}_fullRefresh(){this._isPaused?this._needsFullRefresh=!0:this.refreshRows(0,this._rowCount-1)}clearTextureAtlas(){var s,o;this._renderer.value&&((o=(s=this._renderer.value).clearTextureAtlas)==null||o.call(s),this._fullRefresh())}handleDevicePixelRatioChange(){this._charSizeService.measure(),this._renderer.value&&(this._renderer.value.handleDevicePixelRatioChange(),this.refreshRows(0,this._rowCount-1))}handleResize(s,o){this._renderer.value&&(this._isPaused?this._pausedResizeTask.set(()=>{var p;return(p=this._renderer.value)==null?void 0:p.handleResize(s,o)}):this._renderer.value.handleResize(s,o),this._fullRefresh())}handleCharSizeChanged(){var s;(s=this._renderer.value)==null||s.handleCharSizeChanged()}handleBlur(){var s;(s=this._renderer.value)==null||s.handleBlur()}handleFocus(){var s;(s=this._renderer.value)==null||s.handleFocus()}handleSelectionChanged(s,o,p){var w;this._selectionState.start=s,this._selectionState.end=o,this._selectionState.columnSelectMode=p,(w=this._renderer.value)==null||w.handleSelectionChanged(s,o,p)}handleCursorMove(){var s;(s=this._renderer.value)==null||s.handleCursorMove()}clear(){var s;(s=this._renderer.value)==null||s.clear()}};n.RenderService=a=f([S(2,h.IOptionsService),S(3,_.ICharSizeService),S(4,h.IDecorationService),S(5,h.IBufferService),S(6,_.ICoreBrowserService),S(7,_.IThemeService)],a)},9312:function(m,n,c){var f=this&&this.__decorate||function(y,b,D,B){var O,T=arguments.length,H=T<3?b:B===null?B=Object.getOwnPropertyDescriptor(b,D):B;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")H=Reflect.decorate(y,b,D,B);else for(var j=y.length-1;j>=0;j--)(O=y[j])&&(H=(T<3?O(H):T>3?O(b,D,H):O(b,D))||H);return T>3&&H&&Object.defineProperty(b,D,H),H},S=this&&this.__param||function(y,b){return function(D,B){b(D,B,y)}};Object.defineProperty(n,"__esModule",{value:!0}),n.SelectionService=void 0;const u=c(9806),_=c(9504),g=c(456),C=c(4725),v=c(8460),h=c(844),a=c(6114),s=c(4841),o=c(511),p=c(2585),w=" ",E=new RegExp(w,"g");let k=n.SelectionService=class extends h.Disposable{constructor(y,b,D,B,O,T,H,j,$){super(),this._element=y,this._screenElement=b,this._linkifier=D,this._bufferService=B,this._coreService=O,this._mouseService=T,this._optionsService=H,this._renderService=j,this._coreBrowserService=$,this._dragScrollAmount=0,this._enabled=!0,this._workCell=new o.CellData,this._mouseDownTimeStamp=0,this._oldHasSelection=!1,this._oldSelectionStart=void 0,this._oldSelectionEnd=void 0,this._onLinuxMouseSelection=this.register(new v.EventEmitter),this.onLinuxMouseSelection=this._onLinuxMouseSelection.event,this._onRedrawRequest=this.register(new v.EventEmitter),this.onRequestRedraw=this._onRedrawRequest.event,this._onSelectionChange=this.register(new v.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onRequestScrollLines=this.register(new v.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this._mouseMoveListener=q=>this._handleMouseMove(q),this._mouseUpListener=q=>this._handleMouseUp(q),this._coreService.onUserInput(()=>{this.hasSelection&&this.clearSelection()}),this._trimListener=this._bufferService.buffer.lines.onTrim(q=>this._handleTrim(q)),this.register(this._bufferService.buffers.onBufferActivate(q=>this._handleBufferActivate(q))),this.enable(),this._model=new g.SelectionModel(this._bufferService),this._activeSelectionMode=0,this.register((0,h.toDisposable)(()=>{this._removeMouseDownListeners()}))}reset(){this.clearSelection()}disable(){this.clearSelection(),this._enabled=!1}enable(){this._enabled=!0}get selectionStart(){return this._model.finalSelectionStart}get selectionEnd(){return this._model.finalSelectionEnd}get hasSelection(){const y=this._model.finalSelectionStart,b=this._model.finalSelectionEnd;return!(!y||!b||y[0]===b[0]&&y[1]===b[1])}get selectionText(){const y=this._model.finalSelectionStart,b=this._model.finalSelectionEnd;if(!y||!b)return"";const D=this._bufferService.buffer,B=[];if(this._activeSelectionMode===3){if(y[0]===b[0])return"";const O=y[0]<b[0]?y[0]:b[0],T=y[0]<b[0]?b[0]:y[0];for(let H=y[1];H<=b[1];H++){const j=D.translateBufferLineToString(H,!0,O,T);B.push(j)}}else{const O=y[1]===b[1]?b[0]:void 0;B.push(D.translateBufferLineToString(y[1],!0,y[0],O));for(let T=y[1]+1;T<=b[1]-1;T++){const H=D.lines.get(T),j=D.translateBufferLineToString(T,!0);H!=null&&H.isWrapped?B[B.length-1]+=j:B.push(j)}if(y[1]!==b[1]){const T=D.lines.get(b[1]),H=D.translateBufferLineToString(b[1],!0,0,b[0]);T&&T.isWrapped?B[B.length-1]+=H:B.push(H)}}return B.map(O=>O.replace(E," ")).join(a.isWindows?`\r
`:`
`)}clearSelection(){this._model.clearSelection(),this._removeMouseDownListeners(),this.refresh(),this._onSelectionChange.fire()}refresh(y){this._refreshAnimationFrame||(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._refresh())),a.isLinux&&y&&this.selectionText.length&&this._onLinuxMouseSelection.fire(this.selectionText)}_refresh(){this._refreshAnimationFrame=void 0,this._onRedrawRequest.fire({start:this._model.finalSelectionStart,end:this._model.finalSelectionEnd,columnSelectMode:this._activeSelectionMode===3})}_isClickInSelection(y){const b=this._getMouseBufferCoords(y),D=this._model.finalSelectionStart,B=this._model.finalSelectionEnd;return!!(D&&B&&b)&&this._areCoordsInSelection(b,D,B)}isCellInSelection(y,b){const D=this._model.finalSelectionStart,B=this._model.finalSelectionEnd;return!(!D||!B)&&this._areCoordsInSelection([y,b],D,B)}_areCoordsInSelection(y,b,D){return y[1]>b[1]&&y[1]<D[1]||b[1]===D[1]&&y[1]===b[1]&&y[0]>=b[0]&&y[0]<D[0]||b[1]<D[1]&&y[1]===D[1]&&y[0]<D[0]||b[1]<D[1]&&y[1]===b[1]&&y[0]>=b[0]}_selectWordAtCursor(y,b){var O,T;const D=(T=(O=this._linkifier.currentLink)==null?void 0:O.link)==null?void 0:T.range;if(D)return this._model.selectionStart=[D.start.x-1,D.start.y-1],this._model.selectionStartLength=(0,s.getRangeLength)(D,this._bufferService.cols),this._model.selectionEnd=void 0,!0;const B=this._getMouseBufferCoords(y);return!!B&&(this._selectWordAt(B,b),this._model.selectionEnd=void 0,!0)}selectAll(){this._model.isSelectAllActive=!0,this.refresh(),this._onSelectionChange.fire()}selectLines(y,b){this._model.clearSelection(),y=Math.max(y,0),b=Math.min(b,this._bufferService.buffer.lines.length-1),this._model.selectionStart=[0,y],this._model.selectionEnd=[this._bufferService.cols,b],this.refresh(),this._onSelectionChange.fire()}_handleTrim(y){this._model.handleTrim(y)&&this.refresh()}_getMouseBufferCoords(y){const b=this._mouseService.getCoords(y,this._screenElement,this._bufferService.cols,this._bufferService.rows,!0);if(b)return b[0]--,b[1]--,b[1]+=this._bufferService.buffer.ydisp,b}_getMouseEventScrollAmount(y){let b=(0,u.getCoordsRelativeToElement)(this._coreBrowserService.window,y,this._screenElement)[1];const D=this._renderService.dimensions.css.canvas.height;return b>=0&&b<=D?0:(b>D&&(b-=D),b=Math.min(Math.max(b,-50),50),b/=50,b/Math.abs(b)+Math.round(14*b))}shouldForceSelection(y){return a.isMac?y.altKey&&this._optionsService.rawOptions.macOptionClickForcesSelection:y.shiftKey}handleMouseDown(y){if(this._mouseDownTimeStamp=y.timeStamp,(y.button!==2||!this.hasSelection)&&y.button===0){if(!this._enabled){if(!this.shouldForceSelection(y))return;y.stopPropagation()}y.preventDefault(),this._dragScrollAmount=0,this._enabled&&y.shiftKey?this._handleIncrementalClick(y):y.detail===1?this._handleSingleClick(y):y.detail===2?this._handleDoubleClick(y):y.detail===3&&this._handleTripleClick(y),this._addMouseDownListeners(),this.refresh(!0)}}_addMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.addEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.addEventListener("mouseup",this._mouseUpListener)),this._dragScrollIntervalTimer=this._coreBrowserService.window.setInterval(()=>this._dragScroll(),50)}_removeMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.removeEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.removeEventListener("mouseup",this._mouseUpListener)),this._coreBrowserService.window.clearInterval(this._dragScrollIntervalTimer),this._dragScrollIntervalTimer=void 0}_handleIncrementalClick(y){this._model.selectionStart&&(this._model.selectionEnd=this._getMouseBufferCoords(y))}_handleSingleClick(y){if(this._model.selectionStartLength=0,this._model.isSelectAllActive=!1,this._activeSelectionMode=this.shouldColumnSelect(y)?3:0,this._model.selectionStart=this._getMouseBufferCoords(y),!this._model.selectionStart)return;this._model.selectionEnd=void 0;const b=this._bufferService.buffer.lines.get(this._model.selectionStart[1]);b&&b.length!==this._model.selectionStart[0]&&b.hasWidth(this._model.selectionStart[0])===0&&this._model.selectionStart[0]++}_handleDoubleClick(y){this._selectWordAtCursor(y,!0)&&(this._activeSelectionMode=1)}_handleTripleClick(y){const b=this._getMouseBufferCoords(y);b&&(this._activeSelectionMode=2,this._selectLineAt(b[1]))}shouldColumnSelect(y){return y.altKey&&!(a.isMac&&this._optionsService.rawOptions.macOptionClickForcesSelection)}_handleMouseMove(y){if(y.stopImmediatePropagation(),!this._model.selectionStart)return;const b=this._model.selectionEnd?[this._model.selectionEnd[0],this._model.selectionEnd[1]]:null;if(this._model.selectionEnd=this._getMouseBufferCoords(y),!this._model.selectionEnd)return void this.refresh(!0);this._activeSelectionMode===2?this._model.selectionEnd[1]<this._model.selectionStart[1]?this._model.selectionEnd[0]=0:this._model.selectionEnd[0]=this._bufferService.cols:this._activeSelectionMode===1&&this._selectToWordAt(this._model.selectionEnd),this._dragScrollAmount=this._getMouseEventScrollAmount(y),this._activeSelectionMode!==3&&(this._dragScrollAmount>0?this._model.selectionEnd[0]=this._bufferService.cols:this._dragScrollAmount<0&&(this._model.selectionEnd[0]=0));const D=this._bufferService.buffer;if(this._model.selectionEnd[1]<D.lines.length){const B=D.lines.get(this._model.selectionEnd[1]);B&&B.hasWidth(this._model.selectionEnd[0])===0&&this._model.selectionEnd[0]<this._bufferService.cols&&this._model.selectionEnd[0]++}b&&b[0]===this._model.selectionEnd[0]&&b[1]===this._model.selectionEnd[1]||this.refresh(!0)}_dragScroll(){if(this._model.selectionEnd&&this._model.selectionStart&&this._dragScrollAmount){this._onRequestScrollLines.fire({amount:this._dragScrollAmount,suppressScrollEvent:!1});const y=this._bufferService.buffer;this._dragScrollAmount>0?(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=this._bufferService.cols),this._model.selectionEnd[1]=Math.min(y.ydisp+this._bufferService.rows,y.lines.length-1)):(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=0),this._model.selectionEnd[1]=y.ydisp),this.refresh()}}_handleMouseUp(y){const b=y.timeStamp-this._mouseDownTimeStamp;if(this._removeMouseDownListeners(),this.selectionText.length<=1&&b<500&&y.altKey&&this._optionsService.rawOptions.altClickMovesCursor){if(this._bufferService.buffer.ybase===this._bufferService.buffer.ydisp){const D=this._mouseService.getCoords(y,this._element,this._bufferService.cols,this._bufferService.rows,!1);if(D&&D[0]!==void 0&&D[1]!==void 0){const B=(0,_.moveToCellSequence)(D[0]-1,D[1]-1,this._bufferService,this._coreService.decPrivateModes.applicationCursorKeys);this._coreService.triggerDataEvent(B,!0)}}}else this._fireEventIfSelectionChanged()}_fireEventIfSelectionChanged(){const y=this._model.finalSelectionStart,b=this._model.finalSelectionEnd,D=!(!y||!b||y[0]===b[0]&&y[1]===b[1]);D?y&&b&&(this._oldSelectionStart&&this._oldSelectionEnd&&y[0]===this._oldSelectionStart[0]&&y[1]===this._oldSelectionStart[1]&&b[0]===this._oldSelectionEnd[0]&&b[1]===this._oldSelectionEnd[1]||this._fireOnSelectionChange(y,b,D)):this._oldHasSelection&&this._fireOnSelectionChange(y,b,D)}_fireOnSelectionChange(y,b,D){this._oldSelectionStart=y,this._oldSelectionEnd=b,this._oldHasSelection=D,this._onSelectionChange.fire()}_handleBufferActivate(y){this.clearSelection(),this._trimListener.dispose(),this._trimListener=y.activeBuffer.lines.onTrim(b=>this._handleTrim(b))}_convertViewportColToCharacterIndex(y,b){let D=b;for(let B=0;b>=B;B++){const O=y.loadCell(B,this._workCell).getChars().length;this._workCell.getWidth()===0?D--:O>1&&b!==B&&(D+=O-1)}return D}setSelection(y,b,D){this._model.clearSelection(),this._removeMouseDownListeners(),this._model.selectionStart=[y,b],this._model.selectionStartLength=D,this.refresh(),this._fireEventIfSelectionChanged()}rightClickSelect(y){this._isClickInSelection(y)||(this._selectWordAtCursor(y,!1)&&this.refresh(!0),this._fireEventIfSelectionChanged())}_getWordAt(y,b,D=!0,B=!0){if(y[0]>=this._bufferService.cols)return;const O=this._bufferService.buffer,T=O.lines.get(y[1]);if(!T)return;const H=O.translateBufferLineToString(y[1],!1);let j=this._convertViewportColToCharacterIndex(T,y[0]),$=j;const q=y[0]-j;let N=0,x=0,R=0,A=0;if(H.charAt(j)===" "){for(;j>0&&H.charAt(j-1)===" ";)j--;for(;$<H.length&&H.charAt($+1)===" ";)$++}else{let V=y[0],Y=y[0];T.getWidth(V)===0&&(N++,V--),T.getWidth(Y)===2&&(x++,Y++);const Q=T.getString(Y).length;for(Q>1&&(A+=Q-1,$+=Q-1);V>0&&j>0&&!this._isCharWordSeparator(T.loadCell(V-1,this._workCell));){T.loadCell(V-1,this._workCell);const M=this._workCell.getChars().length;this._workCell.getWidth()===0?(N++,V--):M>1&&(R+=M-1,j-=M-1),j--,V--}for(;Y<T.length&&$+1<H.length&&!this._isCharWordSeparator(T.loadCell(Y+1,this._workCell));){T.loadCell(Y+1,this._workCell);const M=this._workCell.getChars().length;this._workCell.getWidth()===2?(x++,Y++):M>1&&(A+=M-1,$+=M-1),$++,Y++}}$++;let P=j+q-N+R,z=Math.min(this._bufferService.cols,$-j+N+x-R-A);if(b||H.slice(j,$).trim()!==""){if(D&&P===0&&T.getCodePoint(0)!==32){const V=O.lines.get(y[1]-1);if(V&&T.isWrapped&&V.getCodePoint(this._bufferService.cols-1)!==32){const Y=this._getWordAt([this._bufferService.cols-1,y[1]-1],!1,!0,!1);if(Y){const Q=this._bufferService.cols-Y.start;P-=Q,z+=Q}}}if(B&&P+z===this._bufferService.cols&&T.getCodePoint(this._bufferService.cols-1)!==32){const V=O.lines.get(y[1]+1);if(V!=null&&V.isWrapped&&V.getCodePoint(0)!==32){const Y=this._getWordAt([0,y[1]+1],!1,!1,!0);Y&&(z+=Y.length)}}return{start:P,length:z}}}_selectWordAt(y,b){const D=this._getWordAt(y,b);if(D){for(;D.start<0;)D.start+=this._bufferService.cols,y[1]--;this._model.selectionStart=[D.start,y[1]],this._model.selectionStartLength=D.length}}_selectToWordAt(y){const b=this._getWordAt(y,!0);if(b){let D=y[1];for(;b.start<0;)b.start+=this._bufferService.cols,D--;if(!this._model.areSelectionValuesReversed())for(;b.start+b.length>this._bufferService.cols;)b.length-=this._bufferService.cols,D++;this._model.selectionEnd=[this._model.areSelectionValuesReversed()?b.start:b.start+b.length,D]}}_isCharWordSeparator(y){return y.getWidth()!==0&&this._optionsService.rawOptions.wordSeparator.indexOf(y.getChars())>=0}_selectLineAt(y){const b=this._bufferService.buffer.getWrappedRangeForLine(y),D={start:{x:0,y:b.first},end:{x:this._bufferService.cols-1,y:b.last}};this._model.selectionStart=[0,b.first],this._model.selectionEnd=void 0,this._model.selectionStartLength=(0,s.getRangeLength)(D,this._bufferService.cols)}};n.SelectionService=k=f([S(3,p.IBufferService),S(4,p.ICoreService),S(5,C.IMouseService),S(6,p.IOptionsService),S(7,C.IRenderService),S(8,C.ICoreBrowserService)],k)},4725:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.ILinkProviderService=n.IThemeService=n.ICharacterJoinerService=n.ISelectionService=n.IRenderService=n.IMouseService=n.ICoreBrowserService=n.ICharSizeService=void 0;const f=c(8343);n.ICharSizeService=(0,f.createDecorator)("CharSizeService"),n.ICoreBrowserService=(0,f.createDecorator)("CoreBrowserService"),n.IMouseService=(0,f.createDecorator)("MouseService"),n.IRenderService=(0,f.createDecorator)("RenderService"),n.ISelectionService=(0,f.createDecorator)("SelectionService"),n.ICharacterJoinerService=(0,f.createDecorator)("CharacterJoinerService"),n.IThemeService=(0,f.createDecorator)("ThemeService"),n.ILinkProviderService=(0,f.createDecorator)("LinkProviderService")},6731:function(m,n,c){var f=this&&this.__decorate||function(k,y,b,D){var B,O=arguments.length,T=O<3?y:D===null?D=Object.getOwnPropertyDescriptor(y,b):D;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")T=Reflect.decorate(k,y,b,D);else for(var H=k.length-1;H>=0;H--)(B=k[H])&&(T=(O<3?B(T):O>3?B(y,b,T):B(y,b))||T);return O>3&&T&&Object.defineProperty(y,b,T),T},S=this&&this.__param||function(k,y){return function(b,D){y(b,D,k)}};Object.defineProperty(n,"__esModule",{value:!0}),n.ThemeService=n.DEFAULT_ANSI_COLORS=void 0;const u=c(7239),_=c(8055),g=c(8460),C=c(844),v=c(2585),h=_.css.toColor("#ffffff"),a=_.css.toColor("#000000"),s=_.css.toColor("#ffffff"),o=_.css.toColor("#000000"),p={css:"rgba(255, 255, 255, 0.3)",rgba:**********};n.DEFAULT_ANSI_COLORS=Object.freeze((()=>{const k=[_.css.toColor("#2e3436"),_.css.toColor("#cc0000"),_.css.toColor("#4e9a06"),_.css.toColor("#c4a000"),_.css.toColor("#3465a4"),_.css.toColor("#75507b"),_.css.toColor("#06989a"),_.css.toColor("#d3d7cf"),_.css.toColor("#555753"),_.css.toColor("#ef2929"),_.css.toColor("#8ae234"),_.css.toColor("#fce94f"),_.css.toColor("#729fcf"),_.css.toColor("#ad7fa8"),_.css.toColor("#34e2e2"),_.css.toColor("#eeeeec")],y=[0,95,135,175,215,255];for(let b=0;b<216;b++){const D=y[b/36%6|0],B=y[b/6%6|0],O=y[b%6];k.push({css:_.channels.toCss(D,B,O),rgba:_.channels.toRgba(D,B,O)})}for(let b=0;b<24;b++){const D=8+10*b;k.push({css:_.channels.toCss(D,D,D),rgba:_.channels.toRgba(D,D,D)})}return k})());let w=n.ThemeService=class extends C.Disposable{get colors(){return this._colors}constructor(k){super(),this._optionsService=k,this._contrastCache=new u.ColorContrastCache,this._halfContrastCache=new u.ColorContrastCache,this._onChangeColors=this.register(new g.EventEmitter),this.onChangeColors=this._onChangeColors.event,this._colors={foreground:h,background:a,cursor:s,cursorAccent:o,selectionForeground:void 0,selectionBackgroundTransparent:p,selectionBackgroundOpaque:_.color.blend(a,p),selectionInactiveBackgroundTransparent:p,selectionInactiveBackgroundOpaque:_.color.blend(a,p),ansi:n.DEFAULT_ANSI_COLORS.slice(),contrastCache:this._contrastCache,halfContrastCache:this._halfContrastCache},this._updateRestoreColors(),this._setTheme(this._optionsService.rawOptions.theme),this.register(this._optionsService.onSpecificOptionChange("minimumContrastRatio",()=>this._contrastCache.clear())),this.register(this._optionsService.onSpecificOptionChange("theme",()=>this._setTheme(this._optionsService.rawOptions.theme)))}_setTheme(k={}){const y=this._colors;if(y.foreground=E(k.foreground,h),y.background=E(k.background,a),y.cursor=E(k.cursor,s),y.cursorAccent=E(k.cursorAccent,o),y.selectionBackgroundTransparent=E(k.selectionBackground,p),y.selectionBackgroundOpaque=_.color.blend(y.background,y.selectionBackgroundTransparent),y.selectionInactiveBackgroundTransparent=E(k.selectionInactiveBackground,y.selectionBackgroundTransparent),y.selectionInactiveBackgroundOpaque=_.color.blend(y.background,y.selectionInactiveBackgroundTransparent),y.selectionForeground=k.selectionForeground?E(k.selectionForeground,_.NULL_COLOR):void 0,y.selectionForeground===_.NULL_COLOR&&(y.selectionForeground=void 0),_.color.isOpaque(y.selectionBackgroundTransparent)&&(y.selectionBackgroundTransparent=_.color.opacity(y.selectionBackgroundTransparent,.3)),_.color.isOpaque(y.selectionInactiveBackgroundTransparent)&&(y.selectionInactiveBackgroundTransparent=_.color.opacity(y.selectionInactiveBackgroundTransparent,.3)),y.ansi=n.DEFAULT_ANSI_COLORS.slice(),y.ansi[0]=E(k.black,n.DEFAULT_ANSI_COLORS[0]),y.ansi[1]=E(k.red,n.DEFAULT_ANSI_COLORS[1]),y.ansi[2]=E(k.green,n.DEFAULT_ANSI_COLORS[2]),y.ansi[3]=E(k.yellow,n.DEFAULT_ANSI_COLORS[3]),y.ansi[4]=E(k.blue,n.DEFAULT_ANSI_COLORS[4]),y.ansi[5]=E(k.magenta,n.DEFAULT_ANSI_COLORS[5]),y.ansi[6]=E(k.cyan,n.DEFAULT_ANSI_COLORS[6]),y.ansi[7]=E(k.white,n.DEFAULT_ANSI_COLORS[7]),y.ansi[8]=E(k.brightBlack,n.DEFAULT_ANSI_COLORS[8]),y.ansi[9]=E(k.brightRed,n.DEFAULT_ANSI_COLORS[9]),y.ansi[10]=E(k.brightGreen,n.DEFAULT_ANSI_COLORS[10]),y.ansi[11]=E(k.brightYellow,n.DEFAULT_ANSI_COLORS[11]),y.ansi[12]=E(k.brightBlue,n.DEFAULT_ANSI_COLORS[12]),y.ansi[13]=E(k.brightMagenta,n.DEFAULT_ANSI_COLORS[13]),y.ansi[14]=E(k.brightCyan,n.DEFAULT_ANSI_COLORS[14]),y.ansi[15]=E(k.brightWhite,n.DEFAULT_ANSI_COLORS[15]),k.extendedAnsi){const b=Math.min(y.ansi.length-16,k.extendedAnsi.length);for(let D=0;D<b;D++)y.ansi[D+16]=E(k.extendedAnsi[D],n.DEFAULT_ANSI_COLORS[D+16])}this._contrastCache.clear(),this._halfContrastCache.clear(),this._updateRestoreColors(),this._onChangeColors.fire(this.colors)}restoreColor(k){this._restoreColor(k),this._onChangeColors.fire(this.colors)}_restoreColor(k){if(k!==void 0)switch(k){case 256:this._colors.foreground=this._restoreColors.foreground;break;case 257:this._colors.background=this._restoreColors.background;break;case 258:this._colors.cursor=this._restoreColors.cursor;break;default:this._colors.ansi[k]=this._restoreColors.ansi[k]}else for(let y=0;y<this._restoreColors.ansi.length;++y)this._colors.ansi[y]=this._restoreColors.ansi[y]}modifyColors(k){k(this._colors),this._onChangeColors.fire(this.colors)}_updateRestoreColors(){this._restoreColors={foreground:this._colors.foreground,background:this._colors.background,cursor:this._colors.cursor,ansi:this._colors.ansi.slice()}}};function E(k,y){if(k!==void 0)try{return _.css.toColor(k)}catch{}return y}n.ThemeService=w=f([S(0,v.IOptionsService)],w)},6349:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.CircularList=void 0;const f=c(8460),S=c(844);class u extends S.Disposable{constructor(g){super(),this._maxLength=g,this.onDeleteEmitter=this.register(new f.EventEmitter),this.onDelete=this.onDeleteEmitter.event,this.onInsertEmitter=this.register(new f.EventEmitter),this.onInsert=this.onInsertEmitter.event,this.onTrimEmitter=this.register(new f.EventEmitter),this.onTrim=this.onTrimEmitter.event,this._array=new Array(this._maxLength),this._startIndex=0,this._length=0}get maxLength(){return this._maxLength}set maxLength(g){if(this._maxLength===g)return;const C=new Array(g);for(let v=0;v<Math.min(g,this.length);v++)C[v]=this._array[this._getCyclicIndex(v)];this._array=C,this._maxLength=g,this._startIndex=0}get length(){return this._length}set length(g){if(g>this._length)for(let C=this._length;C<g;C++)this._array[C]=void 0;this._length=g}get(g){return this._array[this._getCyclicIndex(g)]}set(g,C){this._array[this._getCyclicIndex(g)]=C}push(g){this._array[this._getCyclicIndex(this._length)]=g,this._length===this._maxLength?(this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1)):this._length++}recycle(){if(this._length!==this._maxLength)throw new Error("Can only recycle when the buffer is full");return this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1),this._array[this._getCyclicIndex(this._length-1)]}get isFull(){return this._length===this._maxLength}pop(){return this._array[this._getCyclicIndex(this._length---1)]}splice(g,C,...v){if(C){for(let h=g;h<this._length-C;h++)this._array[this._getCyclicIndex(h)]=this._array[this._getCyclicIndex(h+C)];this._length-=C,this.onDeleteEmitter.fire({index:g,amount:C})}for(let h=this._length-1;h>=g;h--)this._array[this._getCyclicIndex(h+v.length)]=this._array[this._getCyclicIndex(h)];for(let h=0;h<v.length;h++)this._array[this._getCyclicIndex(g+h)]=v[h];if(v.length&&this.onInsertEmitter.fire({index:g,amount:v.length}),this._length+v.length>this._maxLength){const h=this._length+v.length-this._maxLength;this._startIndex+=h,this._length=this._maxLength,this.onTrimEmitter.fire(h)}else this._length+=v.length}trimStart(g){g>this._length&&(g=this._length),this._startIndex+=g,this._length-=g,this.onTrimEmitter.fire(g)}shiftElements(g,C,v){if(!(C<=0)){if(g<0||g>=this._length)throw new Error("start argument out of range");if(g+v<0)throw new Error("Cannot shift elements in list beyond index 0");if(v>0){for(let a=C-1;a>=0;a--)this.set(g+a+v,this.get(g+a));const h=g+C+v-this._length;if(h>0)for(this._length+=h;this._length>this._maxLength;)this._length--,this._startIndex++,this.onTrimEmitter.fire(1)}else for(let h=0;h<C;h++)this.set(g+h+v,this.get(g+h))}}_getCyclicIndex(g){return(this._startIndex+g)%this._maxLength}}n.CircularList=u},1439:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.clone=void 0,n.clone=function c(f,S=5){if(typeof f!="object")return f;const u=Array.isArray(f)?[]:{};for(const _ in f)u[_]=S<=1?f[_]:f[_]&&c(f[_],S-1);return u}},8055:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.contrastRatio=n.toPaddedHex=n.rgba=n.rgb=n.css=n.color=n.channels=n.NULL_COLOR=void 0;let c=0,f=0,S=0,u=0;var _,g,C,v,h;function a(o){const p=o.toString(16);return p.length<2?"0"+p:p}function s(o,p){return o<p?(p+.05)/(o+.05):(o+.05)/(p+.05)}n.NULL_COLOR={css:"#00000000",rgba:0},function(o){o.toCss=function(p,w,E,k){return k!==void 0?`#${a(p)}${a(w)}${a(E)}${a(k)}`:`#${a(p)}${a(w)}${a(E)}`},o.toRgba=function(p,w,E,k=255){return(p<<24|w<<16|E<<8|k)>>>0},o.toColor=function(p,w,E,k){return{css:o.toCss(p,w,E,k),rgba:o.toRgba(p,w,E,k)}}}(_||(n.channels=_={})),function(o){function p(w,E){return u=Math.round(255*E),[c,f,S]=h.toChannels(w.rgba),{css:_.toCss(c,f,S,u),rgba:_.toRgba(c,f,S,u)}}o.blend=function(w,E){if(u=(255&E.rgba)/255,u===1)return{css:E.css,rgba:E.rgba};const k=E.rgba>>24&255,y=E.rgba>>16&255,b=E.rgba>>8&255,D=w.rgba>>24&255,B=w.rgba>>16&255,O=w.rgba>>8&255;return c=D+Math.round((k-D)*u),f=B+Math.round((y-B)*u),S=O+Math.round((b-O)*u),{css:_.toCss(c,f,S),rgba:_.toRgba(c,f,S)}},o.isOpaque=function(w){return(255&w.rgba)==255},o.ensureContrastRatio=function(w,E,k){const y=h.ensureContrastRatio(w.rgba,E.rgba,k);if(y)return _.toColor(y>>24&255,y>>16&255,y>>8&255)},o.opaque=function(w){const E=(255|w.rgba)>>>0;return[c,f,S]=h.toChannels(E),{css:_.toCss(c,f,S),rgba:E}},o.opacity=p,o.multiplyOpacity=function(w,E){return u=255&w.rgba,p(w,u*E/255)},o.toColorRGB=function(w){return[w.rgba>>24&255,w.rgba>>16&255,w.rgba>>8&255]}}(g||(n.color=g={})),function(o){let p,w;try{const E=document.createElement("canvas");E.width=1,E.height=1;const k=E.getContext("2d",{willReadFrequently:!0});k&&(p=k,p.globalCompositeOperation="copy",w=p.createLinearGradient(0,0,1,1))}catch{}o.toColor=function(E){if(E.match(/#[\da-f]{3,8}/i))switch(E.length){case 4:return c=parseInt(E.slice(1,2).repeat(2),16),f=parseInt(E.slice(2,3).repeat(2),16),S=parseInt(E.slice(3,4).repeat(2),16),_.toColor(c,f,S);case 5:return c=parseInt(E.slice(1,2).repeat(2),16),f=parseInt(E.slice(2,3).repeat(2),16),S=parseInt(E.slice(3,4).repeat(2),16),u=parseInt(E.slice(4,5).repeat(2),16),_.toColor(c,f,S,u);case 7:return{css:E,rgba:(parseInt(E.slice(1),16)<<8|255)>>>0};case 9:return{css:E,rgba:parseInt(E.slice(1),16)>>>0}}const k=E.match(/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(,\s*(0|1|\d?\.(\d+))\s*)?\)/);if(k)return c=parseInt(k[1]),f=parseInt(k[2]),S=parseInt(k[3]),u=Math.round(255*(k[5]===void 0?1:parseFloat(k[5]))),_.toColor(c,f,S,u);if(!p||!w)throw new Error("css.toColor: Unsupported css format");if(p.fillStyle=w,p.fillStyle=E,typeof p.fillStyle!="string")throw new Error("css.toColor: Unsupported css format");if(p.fillRect(0,0,1,1),[c,f,S,u]=p.getImageData(0,0,1,1).data,u!==255)throw new Error("css.toColor: Unsupported css format");return{rgba:_.toRgba(c,f,S,u),css:E}}}(C||(n.css=C={})),function(o){function p(w,E,k){const y=w/255,b=E/255,D=k/255;return .2126*(y<=.03928?y/12.92:Math.pow((y+.055)/1.055,2.4))+.7152*(b<=.03928?b/12.92:Math.pow((b+.055)/1.055,2.4))+.0722*(D<=.03928?D/12.92:Math.pow((D+.055)/1.055,2.4))}o.relativeLuminance=function(w){return p(w>>16&255,w>>8&255,255&w)},o.relativeLuminance2=p}(v||(n.rgb=v={})),function(o){function p(E,k,y){const b=E>>24&255,D=E>>16&255,B=E>>8&255;let O=k>>24&255,T=k>>16&255,H=k>>8&255,j=s(v.relativeLuminance2(O,T,H),v.relativeLuminance2(b,D,B));for(;j<y&&(O>0||T>0||H>0);)O-=Math.max(0,Math.ceil(.1*O)),T-=Math.max(0,Math.ceil(.1*T)),H-=Math.max(0,Math.ceil(.1*H)),j=s(v.relativeLuminance2(O,T,H),v.relativeLuminance2(b,D,B));return(O<<24|T<<16|H<<8|255)>>>0}function w(E,k,y){const b=E>>24&255,D=E>>16&255,B=E>>8&255;let O=k>>24&255,T=k>>16&255,H=k>>8&255,j=s(v.relativeLuminance2(O,T,H),v.relativeLuminance2(b,D,B));for(;j<y&&(O<255||T<255||H<255);)O=Math.min(255,O+Math.ceil(.1*(255-O))),T=Math.min(255,T+Math.ceil(.1*(255-T))),H=Math.min(255,H+Math.ceil(.1*(255-H))),j=s(v.relativeLuminance2(O,T,H),v.relativeLuminance2(b,D,B));return(O<<24|T<<16|H<<8|255)>>>0}o.blend=function(E,k){if(u=(255&k)/255,u===1)return k;const y=k>>24&255,b=k>>16&255,D=k>>8&255,B=E>>24&255,O=E>>16&255,T=E>>8&255;return c=B+Math.round((y-B)*u),f=O+Math.round((b-O)*u),S=T+Math.round((D-T)*u),_.toRgba(c,f,S)},o.ensureContrastRatio=function(E,k,y){const b=v.relativeLuminance(E>>8),D=v.relativeLuminance(k>>8);if(s(b,D)<y){if(D<b){const T=p(E,k,y),H=s(b,v.relativeLuminance(T>>8));if(H<y){const j=w(E,k,y);return H>s(b,v.relativeLuminance(j>>8))?T:j}return T}const B=w(E,k,y),O=s(b,v.relativeLuminance(B>>8));if(O<y){const T=p(E,k,y);return O>s(b,v.relativeLuminance(T>>8))?B:T}return B}},o.reduceLuminance=p,o.increaseLuminance=w,o.toChannels=function(E){return[E>>24&255,E>>16&255,E>>8&255,255&E]}}(h||(n.rgba=h={})),n.toPaddedHex=a,n.contrastRatio=s},8969:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.CoreTerminal=void 0;const f=c(844),S=c(2585),u=c(4348),_=c(7866),g=c(744),C=c(7302),v=c(6975),h=c(8460),a=c(1753),s=c(1480),o=c(7994),p=c(9282),w=c(5435),E=c(5981),k=c(2660);let y=!1;class b extends f.Disposable{get onScroll(){return this._onScrollApi||(this._onScrollApi=this.register(new h.EventEmitter),this._onScroll.event(B=>{var O;(O=this._onScrollApi)==null||O.fire(B.position)})),this._onScrollApi.event}get cols(){return this._bufferService.cols}get rows(){return this._bufferService.rows}get buffers(){return this._bufferService.buffers}get options(){return this.optionsService.options}set options(B){for(const O in B)this.optionsService.options[O]=B[O]}constructor(B){super(),this._windowsWrappingHeuristics=this.register(new f.MutableDisposable),this._onBinary=this.register(new h.EventEmitter),this.onBinary=this._onBinary.event,this._onData=this.register(new h.EventEmitter),this.onData=this._onData.event,this._onLineFeed=this.register(new h.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onResize=this.register(new h.EventEmitter),this.onResize=this._onResize.event,this._onWriteParsed=this.register(new h.EventEmitter),this.onWriteParsed=this._onWriteParsed.event,this._onScroll=this.register(new h.EventEmitter),this._instantiationService=new u.InstantiationService,this.optionsService=this.register(new C.OptionsService(B)),this._instantiationService.setService(S.IOptionsService,this.optionsService),this._bufferService=this.register(this._instantiationService.createInstance(g.BufferService)),this._instantiationService.setService(S.IBufferService,this._bufferService),this._logService=this.register(this._instantiationService.createInstance(_.LogService)),this._instantiationService.setService(S.ILogService,this._logService),this.coreService=this.register(this._instantiationService.createInstance(v.CoreService)),this._instantiationService.setService(S.ICoreService,this.coreService),this.coreMouseService=this.register(this._instantiationService.createInstance(a.CoreMouseService)),this._instantiationService.setService(S.ICoreMouseService,this.coreMouseService),this.unicodeService=this.register(this._instantiationService.createInstance(s.UnicodeService)),this._instantiationService.setService(S.IUnicodeService,this.unicodeService),this._charsetService=this._instantiationService.createInstance(o.CharsetService),this._instantiationService.setService(S.ICharsetService,this._charsetService),this._oscLinkService=this._instantiationService.createInstance(k.OscLinkService),this._instantiationService.setService(S.IOscLinkService,this._oscLinkService),this._inputHandler=this.register(new w.InputHandler(this._bufferService,this._charsetService,this.coreService,this._logService,this.optionsService,this._oscLinkService,this.coreMouseService,this.unicodeService)),this.register((0,h.forwardEvent)(this._inputHandler.onLineFeed,this._onLineFeed)),this.register(this._inputHandler),this.register((0,h.forwardEvent)(this._bufferService.onResize,this._onResize)),this.register((0,h.forwardEvent)(this.coreService.onData,this._onData)),this.register((0,h.forwardEvent)(this.coreService.onBinary,this._onBinary)),this.register(this.coreService.onRequestScrollToBottom(()=>this.scrollToBottom())),this.register(this.coreService.onUserInput(()=>this._writeBuffer.handleUserInput())),this.register(this.optionsService.onMultipleOptionChange(["windowsMode","windowsPty"],()=>this._handleWindowsPtyOptionChange())),this.register(this._bufferService.onScroll(O=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this.register(this._inputHandler.onScroll(O=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this._writeBuffer=this.register(new E.WriteBuffer((O,T)=>this._inputHandler.parse(O,T))),this.register((0,h.forwardEvent)(this._writeBuffer.onWriteParsed,this._onWriteParsed))}write(B,O){this._writeBuffer.write(B,O)}writeSync(B,O){this._logService.logLevel<=S.LogLevelEnum.WARN&&!y&&(this._logService.warn("writeSync is unreliable and will be removed soon."),y=!0),this._writeBuffer.writeSync(B,O)}input(B,O=!0){this.coreService.triggerDataEvent(B,O)}resize(B,O){isNaN(B)||isNaN(O)||(B=Math.max(B,g.MINIMUM_COLS),O=Math.max(O,g.MINIMUM_ROWS),this._bufferService.resize(B,O))}scroll(B,O=!1){this._bufferService.scroll(B,O)}scrollLines(B,O,T){this._bufferService.scrollLines(B,O,T)}scrollPages(B){this.scrollLines(B*(this.rows-1))}scrollToTop(){this.scrollLines(-this._bufferService.buffer.ydisp)}scrollToBottom(){this.scrollLines(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp)}scrollToLine(B){const O=B-this._bufferService.buffer.ydisp;O!==0&&this.scrollLines(O)}registerEscHandler(B,O){return this._inputHandler.registerEscHandler(B,O)}registerDcsHandler(B,O){return this._inputHandler.registerDcsHandler(B,O)}registerCsiHandler(B,O){return this._inputHandler.registerCsiHandler(B,O)}registerOscHandler(B,O){return this._inputHandler.registerOscHandler(B,O)}_setup(){this._handleWindowsPtyOptionChange()}reset(){this._inputHandler.reset(),this._bufferService.reset(),this._charsetService.reset(),this.coreService.reset(),this.coreMouseService.reset()}_handleWindowsPtyOptionChange(){let B=!1;const O=this.optionsService.rawOptions.windowsPty;O&&O.buildNumber!==void 0&&O.buildNumber!==void 0?B=O.backend==="conpty"&&O.buildNumber<21376:this.optionsService.rawOptions.windowsMode&&(B=!0),B?this._enableWindowsWrappingHeuristics():this._windowsWrappingHeuristics.clear()}_enableWindowsWrappingHeuristics(){if(!this._windowsWrappingHeuristics.value){const B=[];B.push(this.onLineFeed(p.updateWindowsModeWrappedState.bind(null,this._bufferService))),B.push(this.registerCsiHandler({final:"H"},()=>((0,p.updateWindowsModeWrappedState)(this._bufferService),!1))),this._windowsWrappingHeuristics.value=(0,f.toDisposable)(()=>{for(const O of B)O.dispose()})}}}n.CoreTerminal=b},8460:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.runAndSubscribe=n.forwardEvent=n.EventEmitter=void 0,n.EventEmitter=class{constructor(){this._listeners=[],this._disposed=!1}get event(){return this._event||(this._event=c=>(this._listeners.push(c),{dispose:()=>{if(!this._disposed){for(let f=0;f<this._listeners.length;f++)if(this._listeners[f]===c)return void this._listeners.splice(f,1)}}})),this._event}fire(c,f){const S=[];for(let u=0;u<this._listeners.length;u++)S.push(this._listeners[u]);for(let u=0;u<S.length;u++)S[u].call(void 0,c,f)}dispose(){this.clearListeners(),this._disposed=!0}clearListeners(){this._listeners&&(this._listeners.length=0)}},n.forwardEvent=function(c,f){return c(S=>f.fire(S))},n.runAndSubscribe=function(c,f){return f(void 0),c(S=>f(S))}},5435:function(m,n,c){var f=this&&this.__decorate||function(N,x,R,A){var P,z=arguments.length,V=z<3?x:A===null?A=Object.getOwnPropertyDescriptor(x,R):A;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")V=Reflect.decorate(N,x,R,A);else for(var Y=N.length-1;Y>=0;Y--)(P=N[Y])&&(V=(z<3?P(V):z>3?P(x,R,V):P(x,R))||V);return z>3&&V&&Object.defineProperty(x,R,V),V},S=this&&this.__param||function(N,x){return function(R,A){x(R,A,N)}};Object.defineProperty(n,"__esModule",{value:!0}),n.InputHandler=n.WindowsOptionsReportType=void 0;const u=c(2584),_=c(7116),g=c(2015),C=c(844),v=c(482),h=c(8437),a=c(8460),s=c(643),o=c(511),p=c(3734),w=c(2585),E=c(1480),k=c(6242),y=c(6351),b=c(5941),D={"(":0,")":1,"*":2,"+":3,"-":1,".":2},B=131072;function O(N,x){if(N>24)return x.setWinLines||!1;switch(N){case 1:return!!x.restoreWin;case 2:return!!x.minimizeWin;case 3:return!!x.setWinPosition;case 4:return!!x.setWinSizePixels;case 5:return!!x.raiseWin;case 6:return!!x.lowerWin;case 7:return!!x.refreshWin;case 8:return!!x.setWinSizeChars;case 9:return!!x.maximizeWin;case 10:return!!x.fullscreenWin;case 11:return!!x.getWinState;case 13:return!!x.getWinPosition;case 14:return!!x.getWinSizePixels;case 15:return!!x.getScreenSizePixels;case 16:return!!x.getCellSizePixels;case 18:return!!x.getWinSizeChars;case 19:return!!x.getScreenSizeChars;case 20:return!!x.getIconTitle;case 21:return!!x.getWinTitle;case 22:return!!x.pushTitle;case 23:return!!x.popTitle;case 24:return!!x.setWinLines}return!1}var T;(function(N){N[N.GET_WIN_SIZE_PIXELS=0]="GET_WIN_SIZE_PIXELS",N[N.GET_CELL_SIZE_PIXELS=1]="GET_CELL_SIZE_PIXELS"})(T||(n.WindowsOptionsReportType=T={}));let H=0;class j extends C.Disposable{getAttrData(){return this._curAttrData}constructor(x,R,A,P,z,V,Y,Q,M=new g.EscapeSequenceParser){super(),this._bufferService=x,this._charsetService=R,this._coreService=A,this._logService=P,this._optionsService=z,this._oscLinkService=V,this._coreMouseService=Y,this._unicodeService=Q,this._parser=M,this._parseBuffer=new Uint32Array(4096),this._stringDecoder=new v.StringToUtf32,this._utf8Decoder=new v.Utf8ToUtf32,this._workCell=new o.CellData,this._windowTitle="",this._iconName="",this._windowTitleStack=[],this._iconNameStack=[],this._curAttrData=h.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=h.DEFAULT_ATTR_DATA.clone(),this._onRequestBell=this.register(new a.EventEmitter),this.onRequestBell=this._onRequestBell.event,this._onRequestRefreshRows=this.register(new a.EventEmitter),this.onRequestRefreshRows=this._onRequestRefreshRows.event,this._onRequestReset=this.register(new a.EventEmitter),this.onRequestReset=this._onRequestReset.event,this._onRequestSendFocus=this.register(new a.EventEmitter),this.onRequestSendFocus=this._onRequestSendFocus.event,this._onRequestSyncScrollBar=this.register(new a.EventEmitter),this.onRequestSyncScrollBar=this._onRequestSyncScrollBar.event,this._onRequestWindowsOptionsReport=this.register(new a.EventEmitter),this.onRequestWindowsOptionsReport=this._onRequestWindowsOptionsReport.event,this._onA11yChar=this.register(new a.EventEmitter),this.onA11yChar=this._onA11yChar.event,this._onA11yTab=this.register(new a.EventEmitter),this.onA11yTab=this._onA11yTab.event,this._onCursorMove=this.register(new a.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onLineFeed=this.register(new a.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onScroll=this.register(new a.EventEmitter),this.onScroll=this._onScroll.event,this._onTitleChange=this.register(new a.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onColor=this.register(new a.EventEmitter),this.onColor=this._onColor.event,this._parseStack={paused:!1,cursorStartX:0,cursorStartY:0,decodedLength:0,position:0},this._specialColors=[256,257,258],this.register(this._parser),this._dirtyRowTracker=new $(this._bufferService),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(L=>this._activeBuffer=L.activeBuffer)),this._parser.setCsiHandlerFallback((L,W)=>{this._logService.debug("Unknown CSI code: ",{identifier:this._parser.identToString(L),params:W.toArray()})}),this._parser.setEscHandlerFallback(L=>{this._logService.debug("Unknown ESC code: ",{identifier:this._parser.identToString(L)})}),this._parser.setExecuteHandlerFallback(L=>{this._logService.debug("Unknown EXECUTE code: ",{code:L})}),this._parser.setOscHandlerFallback((L,W,F)=>{this._logService.debug("Unknown OSC code: ",{identifier:L,action:W,data:F})}),this._parser.setDcsHandlerFallback((L,W,F)=>{W==="HOOK"&&(F=F.toArray()),this._logService.debug("Unknown DCS code: ",{identifier:this._parser.identToString(L),action:W,payload:F})}),this._parser.setPrintHandler((L,W,F)=>this.print(L,W,F)),this._parser.registerCsiHandler({final:"@"},L=>this.insertChars(L)),this._parser.registerCsiHandler({intermediates:" ",final:"@"},L=>this.scrollLeft(L)),this._parser.registerCsiHandler({final:"A"},L=>this.cursorUp(L)),this._parser.registerCsiHandler({intermediates:" ",final:"A"},L=>this.scrollRight(L)),this._parser.registerCsiHandler({final:"B"},L=>this.cursorDown(L)),this._parser.registerCsiHandler({final:"C"},L=>this.cursorForward(L)),this._parser.registerCsiHandler({final:"D"},L=>this.cursorBackward(L)),this._parser.registerCsiHandler({final:"E"},L=>this.cursorNextLine(L)),this._parser.registerCsiHandler({final:"F"},L=>this.cursorPrecedingLine(L)),this._parser.registerCsiHandler({final:"G"},L=>this.cursorCharAbsolute(L)),this._parser.registerCsiHandler({final:"H"},L=>this.cursorPosition(L)),this._parser.registerCsiHandler({final:"I"},L=>this.cursorForwardTab(L)),this._parser.registerCsiHandler({final:"J"},L=>this.eraseInDisplay(L,!1)),this._parser.registerCsiHandler({prefix:"?",final:"J"},L=>this.eraseInDisplay(L,!0)),this._parser.registerCsiHandler({final:"K"},L=>this.eraseInLine(L,!1)),this._parser.registerCsiHandler({prefix:"?",final:"K"},L=>this.eraseInLine(L,!0)),this._parser.registerCsiHandler({final:"L"},L=>this.insertLines(L)),this._parser.registerCsiHandler({final:"M"},L=>this.deleteLines(L)),this._parser.registerCsiHandler({final:"P"},L=>this.deleteChars(L)),this._parser.registerCsiHandler({final:"S"},L=>this.scrollUp(L)),this._parser.registerCsiHandler({final:"T"},L=>this.scrollDown(L)),this._parser.registerCsiHandler({final:"X"},L=>this.eraseChars(L)),this._parser.registerCsiHandler({final:"Z"},L=>this.cursorBackwardTab(L)),this._parser.registerCsiHandler({final:"`"},L=>this.charPosAbsolute(L)),this._parser.registerCsiHandler({final:"a"},L=>this.hPositionRelative(L)),this._parser.registerCsiHandler({final:"b"},L=>this.repeatPrecedingCharacter(L)),this._parser.registerCsiHandler({final:"c"},L=>this.sendDeviceAttributesPrimary(L)),this._parser.registerCsiHandler({prefix:">",final:"c"},L=>this.sendDeviceAttributesSecondary(L)),this._parser.registerCsiHandler({final:"d"},L=>this.linePosAbsolute(L)),this._parser.registerCsiHandler({final:"e"},L=>this.vPositionRelative(L)),this._parser.registerCsiHandler({final:"f"},L=>this.hVPosition(L)),this._parser.registerCsiHandler({final:"g"},L=>this.tabClear(L)),this._parser.registerCsiHandler({final:"h"},L=>this.setMode(L)),this._parser.registerCsiHandler({prefix:"?",final:"h"},L=>this.setModePrivate(L)),this._parser.registerCsiHandler({final:"l"},L=>this.resetMode(L)),this._parser.registerCsiHandler({prefix:"?",final:"l"},L=>this.resetModePrivate(L)),this._parser.registerCsiHandler({final:"m"},L=>this.charAttributes(L)),this._parser.registerCsiHandler({final:"n"},L=>this.deviceStatus(L)),this._parser.registerCsiHandler({prefix:"?",final:"n"},L=>this.deviceStatusPrivate(L)),this._parser.registerCsiHandler({intermediates:"!",final:"p"},L=>this.softReset(L)),this._parser.registerCsiHandler({intermediates:" ",final:"q"},L=>this.setCursorStyle(L)),this._parser.registerCsiHandler({final:"r"},L=>this.setScrollRegion(L)),this._parser.registerCsiHandler({final:"s"},L=>this.saveCursor(L)),this._parser.registerCsiHandler({final:"t"},L=>this.windowOptions(L)),this._parser.registerCsiHandler({final:"u"},L=>this.restoreCursor(L)),this._parser.registerCsiHandler({intermediates:"'",final:"}"},L=>this.insertColumns(L)),this._parser.registerCsiHandler({intermediates:"'",final:"~"},L=>this.deleteColumns(L)),this._parser.registerCsiHandler({intermediates:'"',final:"q"},L=>this.selectProtected(L)),this._parser.registerCsiHandler({intermediates:"$",final:"p"},L=>this.requestMode(L,!0)),this._parser.registerCsiHandler({prefix:"?",intermediates:"$",final:"p"},L=>this.requestMode(L,!1)),this._parser.setExecuteHandler(u.C0.BEL,()=>this.bell()),this._parser.setExecuteHandler(u.C0.LF,()=>this.lineFeed()),this._parser.setExecuteHandler(u.C0.VT,()=>this.lineFeed()),this._parser.setExecuteHandler(u.C0.FF,()=>this.lineFeed()),this._parser.setExecuteHandler(u.C0.CR,()=>this.carriageReturn()),this._parser.setExecuteHandler(u.C0.BS,()=>this.backspace()),this._parser.setExecuteHandler(u.C0.HT,()=>this.tab()),this._parser.setExecuteHandler(u.C0.SO,()=>this.shiftOut()),this._parser.setExecuteHandler(u.C0.SI,()=>this.shiftIn()),this._parser.setExecuteHandler(u.C1.IND,()=>this.index()),this._parser.setExecuteHandler(u.C1.NEL,()=>this.nextLine()),this._parser.setExecuteHandler(u.C1.HTS,()=>this.tabSet()),this._parser.registerOscHandler(0,new k.OscHandler(L=>(this.setTitle(L),this.setIconName(L),!0))),this._parser.registerOscHandler(1,new k.OscHandler(L=>this.setIconName(L))),this._parser.registerOscHandler(2,new k.OscHandler(L=>this.setTitle(L))),this._parser.registerOscHandler(4,new k.OscHandler(L=>this.setOrReportIndexedColor(L))),this._parser.registerOscHandler(8,new k.OscHandler(L=>this.setHyperlink(L))),this._parser.registerOscHandler(10,new k.OscHandler(L=>this.setOrReportFgColor(L))),this._parser.registerOscHandler(11,new k.OscHandler(L=>this.setOrReportBgColor(L))),this._parser.registerOscHandler(12,new k.OscHandler(L=>this.setOrReportCursorColor(L))),this._parser.registerOscHandler(104,new k.OscHandler(L=>this.restoreIndexedColor(L))),this._parser.registerOscHandler(110,new k.OscHandler(L=>this.restoreFgColor(L))),this._parser.registerOscHandler(111,new k.OscHandler(L=>this.restoreBgColor(L))),this._parser.registerOscHandler(112,new k.OscHandler(L=>this.restoreCursorColor(L))),this._parser.registerEscHandler({final:"7"},()=>this.saveCursor()),this._parser.registerEscHandler({final:"8"},()=>this.restoreCursor()),this._parser.registerEscHandler({final:"D"},()=>this.index()),this._parser.registerEscHandler({final:"E"},()=>this.nextLine()),this._parser.registerEscHandler({final:"H"},()=>this.tabSet()),this._parser.registerEscHandler({final:"M"},()=>this.reverseIndex()),this._parser.registerEscHandler({final:"="},()=>this.keypadApplicationMode()),this._parser.registerEscHandler({final:">"},()=>this.keypadNumericMode()),this._parser.registerEscHandler({final:"c"},()=>this.fullReset()),this._parser.registerEscHandler({final:"n"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"o"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"|"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"}"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"~"},()=>this.setgLevel(1)),this._parser.registerEscHandler({intermediates:"%",final:"@"},()=>this.selectDefaultCharset()),this._parser.registerEscHandler({intermediates:"%",final:"G"},()=>this.selectDefaultCharset());for(const L in _.CHARSETS)this._parser.registerEscHandler({intermediates:"(",final:L},()=>this.selectCharset("("+L)),this._parser.registerEscHandler({intermediates:")",final:L},()=>this.selectCharset(")"+L)),this._parser.registerEscHandler({intermediates:"*",final:L},()=>this.selectCharset("*"+L)),this._parser.registerEscHandler({intermediates:"+",final:L},()=>this.selectCharset("+"+L)),this._parser.registerEscHandler({intermediates:"-",final:L},()=>this.selectCharset("-"+L)),this._parser.registerEscHandler({intermediates:".",final:L},()=>this.selectCharset("."+L)),this._parser.registerEscHandler({intermediates:"/",final:L},()=>this.selectCharset("/"+L));this._parser.registerEscHandler({intermediates:"#",final:"8"},()=>this.screenAlignmentPattern()),this._parser.setErrorHandler(L=>(this._logService.error("Parsing error: ",L),L)),this._parser.registerDcsHandler({intermediates:"$",final:"q"},new y.DcsHandler((L,W)=>this.requestStatusString(L,W)))}_preserveStack(x,R,A,P){this._parseStack.paused=!0,this._parseStack.cursorStartX=x,this._parseStack.cursorStartY=R,this._parseStack.decodedLength=A,this._parseStack.position=P}_logSlowResolvingAsync(x){this._logService.logLevel<=w.LogLevelEnum.WARN&&Promise.race([x,new Promise((R,A)=>setTimeout(()=>A("#SLOW_TIMEOUT"),5e3))]).catch(R=>{if(R!=="#SLOW_TIMEOUT")throw R;console.warn("async parser handler taking longer than 5000 ms")})}_getCurrentLinkId(){return this._curAttrData.extended.urlId}parse(x,R){let A,P=this._activeBuffer.x,z=this._activeBuffer.y,V=0;const Y=this._parseStack.paused;if(Y){if(A=this._parser.parse(this._parseBuffer,this._parseStack.decodedLength,R))return this._logSlowResolvingAsync(A),A;P=this._parseStack.cursorStartX,z=this._parseStack.cursorStartY,this._parseStack.paused=!1,x.length>B&&(V=this._parseStack.position+B)}if(this._logService.logLevel<=w.LogLevelEnum.DEBUG&&this._logService.debug("parsing data"+(typeof x=="string"?` "${x}"`:` "${Array.prototype.map.call(x,L=>String.fromCharCode(L)).join("")}"`),typeof x=="string"?x.split("").map(L=>L.charCodeAt(0)):x),this._parseBuffer.length<x.length&&this._parseBuffer.length<B&&(this._parseBuffer=new Uint32Array(Math.min(x.length,B))),Y||this._dirtyRowTracker.clearRange(),x.length>B)for(let L=V;L<x.length;L+=B){const W=L+B<x.length?L+B:x.length,F=typeof x=="string"?this._stringDecoder.decode(x.substring(L,W),this._parseBuffer):this._utf8Decoder.decode(x.subarray(L,W),this._parseBuffer);if(A=this._parser.parse(this._parseBuffer,F))return this._preserveStack(P,z,F,L),this._logSlowResolvingAsync(A),A}else if(!Y){const L=typeof x=="string"?this._stringDecoder.decode(x,this._parseBuffer):this._utf8Decoder.decode(x,this._parseBuffer);if(A=this._parser.parse(this._parseBuffer,L))return this._preserveStack(P,z,L,0),this._logSlowResolvingAsync(A),A}this._activeBuffer.x===P&&this._activeBuffer.y===z||this._onCursorMove.fire();const Q=this._dirtyRowTracker.end+(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp),M=this._dirtyRowTracker.start+(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp);M<this._bufferService.rows&&this._onRequestRefreshRows.fire(Math.min(M,this._bufferService.rows-1),Math.min(Q,this._bufferService.rows-1))}print(x,R,A){let P,z;const V=this._charsetService.charset,Y=this._optionsService.rawOptions.screenReaderMode,Q=this._bufferService.cols,M=this._coreService.decPrivateModes.wraparound,L=this._coreService.modes.insertMode,W=this._curAttrData;let F=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._activeBuffer.x&&A-R>0&&F.getWidth(this._activeBuffer.x-1)===2&&F.setCellFromCodepoint(this._activeBuffer.x-1,0,1,W);let X=this._parser.precedingJoinState;for(let K=R;K<A;++K){if(P=x[K],P<127&&V){const Me=V[String.fromCharCode(P)];Me&&(P=Me.charCodeAt(0))}const te=this._unicodeService.charProperties(P,X);z=E.UnicodeService.extractWidth(te);const de=E.UnicodeService.extractShouldJoin(te),fe=de?E.UnicodeService.extractWidth(X):0;if(X=te,Y&&this._onA11yChar.fire((0,v.stringFromCodePoint)(P)),this._getCurrentLinkId()&&this._oscLinkService.addLineToLink(this._getCurrentLinkId(),this._activeBuffer.ybase+this._activeBuffer.y),this._activeBuffer.x+z-fe>Q){if(M){const Me=F;let J=this._activeBuffer.x-fe;for(this._activeBuffer.x=fe,this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData(),!0)):(this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!0),F=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y),fe>0&&F instanceof h.BufferLine&&F.copyCellsFrom(Me,J,0,fe,!1);J<Q;)Me.setCellFromCodepoint(J++,0,1,W)}else if(this._activeBuffer.x=Q-1,z===2)continue}if(de&&this._activeBuffer.x){const Me=F.getWidth(this._activeBuffer.x-1)?1:2;F.addCodepointToCell(this._activeBuffer.x-Me,P,z);for(let J=z-fe;--J>=0;)F.setCellFromCodepoint(this._activeBuffer.x++,0,0,W)}else if(L&&(F.insertCells(this._activeBuffer.x,z-fe,this._activeBuffer.getNullCell(W)),F.getWidth(Q-1)===2&&F.setCellFromCodepoint(Q-1,s.NULL_CELL_CODE,s.NULL_CELL_WIDTH,W)),F.setCellFromCodepoint(this._activeBuffer.x++,P,z,W),z>0)for(;--z;)F.setCellFromCodepoint(this._activeBuffer.x++,0,0,W)}this._parser.precedingJoinState=X,this._activeBuffer.x<Q&&A-R>0&&F.getWidth(this._activeBuffer.x)===0&&!F.hasContent(this._activeBuffer.x)&&F.setCellFromCodepoint(this._activeBuffer.x,0,1,W),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}registerCsiHandler(x,R){return x.final!=="t"||x.prefix||x.intermediates?this._parser.registerCsiHandler(x,R):this._parser.registerCsiHandler(x,A=>!O(A.params[0],this._optionsService.rawOptions.windowOptions)||R(A))}registerDcsHandler(x,R){return this._parser.registerDcsHandler(x,new y.DcsHandler(R))}registerEscHandler(x,R){return this._parser.registerEscHandler(x,R)}registerOscHandler(x,R){return this._parser.registerOscHandler(x,new k.OscHandler(R))}bell(){return this._onRequestBell.fire(),!0}lineFeed(){return this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._optionsService.rawOptions.convertEol&&(this._activeBuffer.x=0),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows?this._activeBuffer.y=this._bufferService.rows-1:this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.x>=this._bufferService.cols&&this._activeBuffer.x--,this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._onLineFeed.fire(),!0}carriageReturn(){return this._activeBuffer.x=0,!0}backspace(){var x;if(!this._coreService.decPrivateModes.reverseWraparound)return this._restrictCursor(),this._activeBuffer.x>0&&this._activeBuffer.x--,!0;if(this._restrictCursor(this._bufferService.cols),this._activeBuffer.x>0)this._activeBuffer.x--;else if(this._activeBuffer.x===0&&this._activeBuffer.y>this._activeBuffer.scrollTop&&this._activeBuffer.y<=this._activeBuffer.scrollBottom&&((x=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y))!=null&&x.isWrapped)){this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.y--,this._activeBuffer.x=this._bufferService.cols-1;const R=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);R.hasWidth(this._activeBuffer.x)&&!R.hasContent(this._activeBuffer.x)&&this._activeBuffer.x--}return this._restrictCursor(),!0}tab(){if(this._activeBuffer.x>=this._bufferService.cols)return!0;const x=this._activeBuffer.x;return this._activeBuffer.x=this._activeBuffer.nextStop(),this._optionsService.rawOptions.screenReaderMode&&this._onA11yTab.fire(this._activeBuffer.x-x),!0}shiftOut(){return this._charsetService.setgLevel(1),!0}shiftIn(){return this._charsetService.setgLevel(0),!0}_restrictCursor(x=this._bufferService.cols-1){this._activeBuffer.x=Math.min(x,Math.max(0,this._activeBuffer.x)),this._activeBuffer.y=this._coreService.decPrivateModes.origin?Math.min(this._activeBuffer.scrollBottom,Math.max(this._activeBuffer.scrollTop,this._activeBuffer.y)):Math.min(this._bufferService.rows-1,Math.max(0,this._activeBuffer.y)),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_setCursor(x,R){this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._coreService.decPrivateModes.origin?(this._activeBuffer.x=x,this._activeBuffer.y=this._activeBuffer.scrollTop+R):(this._activeBuffer.x=x,this._activeBuffer.y=R),this._restrictCursor(),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_moveCursor(x,R){this._restrictCursor(),this._setCursor(this._activeBuffer.x+x,this._activeBuffer.y+R)}cursorUp(x){const R=this._activeBuffer.y-this._activeBuffer.scrollTop;return R>=0?this._moveCursor(0,-Math.min(R,x.params[0]||1)):this._moveCursor(0,-(x.params[0]||1)),!0}cursorDown(x){const R=this._activeBuffer.scrollBottom-this._activeBuffer.y;return R>=0?this._moveCursor(0,Math.min(R,x.params[0]||1)):this._moveCursor(0,x.params[0]||1),!0}cursorForward(x){return this._moveCursor(x.params[0]||1,0),!0}cursorBackward(x){return this._moveCursor(-(x.params[0]||1),0),!0}cursorNextLine(x){return this.cursorDown(x),this._activeBuffer.x=0,!0}cursorPrecedingLine(x){return this.cursorUp(x),this._activeBuffer.x=0,!0}cursorCharAbsolute(x){return this._setCursor((x.params[0]||1)-1,this._activeBuffer.y),!0}cursorPosition(x){return this._setCursor(x.length>=2?(x.params[1]||1)-1:0,(x.params[0]||1)-1),!0}charPosAbsolute(x){return this._setCursor((x.params[0]||1)-1,this._activeBuffer.y),!0}hPositionRelative(x){return this._moveCursor(x.params[0]||1,0),!0}linePosAbsolute(x){return this._setCursor(this._activeBuffer.x,(x.params[0]||1)-1),!0}vPositionRelative(x){return this._moveCursor(0,x.params[0]||1),!0}hVPosition(x){return this.cursorPosition(x),!0}tabClear(x){const R=x.params[0];return R===0?delete this._activeBuffer.tabs[this._activeBuffer.x]:R===3&&(this._activeBuffer.tabs={}),!0}cursorForwardTab(x){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let R=x.params[0]||1;for(;R--;)this._activeBuffer.x=this._activeBuffer.nextStop();return!0}cursorBackwardTab(x){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let R=x.params[0]||1;for(;R--;)this._activeBuffer.x=this._activeBuffer.prevStop();return!0}selectProtected(x){const R=x.params[0];return R===1&&(this._curAttrData.bg|=536870912),R!==2&&R!==0||(this._curAttrData.bg&=-536870913),!0}_eraseInBufferLine(x,R,A,P=!1,z=!1){const V=this._activeBuffer.lines.get(this._activeBuffer.ybase+x);V.replaceCells(R,A,this._activeBuffer.getNullCell(this._eraseAttrData()),z),P&&(V.isWrapped=!1)}_resetBufferLine(x,R=!1){const A=this._activeBuffer.lines.get(this._activeBuffer.ybase+x);A&&(A.fill(this._activeBuffer.getNullCell(this._eraseAttrData()),R),this._bufferService.buffer.clearMarkers(this._activeBuffer.ybase+x),A.isWrapped=!1)}eraseInDisplay(x,R=!1){let A;switch(this._restrictCursor(this._bufferService.cols),x.params[0]){case 0:for(A=this._activeBuffer.y,this._dirtyRowTracker.markDirty(A),this._eraseInBufferLine(A++,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,R);A<this._bufferService.rows;A++)this._resetBufferLine(A,R);this._dirtyRowTracker.markDirty(A);break;case 1:for(A=this._activeBuffer.y,this._dirtyRowTracker.markDirty(A),this._eraseInBufferLine(A,0,this._activeBuffer.x+1,!0,R),this._activeBuffer.x+1>=this._bufferService.cols&&(this._activeBuffer.lines.get(A+1).isWrapped=!1);A--;)this._resetBufferLine(A,R);this._dirtyRowTracker.markDirty(0);break;case 2:for(A=this._bufferService.rows,this._dirtyRowTracker.markDirty(A-1);A--;)this._resetBufferLine(A,R);this._dirtyRowTracker.markDirty(0);break;case 3:const P=this._activeBuffer.lines.length-this._bufferService.rows;P>0&&(this._activeBuffer.lines.trimStart(P),this._activeBuffer.ybase=Math.max(this._activeBuffer.ybase-P,0),this._activeBuffer.ydisp=Math.max(this._activeBuffer.ydisp-P,0),this._onScroll.fire(0))}return!0}eraseInLine(x,R=!1){switch(this._restrictCursor(this._bufferService.cols),x.params[0]){case 0:this._eraseInBufferLine(this._activeBuffer.y,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,R);break;case 1:this._eraseInBufferLine(this._activeBuffer.y,0,this._activeBuffer.x+1,!1,R);break;case 2:this._eraseInBufferLine(this._activeBuffer.y,0,this._bufferService.cols,!0,R)}return this._dirtyRowTracker.markDirty(this._activeBuffer.y),!0}insertLines(x){this._restrictCursor();let R=x.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const A=this._activeBuffer.ybase+this._activeBuffer.y,P=this._bufferService.rows-1-this._activeBuffer.scrollBottom,z=this._bufferService.rows-1+this._activeBuffer.ybase-P+1;for(;R--;)this._activeBuffer.lines.splice(z-1,1),this._activeBuffer.lines.splice(A,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}deleteLines(x){this._restrictCursor();let R=x.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const A=this._activeBuffer.ybase+this._activeBuffer.y;let P;for(P=this._bufferService.rows-1-this._activeBuffer.scrollBottom,P=this._bufferService.rows-1+this._activeBuffer.ybase-P;R--;)this._activeBuffer.lines.splice(A,1),this._activeBuffer.lines.splice(P,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}insertChars(x){this._restrictCursor();const R=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return R&&(R.insertCells(this._activeBuffer.x,x.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData())),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}deleteChars(x){this._restrictCursor();const R=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return R&&(R.deleteCells(this._activeBuffer.x,x.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData())),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}scrollUp(x){let R=x.params[0]||1;for(;R--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollDown(x){let R=x.params[0]||1;for(;R--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,0,this._activeBuffer.getBlankLine(h.DEFAULT_ATTR_DATA));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollLeft(x){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const R=x.params[0]||1;for(let A=this._activeBuffer.scrollTop;A<=this._activeBuffer.scrollBottom;++A){const P=this._activeBuffer.lines.get(this._activeBuffer.ybase+A);P.deleteCells(0,R,this._activeBuffer.getNullCell(this._eraseAttrData())),P.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollRight(x){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const R=x.params[0]||1;for(let A=this._activeBuffer.scrollTop;A<=this._activeBuffer.scrollBottom;++A){const P=this._activeBuffer.lines.get(this._activeBuffer.ybase+A);P.insertCells(0,R,this._activeBuffer.getNullCell(this._eraseAttrData())),P.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}insertColumns(x){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const R=x.params[0]||1;for(let A=this._activeBuffer.scrollTop;A<=this._activeBuffer.scrollBottom;++A){const P=this._activeBuffer.lines.get(this._activeBuffer.ybase+A);P.insertCells(this._activeBuffer.x,R,this._activeBuffer.getNullCell(this._eraseAttrData())),P.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}deleteColumns(x){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const R=x.params[0]||1;for(let A=this._activeBuffer.scrollTop;A<=this._activeBuffer.scrollBottom;++A){const P=this._activeBuffer.lines.get(this._activeBuffer.ybase+A);P.deleteCells(this._activeBuffer.x,R,this._activeBuffer.getNullCell(this._eraseAttrData())),P.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}eraseChars(x){this._restrictCursor();const R=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return R&&(R.replaceCells(this._activeBuffer.x,this._activeBuffer.x+(x.params[0]||1),this._activeBuffer.getNullCell(this._eraseAttrData())),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}repeatPrecedingCharacter(x){const R=this._parser.precedingJoinState;if(!R)return!0;const A=x.params[0]||1,P=E.UnicodeService.extractWidth(R),z=this._activeBuffer.x-P,V=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).getString(z),Y=new Uint32Array(V.length*A);let Q=0;for(let L=0;L<V.length;){const W=V.codePointAt(L)||0;Y[Q++]=W,L+=W>65535?2:1}let M=Q;for(let L=1;L<A;++L)Y.copyWithin(M,0,Q),M+=Q;return this.print(Y,0,M),!0}sendDeviceAttributesPrimary(x){return x.params[0]>0||(this._is("xterm")||this._is("rxvt-unicode")||this._is("screen")?this._coreService.triggerDataEvent(u.C0.ESC+"[?1;2c"):this._is("linux")&&this._coreService.triggerDataEvent(u.C0.ESC+"[?6c")),!0}sendDeviceAttributesSecondary(x){return x.params[0]>0||(this._is("xterm")?this._coreService.triggerDataEvent(u.C0.ESC+"[>0;276;0c"):this._is("rxvt-unicode")?this._coreService.triggerDataEvent(u.C0.ESC+"[>85;95;0c"):this._is("linux")?this._coreService.triggerDataEvent(x.params[0]+"c"):this._is("screen")&&this._coreService.triggerDataEvent(u.C0.ESC+"[>83;40003;0c")),!0}_is(x){return(this._optionsService.rawOptions.termName+"").indexOf(x)===0}setMode(x){for(let R=0;R<x.length;R++)switch(x.params[R]){case 4:this._coreService.modes.insertMode=!0;break;case 20:this._optionsService.options.convertEol=!0}return!0}setModePrivate(x){for(let R=0;R<x.length;R++)switch(x.params[R]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!0;break;case 2:this._charsetService.setgCharset(0,_.DEFAULT_CHARSET),this._charsetService.setgCharset(1,_.DEFAULT_CHARSET),this._charsetService.setgCharset(2,_.DEFAULT_CHARSET),this._charsetService.setgCharset(3,_.DEFAULT_CHARSET);break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(132,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!0,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!0;break;case 12:this._optionsService.options.cursorBlink=!0;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!0;break;case 66:this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire();break;case 9:this._coreMouseService.activeProtocol="X10";break;case 1e3:this._coreMouseService.activeProtocol="VT200";break;case 1002:this._coreMouseService.activeProtocol="DRAG";break;case 1003:this._coreMouseService.activeProtocol="ANY";break;case 1004:this._coreService.decPrivateModes.sendFocus=!0,this._onRequestSendFocus.fire();break;case 1005:this._logService.debug("DECSET 1005 not supported (see #2507)");break;case 1006:this._coreMouseService.activeEncoding="SGR";break;case 1015:this._logService.debug("DECSET 1015 not supported (see #2507)");break;case 1016:this._coreMouseService.activeEncoding="SGR_PIXELS";break;case 25:this._coreService.isCursorHidden=!1;break;case 1048:this.saveCursor();break;case 1049:this.saveCursor();case 47:case 1047:this._bufferService.buffers.activateAltBuffer(this._eraseAttrData()),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!0}return!0}resetMode(x){for(let R=0;R<x.length;R++)switch(x.params[R]){case 4:this._coreService.modes.insertMode=!1;break;case 20:this._optionsService.options.convertEol=!1}return!0}resetModePrivate(x){for(let R=0;R<x.length;R++)switch(x.params[R]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!1;break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(80,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!1,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!1;break;case 12:this._optionsService.options.cursorBlink=!1;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!1;break;case 66:this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire();break;case 9:case 1e3:case 1002:case 1003:this._coreMouseService.activeProtocol="NONE";break;case 1004:this._coreService.decPrivateModes.sendFocus=!1;break;case 1005:this._logService.debug("DECRST 1005 not supported (see #2507)");break;case 1006:case 1016:this._coreMouseService.activeEncoding="DEFAULT";break;case 1015:this._logService.debug("DECRST 1015 not supported (see #2507)");break;case 25:this._coreService.isCursorHidden=!0;break;case 1048:this.restoreCursor();break;case 1049:case 47:case 1047:this._bufferService.buffers.activateNormalBuffer(),x.params[R]===1049&&this.restoreCursor(),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!1}return!0}requestMode(x,R){const A=this._coreService.decPrivateModes,{activeProtocol:P,activeEncoding:z}=this._coreMouseService,V=this._coreService,{buffers:Y,cols:Q}=this._bufferService,{active:M,alt:L}=Y,W=this._optionsService.rawOptions,F=de=>de?1:2,X=x.params[0];return K=X,te=R?X===2?4:X===4?F(V.modes.insertMode):X===12?3:X===20?F(W.convertEol):0:X===1?F(A.applicationCursorKeys):X===3?W.windowOptions.setWinLines?Q===80?2:Q===132?1:0:0:X===6?F(A.origin):X===7?F(A.wraparound):X===8?3:X===9?F(P==="X10"):X===12?F(W.cursorBlink):X===25?F(!V.isCursorHidden):X===45?F(A.reverseWraparound):X===66?F(A.applicationKeypad):X===67?4:X===1e3?F(P==="VT200"):X===1002?F(P==="DRAG"):X===1003?F(P==="ANY"):X===1004?F(A.sendFocus):X===1005?4:X===1006?F(z==="SGR"):X===1015?4:X===1016?F(z==="SGR_PIXELS"):X===1048?1:X===47||X===1047||X===1049?F(M===L):X===2004?F(A.bracketedPasteMode):0,V.triggerDataEvent(`${u.C0.ESC}[${R?"":"?"}${K};${te}$y`),!0;var K,te}_updateAttrColor(x,R,A,P,z){return R===2?(x|=50331648,x&=-16777216,x|=p.AttributeData.fromColorRGB([A,P,z])):R===5&&(x&=-50331904,x|=33554432|255&A),x}_extractColor(x,R,A){const P=[0,0,-1,0,0,0];let z=0,V=0;do{if(P[V+z]=x.params[R+V],x.hasSubParams(R+V)){const Y=x.getSubParams(R+V);let Q=0;do P[1]===5&&(z=1),P[V+Q+1+z]=Y[Q];while(++Q<Y.length&&Q+V+1+z<P.length);break}if(P[1]===5&&V+z>=2||P[1]===2&&V+z>=5)break;P[1]&&(z=1)}while(++V+R<x.length&&V+z<P.length);for(let Y=2;Y<P.length;++Y)P[Y]===-1&&(P[Y]=0);switch(P[0]){case 38:A.fg=this._updateAttrColor(A.fg,P[1],P[3],P[4],P[5]);break;case 48:A.bg=this._updateAttrColor(A.bg,P[1],P[3],P[4],P[5]);break;case 58:A.extended=A.extended.clone(),A.extended.underlineColor=this._updateAttrColor(A.extended.underlineColor,P[1],P[3],P[4],P[5])}return V}_processUnderline(x,R){R.extended=R.extended.clone(),(!~x||x>5)&&(x=1),R.extended.underlineStyle=x,R.fg|=268435456,x===0&&(R.fg&=-268435457),R.updateExtended()}_processSGR0(x){x.fg=h.DEFAULT_ATTR_DATA.fg,x.bg=h.DEFAULT_ATTR_DATA.bg,x.extended=x.extended.clone(),x.extended.underlineStyle=0,x.extended.underlineColor&=-67108864,x.updateExtended()}charAttributes(x){if(x.length===1&&x.params[0]===0)return this._processSGR0(this._curAttrData),!0;const R=x.length;let A;const P=this._curAttrData;for(let z=0;z<R;z++)A=x.params[z],A>=30&&A<=37?(P.fg&=-50331904,P.fg|=16777216|A-30):A>=40&&A<=47?(P.bg&=-50331904,P.bg|=16777216|A-40):A>=90&&A<=97?(P.fg&=-50331904,P.fg|=16777224|A-90):A>=100&&A<=107?(P.bg&=-50331904,P.bg|=16777224|A-100):A===0?this._processSGR0(P):A===1?P.fg|=134217728:A===3?P.bg|=67108864:A===4?(P.fg|=268435456,this._processUnderline(x.hasSubParams(z)?x.getSubParams(z)[0]:1,P)):A===5?P.fg|=536870912:A===7?P.fg|=67108864:A===8?P.fg|=1073741824:A===9?P.fg|=2147483648:A===2?P.bg|=134217728:A===21?this._processUnderline(2,P):A===22?(P.fg&=-134217729,P.bg&=-134217729):A===23?P.bg&=-67108865:A===24?(P.fg&=-268435457,this._processUnderline(0,P)):A===25?P.fg&=-536870913:A===27?P.fg&=-67108865:A===28?P.fg&=-1073741825:A===29?P.fg&=2147483647:A===39?(P.fg&=-67108864,P.fg|=16777215&h.DEFAULT_ATTR_DATA.fg):A===49?(P.bg&=-67108864,P.bg|=16777215&h.DEFAULT_ATTR_DATA.bg):A===38||A===48||A===58?z+=this._extractColor(x,z,P):A===53?P.bg|=1073741824:A===55?P.bg&=-1073741825:A===59?(P.extended=P.extended.clone(),P.extended.underlineColor=-1,P.updateExtended()):A===100?(P.fg&=-67108864,P.fg|=16777215&h.DEFAULT_ATTR_DATA.fg,P.bg&=-67108864,P.bg|=16777215&h.DEFAULT_ATTR_DATA.bg):this._logService.debug("Unknown SGR attribute: %d.",A);return!0}deviceStatus(x){switch(x.params[0]){case 5:this._coreService.triggerDataEvent(`${u.C0.ESC}[0n`);break;case 6:const R=this._activeBuffer.y+1,A=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${u.C0.ESC}[${R};${A}R`)}return!0}deviceStatusPrivate(x){if(x.params[0]===6){const R=this._activeBuffer.y+1,A=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${u.C0.ESC}[?${R};${A}R`)}return!0}softReset(x){return this._coreService.isCursorHidden=!1,this._onRequestSyncScrollBar.fire(),this._activeBuffer.scrollTop=0,this._activeBuffer.scrollBottom=this._bufferService.rows-1,this._curAttrData=h.DEFAULT_ATTR_DATA.clone(),this._coreService.reset(),this._charsetService.reset(),this._activeBuffer.savedX=0,this._activeBuffer.savedY=this._activeBuffer.ybase,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,this._coreService.decPrivateModes.origin=!1,!0}setCursorStyle(x){const R=x.params[0]||1;switch(R){case 1:case 2:this._optionsService.options.cursorStyle="block";break;case 3:case 4:this._optionsService.options.cursorStyle="underline";break;case 5:case 6:this._optionsService.options.cursorStyle="bar"}const A=R%2==1;return this._optionsService.options.cursorBlink=A,!0}setScrollRegion(x){const R=x.params[0]||1;let A;return(x.length<2||(A=x.params[1])>this._bufferService.rows||A===0)&&(A=this._bufferService.rows),A>R&&(this._activeBuffer.scrollTop=R-1,this._activeBuffer.scrollBottom=A-1,this._setCursor(0,0)),!0}windowOptions(x){if(!O(x.params[0],this._optionsService.rawOptions.windowOptions))return!0;const R=x.length>1?x.params[1]:0;switch(x.params[0]){case 14:R!==2&&this._onRequestWindowsOptionsReport.fire(T.GET_WIN_SIZE_PIXELS);break;case 16:this._onRequestWindowsOptionsReport.fire(T.GET_CELL_SIZE_PIXELS);break;case 18:this._bufferService&&this._coreService.triggerDataEvent(`${u.C0.ESC}[8;${this._bufferService.rows};${this._bufferService.cols}t`);break;case 22:R!==0&&R!==2||(this._windowTitleStack.push(this._windowTitle),this._windowTitleStack.length>10&&this._windowTitleStack.shift()),R!==0&&R!==1||(this._iconNameStack.push(this._iconName),this._iconNameStack.length>10&&this._iconNameStack.shift());break;case 23:R!==0&&R!==2||this._windowTitleStack.length&&this.setTitle(this._windowTitleStack.pop()),R!==0&&R!==1||this._iconNameStack.length&&this.setIconName(this._iconNameStack.pop())}return!0}saveCursor(x){return this._activeBuffer.savedX=this._activeBuffer.x,this._activeBuffer.savedY=this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,!0}restoreCursor(x){return this._activeBuffer.x=this._activeBuffer.savedX||0,this._activeBuffer.y=Math.max(this._activeBuffer.savedY-this._activeBuffer.ybase,0),this._curAttrData.fg=this._activeBuffer.savedCurAttrData.fg,this._curAttrData.bg=this._activeBuffer.savedCurAttrData.bg,this._charsetService.charset=this._savedCharset,this._activeBuffer.savedCharset&&(this._charsetService.charset=this._activeBuffer.savedCharset),this._restrictCursor(),!0}setTitle(x){return this._windowTitle=x,this._onTitleChange.fire(x),!0}setIconName(x){return this._iconName=x,!0}setOrReportIndexedColor(x){const R=[],A=x.split(";");for(;A.length>1;){const P=A.shift(),z=A.shift();if(/^\d+$/.exec(P)){const V=parseInt(P);if(q(V))if(z==="?")R.push({type:0,index:V});else{const Y=(0,b.parseColor)(z);Y&&R.push({type:1,index:V,color:Y})}}}return R.length&&this._onColor.fire(R),!0}setHyperlink(x){const R=x.split(";");return!(R.length<2)&&(R[1]?this._createHyperlink(R[0],R[1]):!R[0]&&this._finishHyperlink())}_createHyperlink(x,R){this._getCurrentLinkId()&&this._finishHyperlink();const A=x.split(":");let P;const z=A.findIndex(V=>V.startsWith("id="));return z!==-1&&(P=A[z].slice(3)||void 0),this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=this._oscLinkService.registerLink({id:P,uri:R}),this._curAttrData.updateExtended(),!0}_finishHyperlink(){return this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=0,this._curAttrData.updateExtended(),!0}_setOrReportSpecialColor(x,R){const A=x.split(";");for(let P=0;P<A.length&&!(R>=this._specialColors.length);++P,++R)if(A[P]==="?")this._onColor.fire([{type:0,index:this._specialColors[R]}]);else{const z=(0,b.parseColor)(A[P]);z&&this._onColor.fire([{type:1,index:this._specialColors[R],color:z}])}return!0}setOrReportFgColor(x){return this._setOrReportSpecialColor(x,0)}setOrReportBgColor(x){return this._setOrReportSpecialColor(x,1)}setOrReportCursorColor(x){return this._setOrReportSpecialColor(x,2)}restoreIndexedColor(x){if(!x)return this._onColor.fire([{type:2}]),!0;const R=[],A=x.split(";");for(let P=0;P<A.length;++P)if(/^\d+$/.exec(A[P])){const z=parseInt(A[P]);q(z)&&R.push({type:2,index:z})}return R.length&&this._onColor.fire(R),!0}restoreFgColor(x){return this._onColor.fire([{type:2,index:256}]),!0}restoreBgColor(x){return this._onColor.fire([{type:2,index:257}]),!0}restoreCursorColor(x){return this._onColor.fire([{type:2,index:258}]),!0}nextLine(){return this._activeBuffer.x=0,this.index(),!0}keypadApplicationMode(){return this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire(),!0}keypadNumericMode(){return this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire(),!0}selectDefaultCharset(){return this._charsetService.setgLevel(0),this._charsetService.setgCharset(0,_.DEFAULT_CHARSET),!0}selectCharset(x){return x.length!==2?(this.selectDefaultCharset(),!0):(x[0]==="/"||this._charsetService.setgCharset(D[x[0]],_.CHARSETS[x[1]]||_.DEFAULT_CHARSET),!0)}index(){return this._restrictCursor(),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._restrictCursor(),!0}tabSet(){return this._activeBuffer.tabs[this._activeBuffer.x]=!0,!0}reverseIndex(){if(this._restrictCursor(),this._activeBuffer.y===this._activeBuffer.scrollTop){const x=this._activeBuffer.scrollBottom-this._activeBuffer.scrollTop;this._activeBuffer.lines.shiftElements(this._activeBuffer.ybase+this._activeBuffer.y,x,1),this._activeBuffer.lines.set(this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.getBlankLine(this._eraseAttrData())),this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom)}else this._activeBuffer.y--,this._restrictCursor();return!0}fullReset(){return this._parser.reset(),this._onRequestReset.fire(),!0}reset(){this._curAttrData=h.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=h.DEFAULT_ATTR_DATA.clone()}_eraseAttrData(){return this._eraseAttrDataInternal.bg&=-67108864,this._eraseAttrDataInternal.bg|=67108863&this._curAttrData.bg,this._eraseAttrDataInternal}setgLevel(x){return this._charsetService.setgLevel(x),!0}screenAlignmentPattern(){const x=new o.CellData;x.content=4194373,x.fg=this._curAttrData.fg,x.bg=this._curAttrData.bg,this._setCursor(0,0);for(let R=0;R<this._bufferService.rows;++R){const A=this._activeBuffer.ybase+this._activeBuffer.y+R,P=this._activeBuffer.lines.get(A);P&&(P.fill(x),P.isWrapped=!1)}return this._dirtyRowTracker.markAllDirty(),this._setCursor(0,0),!0}requestStatusString(x,R){const A=this._bufferService.buffer,P=this._optionsService.rawOptions;return(z=>(this._coreService.triggerDataEvent(`${u.C0.ESC}${z}${u.C0.ESC}\\`),!0))(x==='"q'?`P1$r${this._curAttrData.isProtected()?1:0}"q`:x==='"p'?'P1$r61;1"p':x==="r"?`P1$r${A.scrollTop+1};${A.scrollBottom+1}r`:x==="m"?"P1$r0m":x===" q"?`P1$r${{block:2,underline:4,bar:6}[P.cursorStyle]-(P.cursorBlink?1:0)} q`:"P0$r")}markRangeDirty(x,R){this._dirtyRowTracker.markRangeDirty(x,R)}}n.InputHandler=j;let $=class{constructor(N){this._bufferService=N,this.clearRange()}clearRange(){this.start=this._bufferService.buffer.y,this.end=this._bufferService.buffer.y}markDirty(N){N<this.start?this.start=N:N>this.end&&(this.end=N)}markRangeDirty(N,x){N>x&&(H=N,N=x,x=H),N<this.start&&(this.start=N),x>this.end&&(this.end=x)}markAllDirty(){this.markRangeDirty(0,this._bufferService.rows-1)}};function q(N){return 0<=N&&N<256}$=f([S(0,w.IBufferService)],$)},844:(m,n)=>{function c(f){for(const S of f)S.dispose();f.length=0}Object.defineProperty(n,"__esModule",{value:!0}),n.getDisposeArrayDisposable=n.disposeArray=n.toDisposable=n.MutableDisposable=n.Disposable=void 0,n.Disposable=class{constructor(){this._disposables=[],this._isDisposed=!1}dispose(){this._isDisposed=!0;for(const f of this._disposables)f.dispose();this._disposables.length=0}register(f){return this._disposables.push(f),f}unregister(f){const S=this._disposables.indexOf(f);S!==-1&&this._disposables.splice(S,1)}},n.MutableDisposable=class{constructor(){this._isDisposed=!1}get value(){return this._isDisposed?void 0:this._value}set value(f){var S;this._isDisposed||f===this._value||((S=this._value)==null||S.dispose(),this._value=f)}clear(){this.value=void 0}dispose(){var f;this._isDisposed=!0,(f=this._value)==null||f.dispose(),this._value=void 0}},n.toDisposable=function(f){return{dispose:f}},n.disposeArray=c,n.getDisposeArrayDisposable=function(f){return{dispose:()=>c(f)}}},1505:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.FourKeyMap=n.TwoKeyMap=void 0;class c{constructor(){this._data={}}set(S,u,_){this._data[S]||(this._data[S]={}),this._data[S][u]=_}get(S,u){return this._data[S]?this._data[S][u]:void 0}clear(){this._data={}}}n.TwoKeyMap=c,n.FourKeyMap=class{constructor(){this._data=new c}set(f,S,u,_,g){this._data.get(f,S)||this._data.set(f,S,new c),this._data.get(f,S).set(u,_,g)}get(f,S,u,_){var g;return(g=this._data.get(f,S))==null?void 0:g.get(u,_)}clear(){this._data.clear()}}},6114:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.isChromeOS=n.isLinux=n.isWindows=n.isIphone=n.isIpad=n.isMac=n.getSafariVersion=n.isSafari=n.isLegacyEdge=n.isFirefox=n.isNode=void 0,n.isNode=typeof process<"u"&&"title"in process;const c=n.isNode?"node":navigator.userAgent,f=n.isNode?"node":navigator.platform;n.isFirefox=c.includes("Firefox"),n.isLegacyEdge=c.includes("Edge"),n.isSafari=/^((?!chrome|android).)*safari/i.test(c),n.getSafariVersion=function(){if(!n.isSafari)return 0;const S=c.match(/Version\/(\d+)/);return S===null||S.length<2?0:parseInt(S[1])},n.isMac=["Macintosh","MacIntel","MacPPC","Mac68K"].includes(f),n.isIpad=f==="iPad",n.isIphone=f==="iPhone",n.isWindows=["Windows","Win16","Win32","WinCE"].includes(f),n.isLinux=f.indexOf("Linux")>=0,n.isChromeOS=/\bCrOS\b/.test(c)},6106:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.SortedList=void 0;let c=0;n.SortedList=class{constructor(f){this._getKey=f,this._array=[]}clear(){this._array.length=0}insert(f){this._array.length!==0?(c=this._search(this._getKey(f)),this._array.splice(c,0,f)):this._array.push(f)}delete(f){if(this._array.length===0)return!1;const S=this._getKey(f);if(S===void 0||(c=this._search(S),c===-1)||this._getKey(this._array[c])!==S)return!1;do if(this._array[c]===f)return this._array.splice(c,1),!0;while(++c<this._array.length&&this._getKey(this._array[c])===S);return!1}*getKeyIterator(f){if(this._array.length!==0&&(c=this._search(f),!(c<0||c>=this._array.length)&&this._getKey(this._array[c])===f))do yield this._array[c];while(++c<this._array.length&&this._getKey(this._array[c])===f)}forEachByKey(f,S){if(this._array.length!==0&&(c=this._search(f),!(c<0||c>=this._array.length)&&this._getKey(this._array[c])===f))do S(this._array[c]);while(++c<this._array.length&&this._getKey(this._array[c])===f)}values(){return[...this._array].values()}_search(f){let S=0,u=this._array.length-1;for(;u>=S;){let _=S+u>>1;const g=this._getKey(this._array[_]);if(g>f)u=_-1;else{if(!(g<f)){for(;_>0&&this._getKey(this._array[_-1])===f;)_--;return _}S=_+1}}return S}}},7226:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.DebouncedIdleTask=n.IdleTaskQueue=n.PriorityTaskQueue=void 0;const f=c(6114);class S{constructor(){this._tasks=[],this._i=0}enqueue(g){this._tasks.push(g),this._start()}flush(){for(;this._i<this._tasks.length;)this._tasks[this._i]()||this._i++;this.clear()}clear(){this._idleCallback&&(this._cancelCallback(this._idleCallback),this._idleCallback=void 0),this._i=0,this._tasks.length=0}_start(){this._idleCallback||(this._idleCallback=this._requestCallback(this._process.bind(this)))}_process(g){this._idleCallback=void 0;let C=0,v=0,h=g.timeRemaining(),a=0;for(;this._i<this._tasks.length;){if(C=Date.now(),this._tasks[this._i]()||this._i++,C=Math.max(1,Date.now()-C),v=Math.max(C,v),a=g.timeRemaining(),1.5*v>a)return h-C<-20&&console.warn(`task queue exceeded allotted deadline by ${Math.abs(Math.round(h-C))}ms`),void this._start();h=a}this.clear()}}class u extends S{_requestCallback(g){return setTimeout(()=>g(this._createDeadline(16)))}_cancelCallback(g){clearTimeout(g)}_createDeadline(g){const C=Date.now()+g;return{timeRemaining:()=>Math.max(0,C-Date.now())}}}n.PriorityTaskQueue=u,n.IdleTaskQueue=!f.isNode&&"requestIdleCallback"in window?class extends S{_requestCallback(_){return requestIdleCallback(_)}_cancelCallback(_){cancelIdleCallback(_)}}:u,n.DebouncedIdleTask=class{constructor(){this._queue=new n.IdleTaskQueue}set(_){this._queue.clear(),this._queue.enqueue(_)}flush(){this._queue.flush()}}},9282:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.updateWindowsModeWrappedState=void 0;const f=c(643);n.updateWindowsModeWrappedState=function(S){const u=S.buffer.lines.get(S.buffer.ybase+S.buffer.y-1),_=u==null?void 0:u.get(S.cols-1),g=S.buffer.lines.get(S.buffer.ybase+S.buffer.y);g&&_&&(g.isWrapped=_[f.CHAR_DATA_CODE_INDEX]!==f.NULL_CELL_CODE&&_[f.CHAR_DATA_CODE_INDEX]!==f.WHITESPACE_CELL_CODE)}},3734:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.ExtendedAttrs=n.AttributeData=void 0;class c{constructor(){this.fg=0,this.bg=0,this.extended=new f}static toColorRGB(u){return[u>>>16&255,u>>>8&255,255&u]}static fromColorRGB(u){return(255&u[0])<<16|(255&u[1])<<8|255&u[2]}clone(){const u=new c;return u.fg=this.fg,u.bg=this.bg,u.extended=this.extended.clone(),u}isInverse(){return 67108864&this.fg}isBold(){return 134217728&this.fg}isUnderline(){return this.hasExtendedAttrs()&&this.extended.underlineStyle!==0?1:268435456&this.fg}isBlink(){return 536870912&this.fg}isInvisible(){return 1073741824&this.fg}isItalic(){return 67108864&this.bg}isDim(){return 134217728&this.bg}isStrikethrough(){return 2147483648&this.fg}isProtected(){return 536870912&this.bg}isOverline(){return 1073741824&this.bg}getFgColorMode(){return 50331648&this.fg}getBgColorMode(){return 50331648&this.bg}isFgRGB(){return(50331648&this.fg)==50331648}isBgRGB(){return(50331648&this.bg)==50331648}isFgPalette(){return(50331648&this.fg)==16777216||(50331648&this.fg)==33554432}isBgPalette(){return(50331648&this.bg)==16777216||(50331648&this.bg)==33554432}isFgDefault(){return(50331648&this.fg)==0}isBgDefault(){return(50331648&this.bg)==0}isAttributeDefault(){return this.fg===0&&this.bg===0}getFgColor(){switch(50331648&this.fg){case 16777216:case 33554432:return 255&this.fg;case 50331648:return 16777215&this.fg;default:return-1}}getBgColor(){switch(50331648&this.bg){case 16777216:case 33554432:return 255&this.bg;case 50331648:return 16777215&this.bg;default:return-1}}hasExtendedAttrs(){return 268435456&this.bg}updateExtended(){this.extended.isEmpty()?this.bg&=-268435457:this.bg|=268435456}getUnderlineColor(){if(268435456&this.bg&&~this.extended.underlineColor)switch(50331648&this.extended.underlineColor){case 16777216:case 33554432:return 255&this.extended.underlineColor;case 50331648:return 16777215&this.extended.underlineColor;default:return this.getFgColor()}return this.getFgColor()}getUnderlineColorMode(){return 268435456&this.bg&&~this.extended.underlineColor?50331648&this.extended.underlineColor:this.getFgColorMode()}isUnderlineColorRGB(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==50331648:this.isFgRGB()}isUnderlineColorPalette(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==16777216||(50331648&this.extended.underlineColor)==33554432:this.isFgPalette()}isUnderlineColorDefault(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==0:this.isFgDefault()}getUnderlineStyle(){return 268435456&this.fg?268435456&this.bg?this.extended.underlineStyle:1:0}getUnderlineVariantOffset(){return this.extended.underlineVariantOffset}}n.AttributeData=c;class f{get ext(){return this._urlId?-469762049&this._ext|this.underlineStyle<<26:this._ext}set ext(u){this._ext=u}get underlineStyle(){return this._urlId?5:(469762048&this._ext)>>26}set underlineStyle(u){this._ext&=-469762049,this._ext|=u<<26&469762048}get underlineColor(){return 67108863&this._ext}set underlineColor(u){this._ext&=-67108864,this._ext|=67108863&u}get urlId(){return this._urlId}set urlId(u){this._urlId=u}get underlineVariantOffset(){const u=(3758096384&this._ext)>>29;return u<0?4294967288^u:u}set underlineVariantOffset(u){this._ext&=536870911,this._ext|=u<<29&3758096384}constructor(u=0,_=0){this._ext=0,this._urlId=0,this._ext=u,this._urlId=_}clone(){return new f(this._ext,this._urlId)}isEmpty(){return this.underlineStyle===0&&this._urlId===0}}n.ExtendedAttrs=f},9092:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.Buffer=n.MAX_BUFFER_SIZE=void 0;const f=c(6349),S=c(7226),u=c(3734),_=c(8437),g=c(4634),C=c(511),v=c(643),h=c(4863),a=c(7116);n.MAX_BUFFER_SIZE=4294967295,n.Buffer=class{constructor(s,o,p){this._hasScrollback=s,this._optionsService=o,this._bufferService=p,this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.tabs={},this.savedY=0,this.savedX=0,this.savedCurAttrData=_.DEFAULT_ATTR_DATA.clone(),this.savedCharset=a.DEFAULT_CHARSET,this.markers=[],this._nullCell=C.CellData.fromCharData([0,v.NULL_CELL_CHAR,v.NULL_CELL_WIDTH,v.NULL_CELL_CODE]),this._whitespaceCell=C.CellData.fromCharData([0,v.WHITESPACE_CELL_CHAR,v.WHITESPACE_CELL_WIDTH,v.WHITESPACE_CELL_CODE]),this._isClearing=!1,this._memoryCleanupQueue=new S.IdleTaskQueue,this._memoryCleanupPosition=0,this._cols=this._bufferService.cols,this._rows=this._bufferService.rows,this.lines=new f.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}getNullCell(s){return s?(this._nullCell.fg=s.fg,this._nullCell.bg=s.bg,this._nullCell.extended=s.extended):(this._nullCell.fg=0,this._nullCell.bg=0,this._nullCell.extended=new u.ExtendedAttrs),this._nullCell}getWhitespaceCell(s){return s?(this._whitespaceCell.fg=s.fg,this._whitespaceCell.bg=s.bg,this._whitespaceCell.extended=s.extended):(this._whitespaceCell.fg=0,this._whitespaceCell.bg=0,this._whitespaceCell.extended=new u.ExtendedAttrs),this._whitespaceCell}getBlankLine(s,o){return new _.BufferLine(this._bufferService.cols,this.getNullCell(s),o)}get hasScrollback(){return this._hasScrollback&&this.lines.maxLength>this._rows}get isCursorInViewport(){const s=this.ybase+this.y-this.ydisp;return s>=0&&s<this._rows}_getCorrectBufferLength(s){if(!this._hasScrollback)return s;const o=s+this._optionsService.rawOptions.scrollback;return o>n.MAX_BUFFER_SIZE?n.MAX_BUFFER_SIZE:o}fillViewportRows(s){if(this.lines.length===0){s===void 0&&(s=_.DEFAULT_ATTR_DATA);let o=this._rows;for(;o--;)this.lines.push(this.getBlankLine(s))}}clear(){this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.lines=new f.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}resize(s,o){const p=this.getNullCell(_.DEFAULT_ATTR_DATA);let w=0;const E=this._getCorrectBufferLength(o);if(E>this.lines.maxLength&&(this.lines.maxLength=E),this.lines.length>0){if(this._cols<s)for(let y=0;y<this.lines.length;y++)w+=+this.lines.get(y).resize(s,p);let k=0;if(this._rows<o)for(let y=this._rows;y<o;y++)this.lines.length<o+this.ybase&&(this._optionsService.rawOptions.windowsMode||this._optionsService.rawOptions.windowsPty.backend!==void 0||this._optionsService.rawOptions.windowsPty.buildNumber!==void 0?this.lines.push(new _.BufferLine(s,p)):this.ybase>0&&this.lines.length<=this.ybase+this.y+k+1?(this.ybase--,k++,this.ydisp>0&&this.ydisp--):this.lines.push(new _.BufferLine(s,p)));else for(let y=this._rows;y>o;y--)this.lines.length>o+this.ybase&&(this.lines.length>this.ybase+this.y+1?this.lines.pop():(this.ybase++,this.ydisp++));if(E<this.lines.maxLength){const y=this.lines.length-E;y>0&&(this.lines.trimStart(y),this.ybase=Math.max(this.ybase-y,0),this.ydisp=Math.max(this.ydisp-y,0),this.savedY=Math.max(this.savedY-y,0)),this.lines.maxLength=E}this.x=Math.min(this.x,s-1),this.y=Math.min(this.y,o-1),k&&(this.y+=k),this.savedX=Math.min(this.savedX,s-1),this.scrollTop=0}if(this.scrollBottom=o-1,this._isReflowEnabled&&(this._reflow(s,o),this._cols>s))for(let k=0;k<this.lines.length;k++)w+=+this.lines.get(k).resize(s,p);this._cols=s,this._rows=o,this._memoryCleanupQueue.clear(),w>.1*this.lines.length&&(this._memoryCleanupPosition=0,this._memoryCleanupQueue.enqueue(()=>this._batchedMemoryCleanup()))}_batchedMemoryCleanup(){let s=!0;this._memoryCleanupPosition>=this.lines.length&&(this._memoryCleanupPosition=0,s=!1);let o=0;for(;this._memoryCleanupPosition<this.lines.length;)if(o+=this.lines.get(this._memoryCleanupPosition++).cleanupMemory(),o>100)return!0;return s}get _isReflowEnabled(){const s=this._optionsService.rawOptions.windowsPty;return s&&s.buildNumber?this._hasScrollback&&s.backend==="conpty"&&s.buildNumber>=21376:this._hasScrollback&&!this._optionsService.rawOptions.windowsMode}_reflow(s,o){this._cols!==s&&(s>this._cols?this._reflowLarger(s,o):this._reflowSmaller(s,o))}_reflowLarger(s,o){const p=(0,g.reflowLargerGetLinesToRemove)(this.lines,this._cols,s,this.ybase+this.y,this.getNullCell(_.DEFAULT_ATTR_DATA));if(p.length>0){const w=(0,g.reflowLargerCreateNewLayout)(this.lines,p);(0,g.reflowLargerApplyNewLayout)(this.lines,w.layout),this._reflowLargerAdjustViewport(s,o,w.countRemoved)}}_reflowLargerAdjustViewport(s,o,p){const w=this.getNullCell(_.DEFAULT_ATTR_DATA);let E=p;for(;E-- >0;)this.ybase===0?(this.y>0&&this.y--,this.lines.length<o&&this.lines.push(new _.BufferLine(s,w))):(this.ydisp===this.ybase&&this.ydisp--,this.ybase--);this.savedY=Math.max(this.savedY-p,0)}_reflowSmaller(s,o){const p=this.getNullCell(_.DEFAULT_ATTR_DATA),w=[];let E=0;for(let k=this.lines.length-1;k>=0;k--){let y=this.lines.get(k);if(!y||!y.isWrapped&&y.getTrimmedLength()<=s)continue;const b=[y];for(;y.isWrapped&&k>0;)y=this.lines.get(--k),b.unshift(y);const D=this.ybase+this.y;if(D>=k&&D<k+b.length)continue;const B=b[b.length-1].getTrimmedLength(),O=(0,g.reflowSmallerGetNewLineLengths)(b,this._cols,s),T=O.length-b.length;let H;H=this.ybase===0&&this.y!==this.lines.length-1?Math.max(0,this.y-this.lines.maxLength+T):Math.max(0,this.lines.length-this.lines.maxLength+T);const j=[];for(let A=0;A<T;A++){const P=this.getBlankLine(_.DEFAULT_ATTR_DATA,!0);j.push(P)}j.length>0&&(w.push({start:k+b.length+E,newLines:j}),E+=j.length),b.push(...j);let $=O.length-1,q=O[$];q===0&&($--,q=O[$]);let N=b.length-T-1,x=B;for(;N>=0;){const A=Math.min(x,q);if(b[$]===void 0)break;if(b[$].copyCellsFrom(b[N],x-A,q-A,A,!0),q-=A,q===0&&($--,q=O[$]),x-=A,x===0){N--;const P=Math.max(N,0);x=(0,g.getWrappedLineTrimmedLength)(b,P,this._cols)}}for(let A=0;A<b.length;A++)O[A]<s&&b[A].setCell(O[A],p);let R=T-H;for(;R-- >0;)this.ybase===0?this.y<o-1?(this.y++,this.lines.pop()):(this.ybase++,this.ydisp++):this.ybase<Math.min(this.lines.maxLength,this.lines.length+E)-o&&(this.ybase===this.ydisp&&this.ydisp++,this.ybase++);this.savedY=Math.min(this.savedY+T,this.ybase+o-1)}if(w.length>0){const k=[],y=[];for(let $=0;$<this.lines.length;$++)y.push(this.lines.get($));const b=this.lines.length;let D=b-1,B=0,O=w[B];this.lines.length=Math.min(this.lines.maxLength,this.lines.length+E);let T=0;for(let $=Math.min(this.lines.maxLength-1,b+E-1);$>=0;$--)if(O&&O.start>D+T){for(let q=O.newLines.length-1;q>=0;q--)this.lines.set($--,O.newLines[q]);$++,k.push({index:D+1,amount:O.newLines.length}),T+=O.newLines.length,O=w[++B]}else this.lines.set($,y[D--]);let H=0;for(let $=k.length-1;$>=0;$--)k[$].index+=H,this.lines.onInsertEmitter.fire(k[$]),H+=k[$].amount;const j=Math.max(0,b+E-this.lines.maxLength);j>0&&this.lines.onTrimEmitter.fire(j)}}translateBufferLineToString(s,o,p=0,w){const E=this.lines.get(s);return E?E.translateToString(o,p,w):""}getWrappedRangeForLine(s){let o=s,p=s;for(;o>0&&this.lines.get(o).isWrapped;)o--;for(;p+1<this.lines.length&&this.lines.get(p+1).isWrapped;)p++;return{first:o,last:p}}setupTabStops(s){for(s!=null?this.tabs[s]||(s=this.prevStop(s)):(this.tabs={},s=0);s<this._cols;s+=this._optionsService.rawOptions.tabStopWidth)this.tabs[s]=!0}prevStop(s){for(s==null&&(s=this.x);!this.tabs[--s]&&s>0;);return s>=this._cols?this._cols-1:s<0?0:s}nextStop(s){for(s==null&&(s=this.x);!this.tabs[++s]&&s<this._cols;);return s>=this._cols?this._cols-1:s<0?0:s}clearMarkers(s){this._isClearing=!0;for(let o=0;o<this.markers.length;o++)this.markers[o].line===s&&(this.markers[o].dispose(),this.markers.splice(o--,1));this._isClearing=!1}clearAllMarkers(){this._isClearing=!0;for(let s=0;s<this.markers.length;s++)this.markers[s].dispose(),this.markers.splice(s--,1);this._isClearing=!1}addMarker(s){const o=new h.Marker(s);return this.markers.push(o),o.register(this.lines.onTrim(p=>{o.line-=p,o.line<0&&o.dispose()})),o.register(this.lines.onInsert(p=>{o.line>=p.index&&(o.line+=p.amount)})),o.register(this.lines.onDelete(p=>{o.line>=p.index&&o.line<p.index+p.amount&&o.dispose(),o.line>p.index&&(o.line-=p.amount)})),o.register(o.onDispose(()=>this._removeMarker(o))),o}_removeMarker(s){this._isClearing||this.markers.splice(this.markers.indexOf(s),1)}}},8437:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.BufferLine=n.DEFAULT_ATTR_DATA=void 0;const f=c(3734),S=c(511),u=c(643),_=c(482);n.DEFAULT_ATTR_DATA=Object.freeze(new f.AttributeData);let g=0;class C{constructor(h,a,s=!1){this.isWrapped=s,this._combined={},this._extendedAttrs={},this._data=new Uint32Array(3*h);const o=a||S.CellData.fromCharData([0,u.NULL_CELL_CHAR,u.NULL_CELL_WIDTH,u.NULL_CELL_CODE]);for(let p=0;p<h;++p)this.setCell(p,o);this.length=h}get(h){const a=this._data[3*h+0],s=2097151&a;return[this._data[3*h+1],2097152&a?this._combined[h]:s?(0,_.stringFromCodePoint)(s):"",a>>22,2097152&a?this._combined[h].charCodeAt(this._combined[h].length-1):s]}set(h,a){this._data[3*h+1]=a[u.CHAR_DATA_ATTR_INDEX],a[u.CHAR_DATA_CHAR_INDEX].length>1?(this._combined[h]=a[1],this._data[3*h+0]=2097152|h|a[u.CHAR_DATA_WIDTH_INDEX]<<22):this._data[3*h+0]=a[u.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|a[u.CHAR_DATA_WIDTH_INDEX]<<22}getWidth(h){return this._data[3*h+0]>>22}hasWidth(h){return 12582912&this._data[3*h+0]}getFg(h){return this._data[3*h+1]}getBg(h){return this._data[3*h+2]}hasContent(h){return 4194303&this._data[3*h+0]}getCodePoint(h){const a=this._data[3*h+0];return 2097152&a?this._combined[h].charCodeAt(this._combined[h].length-1):2097151&a}isCombined(h){return 2097152&this._data[3*h+0]}getString(h){const a=this._data[3*h+0];return 2097152&a?this._combined[h]:2097151&a?(0,_.stringFromCodePoint)(2097151&a):""}isProtected(h){return 536870912&this._data[3*h+2]}loadCell(h,a){return g=3*h,a.content=this._data[g+0],a.fg=this._data[g+1],a.bg=this._data[g+2],2097152&a.content&&(a.combinedData=this._combined[h]),268435456&a.bg&&(a.extended=this._extendedAttrs[h]),a}setCell(h,a){2097152&a.content&&(this._combined[h]=a.combinedData),268435456&a.bg&&(this._extendedAttrs[h]=a.extended),this._data[3*h+0]=a.content,this._data[3*h+1]=a.fg,this._data[3*h+2]=a.bg}setCellFromCodepoint(h,a,s,o){268435456&o.bg&&(this._extendedAttrs[h]=o.extended),this._data[3*h+0]=a|s<<22,this._data[3*h+1]=o.fg,this._data[3*h+2]=o.bg}addCodepointToCell(h,a,s){let o=this._data[3*h+0];2097152&o?this._combined[h]+=(0,_.stringFromCodePoint)(a):2097151&o?(this._combined[h]=(0,_.stringFromCodePoint)(2097151&o)+(0,_.stringFromCodePoint)(a),o&=-2097152,o|=2097152):o=a|4194304,s&&(o&=-12582913,o|=s<<22),this._data[3*h+0]=o}insertCells(h,a,s){if((h%=this.length)&&this.getWidth(h-1)===2&&this.setCellFromCodepoint(h-1,0,1,s),a<this.length-h){const o=new S.CellData;for(let p=this.length-h-a-1;p>=0;--p)this.setCell(h+a+p,this.loadCell(h+p,o));for(let p=0;p<a;++p)this.setCell(h+p,s)}else for(let o=h;o<this.length;++o)this.setCell(o,s);this.getWidth(this.length-1)===2&&this.setCellFromCodepoint(this.length-1,0,1,s)}deleteCells(h,a,s){if(h%=this.length,a<this.length-h){const o=new S.CellData;for(let p=0;p<this.length-h-a;++p)this.setCell(h+p,this.loadCell(h+a+p,o));for(let p=this.length-a;p<this.length;++p)this.setCell(p,s)}else for(let o=h;o<this.length;++o)this.setCell(o,s);h&&this.getWidth(h-1)===2&&this.setCellFromCodepoint(h-1,0,1,s),this.getWidth(h)!==0||this.hasContent(h)||this.setCellFromCodepoint(h,0,1,s)}replaceCells(h,a,s,o=!1){if(o)for(h&&this.getWidth(h-1)===2&&!this.isProtected(h-1)&&this.setCellFromCodepoint(h-1,0,1,s),a<this.length&&this.getWidth(a-1)===2&&!this.isProtected(a)&&this.setCellFromCodepoint(a,0,1,s);h<a&&h<this.length;)this.isProtected(h)||this.setCell(h,s),h++;else for(h&&this.getWidth(h-1)===2&&this.setCellFromCodepoint(h-1,0,1,s),a<this.length&&this.getWidth(a-1)===2&&this.setCellFromCodepoint(a,0,1,s);h<a&&h<this.length;)this.setCell(h++,s)}resize(h,a){if(h===this.length)return 4*this._data.length*2<this._data.buffer.byteLength;const s=3*h;if(h>this.length){if(this._data.buffer.byteLength>=4*s)this._data=new Uint32Array(this._data.buffer,0,s);else{const o=new Uint32Array(s);o.set(this._data),this._data=o}for(let o=this.length;o<h;++o)this.setCell(o,a)}else{this._data=this._data.subarray(0,s);const o=Object.keys(this._combined);for(let w=0;w<o.length;w++){const E=parseInt(o[w],10);E>=h&&delete this._combined[E]}const p=Object.keys(this._extendedAttrs);for(let w=0;w<p.length;w++){const E=parseInt(p[w],10);E>=h&&delete this._extendedAttrs[E]}}return this.length=h,4*s*2<this._data.buffer.byteLength}cleanupMemory(){if(4*this._data.length*2<this._data.buffer.byteLength){const h=new Uint32Array(this._data.length);return h.set(this._data),this._data=h,1}return 0}fill(h,a=!1){if(a)for(let s=0;s<this.length;++s)this.isProtected(s)||this.setCell(s,h);else{this._combined={},this._extendedAttrs={};for(let s=0;s<this.length;++s)this.setCell(s,h)}}copyFrom(h){this.length!==h.length?this._data=new Uint32Array(h._data):this._data.set(h._data),this.length=h.length,this._combined={};for(const a in h._combined)this._combined[a]=h._combined[a];this._extendedAttrs={};for(const a in h._extendedAttrs)this._extendedAttrs[a]=h._extendedAttrs[a];this.isWrapped=h.isWrapped}clone(){const h=new C(0);h._data=new Uint32Array(this._data),h.length=this.length;for(const a in this._combined)h._combined[a]=this._combined[a];for(const a in this._extendedAttrs)h._extendedAttrs[a]=this._extendedAttrs[a];return h.isWrapped=this.isWrapped,h}getTrimmedLength(){for(let h=this.length-1;h>=0;--h)if(4194303&this._data[3*h+0])return h+(this._data[3*h+0]>>22);return 0}getNoBgTrimmedLength(){for(let h=this.length-1;h>=0;--h)if(4194303&this._data[3*h+0]||50331648&this._data[3*h+2])return h+(this._data[3*h+0]>>22);return 0}copyCellsFrom(h,a,s,o,p){const w=h._data;if(p)for(let k=o-1;k>=0;k--){for(let y=0;y<3;y++)this._data[3*(s+k)+y]=w[3*(a+k)+y];268435456&w[3*(a+k)+2]&&(this._extendedAttrs[s+k]=h._extendedAttrs[a+k])}else for(let k=0;k<o;k++){for(let y=0;y<3;y++)this._data[3*(s+k)+y]=w[3*(a+k)+y];268435456&w[3*(a+k)+2]&&(this._extendedAttrs[s+k]=h._extendedAttrs[a+k])}const E=Object.keys(h._combined);for(let k=0;k<E.length;k++){const y=parseInt(E[k],10);y>=a&&(this._combined[y-a+s]=h._combined[y])}}translateToString(h,a,s,o){a=a??0,s=s??this.length,h&&(s=Math.min(s,this.getTrimmedLength())),o&&(o.length=0);let p="";for(;a<s;){const w=this._data[3*a+0],E=2097151&w,k=2097152&w?this._combined[a]:E?(0,_.stringFromCodePoint)(E):u.WHITESPACE_CELL_CHAR;if(p+=k,o)for(let y=0;y<k.length;++y)o.push(a);a+=w>>22||1}return o&&o.push(a),p}}n.BufferLine=C},4841:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.getRangeLength=void 0,n.getRangeLength=function(c,f){if(c.start.y>c.end.y)throw new Error(`Buffer range end (${c.end.x}, ${c.end.y}) cannot be before start (${c.start.x}, ${c.start.y})`);return f*(c.end.y-c.start.y)+(c.end.x-c.start.x+1)}},4634:(m,n)=>{function c(f,S,u){if(S===f.length-1)return f[S].getTrimmedLength();const _=!f[S].hasContent(u-1)&&f[S].getWidth(u-1)===1,g=f[S+1].getWidth(0)===2;return _&&g?u-1:u}Object.defineProperty(n,"__esModule",{value:!0}),n.getWrappedLineTrimmedLength=n.reflowSmallerGetNewLineLengths=n.reflowLargerApplyNewLayout=n.reflowLargerCreateNewLayout=n.reflowLargerGetLinesToRemove=void 0,n.reflowLargerGetLinesToRemove=function(f,S,u,_,g){const C=[];for(let v=0;v<f.length-1;v++){let h=v,a=f.get(++h);if(!a.isWrapped)continue;const s=[f.get(v)];for(;h<f.length&&a.isWrapped;)s.push(a),a=f.get(++h);if(_>=v&&_<h){v+=s.length-1;continue}let o=0,p=c(s,o,S),w=1,E=0;for(;w<s.length;){const y=c(s,w,S),b=y-E,D=u-p,B=Math.min(b,D);s[o].copyCellsFrom(s[w],E,p,B,!1),p+=B,p===u&&(o++,p=0),E+=B,E===y&&(w++,E=0),p===0&&o!==0&&s[o-1].getWidth(u-1)===2&&(s[o].copyCellsFrom(s[o-1],u-1,p++,1,!1),s[o-1].setCell(u-1,g))}s[o].replaceCells(p,u,g);let k=0;for(let y=s.length-1;y>0&&(y>o||s[y].getTrimmedLength()===0);y--)k++;k>0&&(C.push(v+s.length-k),C.push(k)),v+=s.length-1}return C},n.reflowLargerCreateNewLayout=function(f,S){const u=[];let _=0,g=S[_],C=0;for(let v=0;v<f.length;v++)if(g===v){const h=S[++_];f.onDeleteEmitter.fire({index:v-C,amount:h}),v+=h-1,C+=h,g=S[++_]}else u.push(v);return{layout:u,countRemoved:C}},n.reflowLargerApplyNewLayout=function(f,S){const u=[];for(let _=0;_<S.length;_++)u.push(f.get(S[_]));for(let _=0;_<u.length;_++)f.set(_,u[_]);f.length=S.length},n.reflowSmallerGetNewLineLengths=function(f,S,u){const _=[],g=f.map((a,s)=>c(f,s,S)).reduce((a,s)=>a+s);let C=0,v=0,h=0;for(;h<g;){if(g-h<u){_.push(g-h);break}C+=u;const a=c(f,v,S);C>a&&(C-=a,v++);const s=f[v].getWidth(C-1)===2;s&&C--;const o=s?u-1:u;_.push(o),h+=o}return _},n.getWrappedLineTrimmedLength=c},5295:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.BufferSet=void 0;const f=c(8460),S=c(844),u=c(9092);class _ extends S.Disposable{constructor(C,v){super(),this._optionsService=C,this._bufferService=v,this._onBufferActivate=this.register(new f.EventEmitter),this.onBufferActivate=this._onBufferActivate.event,this.reset(),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.resize(this._bufferService.cols,this._bufferService.rows))),this.register(this._optionsService.onSpecificOptionChange("tabStopWidth",()=>this.setupTabStops()))}reset(){this._normal=new u.Buffer(!0,this._optionsService,this._bufferService),this._normal.fillViewportRows(),this._alt=new u.Buffer(!1,this._optionsService,this._bufferService),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}),this.setupTabStops()}get alt(){return this._alt}get active(){return this._activeBuffer}get normal(){return this._normal}activateNormalBuffer(){this._activeBuffer!==this._normal&&(this._normal.x=this._alt.x,this._normal.y=this._alt.y,this._alt.clearAllMarkers(),this._alt.clear(),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}))}activateAltBuffer(C){this._activeBuffer!==this._alt&&(this._alt.fillViewportRows(C),this._alt.x=this._normal.x,this._alt.y=this._normal.y,this._activeBuffer=this._alt,this._onBufferActivate.fire({activeBuffer:this._alt,inactiveBuffer:this._normal}))}resize(C,v){this._normal.resize(C,v),this._alt.resize(C,v),this.setupTabStops(C)}setupTabStops(C){this._normal.setupTabStops(C),this._alt.setupTabStops(C)}}n.BufferSet=_},511:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.CellData=void 0;const f=c(482),S=c(643),u=c(3734);class _ extends u.AttributeData{constructor(){super(...arguments),this.content=0,this.fg=0,this.bg=0,this.extended=new u.ExtendedAttrs,this.combinedData=""}static fromCharData(C){const v=new _;return v.setFromCharData(C),v}isCombined(){return 2097152&this.content}getWidth(){return this.content>>22}getChars(){return 2097152&this.content?this.combinedData:2097151&this.content?(0,f.stringFromCodePoint)(2097151&this.content):""}getCode(){return this.isCombined()?this.combinedData.charCodeAt(this.combinedData.length-1):2097151&this.content}setFromCharData(C){this.fg=C[S.CHAR_DATA_ATTR_INDEX],this.bg=0;let v=!1;if(C[S.CHAR_DATA_CHAR_INDEX].length>2)v=!0;else if(C[S.CHAR_DATA_CHAR_INDEX].length===2){const h=C[S.CHAR_DATA_CHAR_INDEX].charCodeAt(0);if(55296<=h&&h<=56319){const a=C[S.CHAR_DATA_CHAR_INDEX].charCodeAt(1);56320<=a&&a<=57343?this.content=1024*(h-55296)+a-56320+65536|C[S.CHAR_DATA_WIDTH_INDEX]<<22:v=!0}else v=!0}else this.content=C[S.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|C[S.CHAR_DATA_WIDTH_INDEX]<<22;v&&(this.combinedData=C[S.CHAR_DATA_CHAR_INDEX],this.content=2097152|C[S.CHAR_DATA_WIDTH_INDEX]<<22)}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}n.CellData=_},643:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.WHITESPACE_CELL_CODE=n.WHITESPACE_CELL_WIDTH=n.WHITESPACE_CELL_CHAR=n.NULL_CELL_CODE=n.NULL_CELL_WIDTH=n.NULL_CELL_CHAR=n.CHAR_DATA_CODE_INDEX=n.CHAR_DATA_WIDTH_INDEX=n.CHAR_DATA_CHAR_INDEX=n.CHAR_DATA_ATTR_INDEX=n.DEFAULT_EXT=n.DEFAULT_ATTR=n.DEFAULT_COLOR=void 0,n.DEFAULT_COLOR=0,n.DEFAULT_ATTR=256|n.DEFAULT_COLOR<<9,n.DEFAULT_EXT=0,n.CHAR_DATA_ATTR_INDEX=0,n.CHAR_DATA_CHAR_INDEX=1,n.CHAR_DATA_WIDTH_INDEX=2,n.CHAR_DATA_CODE_INDEX=3,n.NULL_CELL_CHAR="",n.NULL_CELL_WIDTH=1,n.NULL_CELL_CODE=0,n.WHITESPACE_CELL_CHAR=" ",n.WHITESPACE_CELL_WIDTH=1,n.WHITESPACE_CELL_CODE=32},4863:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.Marker=void 0;const f=c(8460),S=c(844);class u{get id(){return this._id}constructor(g){this.line=g,this.isDisposed=!1,this._disposables=[],this._id=u._nextId++,this._onDispose=this.register(new f.EventEmitter),this.onDispose=this._onDispose.event}dispose(){this.isDisposed||(this.isDisposed=!0,this.line=-1,this._onDispose.fire(),(0,S.disposeArray)(this._disposables),this._disposables.length=0)}register(g){return this._disposables.push(g),g}}n.Marker=u,u._nextId=1},7116:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.DEFAULT_CHARSET=n.CHARSETS=void 0,n.CHARSETS={},n.DEFAULT_CHARSET=n.CHARSETS.B,n.CHARSETS[0]={"`":"◆",a:"▒",b:"␉",c:"␌",d:"␍",e:"␊",f:"°",g:"±",h:"␤",i:"␋",j:"┘",k:"┐",l:"┌",m:"└",n:"┼",o:"⎺",p:"⎻",q:"─",r:"⎼",s:"⎽",t:"├",u:"┤",v:"┴",w:"┬",x:"│",y:"≤",z:"≥","{":"π","|":"≠","}":"£","~":"·"},n.CHARSETS.A={"#":"£"},n.CHARSETS.B=void 0,n.CHARSETS[4]={"#":"£","@":"¾","[":"ij","\\":"½","]":"|","{":"¨","|":"f","}":"¼","~":"´"},n.CHARSETS.C=n.CHARSETS[5]={"[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},n.CHARSETS.R={"#":"£","@":"à","[":"°","\\":"ç","]":"§","{":"é","|":"ù","}":"è","~":"¨"},n.CHARSETS.Q={"@":"à","[":"â","\\":"ç","]":"ê","^":"î","`":"ô","{":"é","|":"ù","}":"è","~":"û"},n.CHARSETS.K={"@":"§","[":"Ä","\\":"Ö","]":"Ü","{":"ä","|":"ö","}":"ü","~":"ß"},n.CHARSETS.Y={"#":"£","@":"§","[":"°","\\":"ç","]":"é","`":"ù","{":"à","|":"ò","}":"è","~":"ì"},n.CHARSETS.E=n.CHARSETS[6]={"@":"Ä","[":"Æ","\\":"Ø","]":"Å","^":"Ü","`":"ä","{":"æ","|":"ø","}":"å","~":"ü"},n.CHARSETS.Z={"#":"£","@":"§","[":"¡","\\":"Ñ","]":"¿","{":"°","|":"ñ","}":"ç"},n.CHARSETS.H=n.CHARSETS[7]={"@":"É","[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},n.CHARSETS["="]={"#":"ù","@":"à","[":"é","\\":"ç","]":"ê","^":"î",_:"è","`":"ô","{":"ä","|":"ö","}":"ü","~":"û"}},2584:(m,n)=>{var c,f,S;Object.defineProperty(n,"__esModule",{value:!0}),n.C1_ESCAPED=n.C1=n.C0=void 0,function(u){u.NUL="\0",u.SOH="",u.STX="",u.ETX="",u.EOT="",u.ENQ="",u.ACK="",u.BEL="\x07",u.BS="\b",u.HT="	",u.LF=`
`,u.VT="\v",u.FF="\f",u.CR="\r",u.SO="",u.SI="",u.DLE="",u.DC1="",u.DC2="",u.DC3="",u.DC4="",u.NAK="",u.SYN="",u.ETB="",u.CAN="",u.EM="",u.SUB="",u.ESC="\x1B",u.FS="",u.GS="",u.RS="",u.US="",u.SP=" ",u.DEL=""}(c||(n.C0=c={})),function(u){u.PAD="",u.HOP="",u.BPH="",u.NBH="",u.IND="",u.NEL="",u.SSA="",u.ESA="",u.HTS="",u.HTJ="",u.VTS="",u.PLD="",u.PLU="",u.RI="",u.SS2="",u.SS3="",u.DCS="",u.PU1="",u.PU2="",u.STS="",u.CCH="",u.MW="",u.SPA="",u.EPA="",u.SOS="",u.SGCI="",u.SCI="",u.CSI="",u.ST="",u.OSC="",u.PM="",u.APC=""}(f||(n.C1=f={})),function(u){u.ST=`${c.ESC}\\`}(S||(n.C1_ESCAPED=S={}))},7399:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.evaluateKeyboardEvent=void 0;const f=c(2584),S={48:["0",")"],49:["1","!"],50:["2","@"],51:["3","#"],52:["4","$"],53:["5","%"],54:["6","^"],55:["7","&"],56:["8","*"],57:["9","("],186:[";",":"],187:["=","+"],188:[",","<"],189:["-","_"],190:[".",">"],191:["/","?"],192:["`","~"],219:["[","{"],220:["\\","|"],221:["]","}"],222:["'",'"']};n.evaluateKeyboardEvent=function(u,_,g,C){const v={type:0,cancel:!1,key:void 0},h=(u.shiftKey?1:0)|(u.altKey?2:0)|(u.ctrlKey?4:0)|(u.metaKey?8:0);switch(u.keyCode){case 0:u.key==="UIKeyInputUpArrow"?v.key=_?f.C0.ESC+"OA":f.C0.ESC+"[A":u.key==="UIKeyInputLeftArrow"?v.key=_?f.C0.ESC+"OD":f.C0.ESC+"[D":u.key==="UIKeyInputRightArrow"?v.key=_?f.C0.ESC+"OC":f.C0.ESC+"[C":u.key==="UIKeyInputDownArrow"&&(v.key=_?f.C0.ESC+"OB":f.C0.ESC+"[B");break;case 8:v.key=u.ctrlKey?"\b":f.C0.DEL,u.altKey&&(v.key=f.C0.ESC+v.key);break;case 9:if(u.shiftKey){v.key=f.C0.ESC+"[Z";break}v.key=f.C0.HT,v.cancel=!0;break;case 13:v.key=u.altKey?f.C0.ESC+f.C0.CR:f.C0.CR,v.cancel=!0;break;case 27:v.key=f.C0.ESC,u.altKey&&(v.key=f.C0.ESC+f.C0.ESC),v.cancel=!0;break;case 37:if(u.metaKey)break;h?(v.key=f.C0.ESC+"[1;"+(h+1)+"D",v.key===f.C0.ESC+"[1;3D"&&(v.key=f.C0.ESC+(g?"b":"[1;5D"))):v.key=_?f.C0.ESC+"OD":f.C0.ESC+"[D";break;case 39:if(u.metaKey)break;h?(v.key=f.C0.ESC+"[1;"+(h+1)+"C",v.key===f.C0.ESC+"[1;3C"&&(v.key=f.C0.ESC+(g?"f":"[1;5C"))):v.key=_?f.C0.ESC+"OC":f.C0.ESC+"[C";break;case 38:if(u.metaKey)break;h?(v.key=f.C0.ESC+"[1;"+(h+1)+"A",g||v.key!==f.C0.ESC+"[1;3A"||(v.key=f.C0.ESC+"[1;5A")):v.key=_?f.C0.ESC+"OA":f.C0.ESC+"[A";break;case 40:if(u.metaKey)break;h?(v.key=f.C0.ESC+"[1;"+(h+1)+"B",g||v.key!==f.C0.ESC+"[1;3B"||(v.key=f.C0.ESC+"[1;5B")):v.key=_?f.C0.ESC+"OB":f.C0.ESC+"[B";break;case 45:u.shiftKey||u.ctrlKey||(v.key=f.C0.ESC+"[2~");break;case 46:v.key=h?f.C0.ESC+"[3;"+(h+1)+"~":f.C0.ESC+"[3~";break;case 36:v.key=h?f.C0.ESC+"[1;"+(h+1)+"H":_?f.C0.ESC+"OH":f.C0.ESC+"[H";break;case 35:v.key=h?f.C0.ESC+"[1;"+(h+1)+"F":_?f.C0.ESC+"OF":f.C0.ESC+"[F";break;case 33:u.shiftKey?v.type=2:u.ctrlKey?v.key=f.C0.ESC+"[5;"+(h+1)+"~":v.key=f.C0.ESC+"[5~";break;case 34:u.shiftKey?v.type=3:u.ctrlKey?v.key=f.C0.ESC+"[6;"+(h+1)+"~":v.key=f.C0.ESC+"[6~";break;case 112:v.key=h?f.C0.ESC+"[1;"+(h+1)+"P":f.C0.ESC+"OP";break;case 113:v.key=h?f.C0.ESC+"[1;"+(h+1)+"Q":f.C0.ESC+"OQ";break;case 114:v.key=h?f.C0.ESC+"[1;"+(h+1)+"R":f.C0.ESC+"OR";break;case 115:v.key=h?f.C0.ESC+"[1;"+(h+1)+"S":f.C0.ESC+"OS";break;case 116:v.key=h?f.C0.ESC+"[15;"+(h+1)+"~":f.C0.ESC+"[15~";break;case 117:v.key=h?f.C0.ESC+"[17;"+(h+1)+"~":f.C0.ESC+"[17~";break;case 118:v.key=h?f.C0.ESC+"[18;"+(h+1)+"~":f.C0.ESC+"[18~";break;case 119:v.key=h?f.C0.ESC+"[19;"+(h+1)+"~":f.C0.ESC+"[19~";break;case 120:v.key=h?f.C0.ESC+"[20;"+(h+1)+"~":f.C0.ESC+"[20~";break;case 121:v.key=h?f.C0.ESC+"[21;"+(h+1)+"~":f.C0.ESC+"[21~";break;case 122:v.key=h?f.C0.ESC+"[23;"+(h+1)+"~":f.C0.ESC+"[23~";break;case 123:v.key=h?f.C0.ESC+"[24;"+(h+1)+"~":f.C0.ESC+"[24~";break;default:if(!u.ctrlKey||u.shiftKey||u.altKey||u.metaKey)if(g&&!C||!u.altKey||u.metaKey)!g||u.altKey||u.ctrlKey||u.shiftKey||!u.metaKey?u.key&&!u.ctrlKey&&!u.altKey&&!u.metaKey&&u.keyCode>=48&&u.key.length===1?v.key=u.key:u.key&&u.ctrlKey&&(u.key==="_"&&(v.key=f.C0.US),u.key==="@"&&(v.key=f.C0.NUL)):u.keyCode===65&&(v.type=1);else{const a=S[u.keyCode],s=a==null?void 0:a[u.shiftKey?1:0];if(s)v.key=f.C0.ESC+s;else if(u.keyCode>=65&&u.keyCode<=90){const o=u.ctrlKey?u.keyCode-64:u.keyCode+32;let p=String.fromCharCode(o);u.shiftKey&&(p=p.toUpperCase()),v.key=f.C0.ESC+p}else if(u.keyCode===32)v.key=f.C0.ESC+(u.ctrlKey?f.C0.NUL:" ");else if(u.key==="Dead"&&u.code.startsWith("Key")){let o=u.code.slice(3,4);u.shiftKey||(o=o.toLowerCase()),v.key=f.C0.ESC+o,v.cancel=!0}}else u.keyCode>=65&&u.keyCode<=90?v.key=String.fromCharCode(u.keyCode-64):u.keyCode===32?v.key=f.C0.NUL:u.keyCode>=51&&u.keyCode<=55?v.key=String.fromCharCode(u.keyCode-51+27):u.keyCode===56?v.key=f.C0.DEL:u.keyCode===219?v.key=f.C0.ESC:u.keyCode===220?v.key=f.C0.FS:u.keyCode===221&&(v.key=f.C0.GS)}return v}},482:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.Utf8ToUtf32=n.StringToUtf32=n.utf32ToString=n.stringFromCodePoint=void 0,n.stringFromCodePoint=function(c){return c>65535?(c-=65536,String.fromCharCode(55296+(c>>10))+String.fromCharCode(c%1024+56320)):String.fromCharCode(c)},n.utf32ToString=function(c,f=0,S=c.length){let u="";for(let _=f;_<S;++_){let g=c[_];g>65535?(g-=65536,u+=String.fromCharCode(55296+(g>>10))+String.fromCharCode(g%1024+56320)):u+=String.fromCharCode(g)}return u},n.StringToUtf32=class{constructor(){this._interim=0}clear(){this._interim=0}decode(c,f){const S=c.length;if(!S)return 0;let u=0,_=0;if(this._interim){const g=c.charCodeAt(_++);56320<=g&&g<=57343?f[u++]=1024*(this._interim-55296)+g-56320+65536:(f[u++]=this._interim,f[u++]=g),this._interim=0}for(let g=_;g<S;++g){const C=c.charCodeAt(g);if(55296<=C&&C<=56319){if(++g>=S)return this._interim=C,u;const v=c.charCodeAt(g);56320<=v&&v<=57343?f[u++]=1024*(C-55296)+v-56320+65536:(f[u++]=C,f[u++]=v)}else C!==65279&&(f[u++]=C)}return u}},n.Utf8ToUtf32=class{constructor(){this.interim=new Uint8Array(3)}clear(){this.interim.fill(0)}decode(c,f){const S=c.length;if(!S)return 0;let u,_,g,C,v=0,h=0,a=0;if(this.interim[0]){let p=!1,w=this.interim[0];w&=(224&w)==192?31:(240&w)==224?15:7;let E,k=0;for(;(E=63&this.interim[++k])&&k<4;)w<<=6,w|=E;const y=(224&this.interim[0])==192?2:(240&this.interim[0])==224?3:4,b=y-k;for(;a<b;){if(a>=S)return 0;if(E=c[a++],(192&E)!=128){a--,p=!0;break}this.interim[k++]=E,w<<=6,w|=63&E}p||(y===2?w<128?a--:f[v++]=w:y===3?w<2048||w>=55296&&w<=57343||w===65279||(f[v++]=w):w<65536||w>1114111||(f[v++]=w)),this.interim.fill(0)}const s=S-4;let o=a;for(;o<S;){for(;!(!(o<s)||128&(u=c[o])||128&(_=c[o+1])||128&(g=c[o+2])||128&(C=c[o+3]));)f[v++]=u,f[v++]=_,f[v++]=g,f[v++]=C,o+=4;if(u=c[o++],u<128)f[v++]=u;else if((224&u)==192){if(o>=S)return this.interim[0]=u,v;if(_=c[o++],(192&_)!=128){o--;continue}if(h=(31&u)<<6|63&_,h<128){o--;continue}f[v++]=h}else if((240&u)==224){if(o>=S)return this.interim[0]=u,v;if(_=c[o++],(192&_)!=128){o--;continue}if(o>=S)return this.interim[0]=u,this.interim[1]=_,v;if(g=c[o++],(192&g)!=128){o--;continue}if(h=(15&u)<<12|(63&_)<<6|63&g,h<2048||h>=55296&&h<=57343||h===65279)continue;f[v++]=h}else if((248&u)==240){if(o>=S)return this.interim[0]=u,v;if(_=c[o++],(192&_)!=128){o--;continue}if(o>=S)return this.interim[0]=u,this.interim[1]=_,v;if(g=c[o++],(192&g)!=128){o--;continue}if(o>=S)return this.interim[0]=u,this.interim[1]=_,this.interim[2]=g,v;if(C=c[o++],(192&C)!=128){o--;continue}if(h=(7&u)<<18|(63&_)<<12|(63&g)<<6|63&C,h<65536||h>1114111)continue;f[v++]=h}}return v}}},225:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.UnicodeV6=void 0;const f=c(1480),S=[[768,879],[1155,1158],[1160,1161],[1425,1469],[1471,1471],[1473,1474],[1476,1477],[1479,1479],[1536,1539],[1552,1557],[1611,1630],[1648,1648],[1750,1764],[1767,1768],[1770,1773],[1807,1807],[1809,1809],[1840,1866],[1958,1968],[2027,2035],[2305,2306],[2364,2364],[2369,2376],[2381,2381],[2385,2388],[2402,2403],[2433,2433],[2492,2492],[2497,2500],[2509,2509],[2530,2531],[2561,2562],[2620,2620],[2625,2626],[2631,2632],[2635,2637],[2672,2673],[2689,2690],[2748,2748],[2753,2757],[2759,2760],[2765,2765],[2786,2787],[2817,2817],[2876,2876],[2879,2879],[2881,2883],[2893,2893],[2902,2902],[2946,2946],[3008,3008],[3021,3021],[3134,3136],[3142,3144],[3146,3149],[3157,3158],[3260,3260],[3263,3263],[3270,3270],[3276,3277],[3298,3299],[3393,3395],[3405,3405],[3530,3530],[3538,3540],[3542,3542],[3633,3633],[3636,3642],[3655,3662],[3761,3761],[3764,3769],[3771,3772],[3784,3789],[3864,3865],[3893,3893],[3895,3895],[3897,3897],[3953,3966],[3968,3972],[3974,3975],[3984,3991],[3993,4028],[4038,4038],[4141,4144],[4146,4146],[4150,4151],[4153,4153],[4184,4185],[4448,4607],[4959,4959],[5906,5908],[5938,5940],[5970,5971],[6002,6003],[6068,6069],[6071,6077],[6086,6086],[6089,6099],[6109,6109],[6155,6157],[6313,6313],[6432,6434],[6439,6440],[6450,6450],[6457,6459],[6679,6680],[6912,6915],[6964,6964],[6966,6970],[6972,6972],[6978,6978],[7019,7027],[7616,7626],[7678,7679],[8203,8207],[8234,8238],[8288,8291],[8298,8303],[8400,8431],[12330,12335],[12441,12442],[43014,43014],[43019,43019],[43045,43046],[64286,64286],[65024,65039],[65056,65059],[65279,65279],[65529,65531]],u=[[68097,68099],[68101,68102],[68108,68111],[68152,68154],[68159,68159],[119143,119145],[119155,119170],[119173,119179],[119210,119213],[119362,119364],[917505,917505],[917536,917631],[917760,917999]];let _;n.UnicodeV6=class{constructor(){if(this.version="6",!_){_=new Uint8Array(65536),_.fill(1),_[0]=0,_.fill(0,1,32),_.fill(0,127,160),_.fill(2,4352,4448),_[9001]=2,_[9002]=2,_.fill(2,11904,42192),_[12351]=1,_.fill(2,44032,55204),_.fill(2,63744,64256),_.fill(2,65040,65050),_.fill(2,65072,65136),_.fill(2,65280,65377),_.fill(2,65504,65511);for(let g=0;g<S.length;++g)_.fill(0,S[g][0],S[g][1]+1)}}wcwidth(g){return g<32?0:g<127?1:g<65536?_[g]:function(C,v){let h,a=0,s=v.length-1;if(C<v[0][0]||C>v[s][1])return!1;for(;s>=a;)if(h=a+s>>1,C>v[h][1])a=h+1;else{if(!(C<v[h][0]))return!0;s=h-1}return!1}(g,u)?0:g>=131072&&g<=196605||g>=196608&&g<=262141?2:1}charProperties(g,C){let v=this.wcwidth(g),h=v===0&&C!==0;if(h){const a=f.UnicodeService.extractWidth(C);a===0?h=!1:a>v&&(v=a)}return f.UnicodeService.createPropertyValue(0,v,h)}}},5981:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.WriteBuffer=void 0;const f=c(8460),S=c(844);class u extends S.Disposable{constructor(g){super(),this._action=g,this._writeBuffer=[],this._callbacks=[],this._pendingData=0,this._bufferOffset=0,this._isSyncWriting=!1,this._syncCalls=0,this._didUserInput=!1,this._onWriteParsed=this.register(new f.EventEmitter),this.onWriteParsed=this._onWriteParsed.event}handleUserInput(){this._didUserInput=!0}writeSync(g,C){if(C!==void 0&&this._syncCalls>C)return void(this._syncCalls=0);if(this._pendingData+=g.length,this._writeBuffer.push(g),this._callbacks.push(void 0),this._syncCalls++,this._isSyncWriting)return;let v;for(this._isSyncWriting=!0;v=this._writeBuffer.shift();){this._action(v);const h=this._callbacks.shift();h&&h()}this._pendingData=0,this._bufferOffset=2147483647,this._isSyncWriting=!1,this._syncCalls=0}write(g,C){if(this._pendingData>5e7)throw new Error("write data discarded, use flow control to avoid losing data");if(!this._writeBuffer.length){if(this._bufferOffset=0,this._didUserInput)return this._didUserInput=!1,this._pendingData+=g.length,this._writeBuffer.push(g),this._callbacks.push(C),void this._innerWrite();setTimeout(()=>this._innerWrite())}this._pendingData+=g.length,this._writeBuffer.push(g),this._callbacks.push(C)}_innerWrite(g=0,C=!0){const v=g||Date.now();for(;this._writeBuffer.length>this._bufferOffset;){const h=this._writeBuffer[this._bufferOffset],a=this._action(h,C);if(a){const o=p=>Date.now()-v>=12?setTimeout(()=>this._innerWrite(0,p)):this._innerWrite(v,p);return void a.catch(p=>(queueMicrotask(()=>{throw p}),Promise.resolve(!1))).then(o)}const s=this._callbacks[this._bufferOffset];if(s&&s(),this._bufferOffset++,this._pendingData-=h.length,Date.now()-v>=12)break}this._writeBuffer.length>this._bufferOffset?(this._bufferOffset>50&&(this._writeBuffer=this._writeBuffer.slice(this._bufferOffset),this._callbacks=this._callbacks.slice(this._bufferOffset),this._bufferOffset=0),setTimeout(()=>this._innerWrite())):(this._writeBuffer.length=0,this._callbacks.length=0,this._pendingData=0,this._bufferOffset=0),this._onWriteParsed.fire()}}n.WriteBuffer=u},5941:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.toRgbString=n.parseColor=void 0;const c=/^([\da-f])\/([\da-f])\/([\da-f])$|^([\da-f]{2})\/([\da-f]{2})\/([\da-f]{2})$|^([\da-f]{3})\/([\da-f]{3})\/([\da-f]{3})$|^([\da-f]{4})\/([\da-f]{4})\/([\da-f]{4})$/,f=/^[\da-f]+$/;function S(u,_){const g=u.toString(16),C=g.length<2?"0"+g:g;switch(_){case 4:return g[0];case 8:return C;case 12:return(C+C).slice(0,3);default:return C+C}}n.parseColor=function(u){if(!u)return;let _=u.toLowerCase();if(_.indexOf("rgb:")===0){_=_.slice(4);const g=c.exec(_);if(g){const C=g[1]?15:g[4]?255:g[7]?4095:65535;return[Math.round(parseInt(g[1]||g[4]||g[7]||g[10],16)/C*255),Math.round(parseInt(g[2]||g[5]||g[8]||g[11],16)/C*255),Math.round(parseInt(g[3]||g[6]||g[9]||g[12],16)/C*255)]}}else if(_.indexOf("#")===0&&(_=_.slice(1),f.exec(_)&&[3,6,9,12].includes(_.length))){const g=_.length/3,C=[0,0,0];for(let v=0;v<3;++v){const h=parseInt(_.slice(g*v,g*v+g),16);C[v]=g===1?h<<4:g===2?h:g===3?h>>4:h>>8}return C}},n.toRgbString=function(u,_=16){const[g,C,v]=u;return`rgb:${S(g,_)}/${S(C,_)}/${S(v,_)}`}},5770:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.PAYLOAD_LIMIT=void 0,n.PAYLOAD_LIMIT=1e7},6351:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.DcsHandler=n.DcsParser=void 0;const f=c(482),S=c(8742),u=c(5770),_=[];n.DcsParser=class{constructor(){this._handlers=Object.create(null),this._active=_,this._ident=0,this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=_}registerHandler(C,v){this._handlers[C]===void 0&&(this._handlers[C]=[]);const h=this._handlers[C];return h.push(v),{dispose:()=>{const a=h.indexOf(v);a!==-1&&h.splice(a,1)}}}clearHandler(C){this._handlers[C]&&delete this._handlers[C]}setHandlerFallback(C){this._handlerFb=C}reset(){if(this._active.length)for(let C=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;C>=0;--C)this._active[C].unhook(!1);this._stack.paused=!1,this._active=_,this._ident=0}hook(C,v){if(this.reset(),this._ident=C,this._active=this._handlers[C]||_,this._active.length)for(let h=this._active.length-1;h>=0;h--)this._active[h].hook(v);else this._handlerFb(this._ident,"HOOK",v)}put(C,v,h){if(this._active.length)for(let a=this._active.length-1;a>=0;a--)this._active[a].put(C,v,h);else this._handlerFb(this._ident,"PUT",(0,f.utf32ToString)(C,v,h))}unhook(C,v=!0){if(this._active.length){let h=!1,a=this._active.length-1,s=!1;if(this._stack.paused&&(a=this._stack.loopPosition-1,h=v,s=this._stack.fallThrough,this._stack.paused=!1),!s&&h===!1){for(;a>=0&&(h=this._active[a].unhook(C),h!==!0);a--)if(h instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=a,this._stack.fallThrough=!1,h;a--}for(;a>=0;a--)if(h=this._active[a].unhook(!1),h instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=a,this._stack.fallThrough=!0,h}else this._handlerFb(this._ident,"UNHOOK",C);this._active=_,this._ident=0}};const g=new S.Params;g.addParam(0),n.DcsHandler=class{constructor(C){this._handler=C,this._data="",this._params=g,this._hitLimit=!1}hook(C){this._params=C.length>1||C.params[0]?C.clone():g,this._data="",this._hitLimit=!1}put(C,v,h){this._hitLimit||(this._data+=(0,f.utf32ToString)(C,v,h),this._data.length>u.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}unhook(C){let v=!1;if(this._hitLimit)v=!1;else if(C&&(v=this._handler(this._data,this._params),v instanceof Promise))return v.then(h=>(this._params=g,this._data="",this._hitLimit=!1,h));return this._params=g,this._data="",this._hitLimit=!1,v}}},2015:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.EscapeSequenceParser=n.VT500_TRANSITION_TABLE=n.TransitionTable=void 0;const f=c(844),S=c(8742),u=c(6242),_=c(6351);class g{constructor(a){this.table=new Uint8Array(a)}setDefault(a,s){this.table.fill(a<<4|s)}add(a,s,o,p){this.table[s<<8|a]=o<<4|p}addMany(a,s,o,p){for(let w=0;w<a.length;w++)this.table[s<<8|a[w]]=o<<4|p}}n.TransitionTable=g;const C=160;n.VT500_TRANSITION_TABLE=function(){const h=new g(4095),a=Array.apply(null,Array(256)).map((k,y)=>y),s=(k,y)=>a.slice(k,y),o=s(32,127),p=s(0,24);p.push(25),p.push.apply(p,s(28,32));const w=s(0,14);let E;for(E in h.setDefault(1,0),h.addMany(o,0,2,0),w)h.addMany([24,26,153,154],E,3,0),h.addMany(s(128,144),E,3,0),h.addMany(s(144,152),E,3,0),h.add(156,E,0,0),h.add(27,E,11,1),h.add(157,E,4,8),h.addMany([152,158,159],E,0,7),h.add(155,E,11,3),h.add(144,E,11,9);return h.addMany(p,0,3,0),h.addMany(p,1,3,1),h.add(127,1,0,1),h.addMany(p,8,0,8),h.addMany(p,3,3,3),h.add(127,3,0,3),h.addMany(p,4,3,4),h.add(127,4,0,4),h.addMany(p,6,3,6),h.addMany(p,5,3,5),h.add(127,5,0,5),h.addMany(p,2,3,2),h.add(127,2,0,2),h.add(93,1,4,8),h.addMany(o,8,5,8),h.add(127,8,5,8),h.addMany([156,27,24,26,7],8,6,0),h.addMany(s(28,32),8,0,8),h.addMany([88,94,95],1,0,7),h.addMany(o,7,0,7),h.addMany(p,7,0,7),h.add(156,7,0,0),h.add(127,7,0,7),h.add(91,1,11,3),h.addMany(s(64,127),3,7,0),h.addMany(s(48,60),3,8,4),h.addMany([60,61,62,63],3,9,4),h.addMany(s(48,60),4,8,4),h.addMany(s(64,127),4,7,0),h.addMany([60,61,62,63],4,0,6),h.addMany(s(32,64),6,0,6),h.add(127,6,0,6),h.addMany(s(64,127),6,0,0),h.addMany(s(32,48),3,9,5),h.addMany(s(32,48),5,9,5),h.addMany(s(48,64),5,0,6),h.addMany(s(64,127),5,7,0),h.addMany(s(32,48),4,9,5),h.addMany(s(32,48),1,9,2),h.addMany(s(32,48),2,9,2),h.addMany(s(48,127),2,10,0),h.addMany(s(48,80),1,10,0),h.addMany(s(81,88),1,10,0),h.addMany([89,90,92],1,10,0),h.addMany(s(96,127),1,10,0),h.add(80,1,11,9),h.addMany(p,9,0,9),h.add(127,9,0,9),h.addMany(s(28,32),9,0,9),h.addMany(s(32,48),9,9,12),h.addMany(s(48,60),9,8,10),h.addMany([60,61,62,63],9,9,10),h.addMany(p,11,0,11),h.addMany(s(32,128),11,0,11),h.addMany(s(28,32),11,0,11),h.addMany(p,10,0,10),h.add(127,10,0,10),h.addMany(s(28,32),10,0,10),h.addMany(s(48,60),10,8,10),h.addMany([60,61,62,63],10,0,11),h.addMany(s(32,48),10,9,12),h.addMany(p,12,0,12),h.add(127,12,0,12),h.addMany(s(28,32),12,0,12),h.addMany(s(32,48),12,9,12),h.addMany(s(48,64),12,0,11),h.addMany(s(64,127),12,12,13),h.addMany(s(64,127),10,12,13),h.addMany(s(64,127),9,12,13),h.addMany(p,13,13,13),h.addMany(o,13,13,13),h.add(127,13,0,13),h.addMany([27,156,24,26],13,14,0),h.add(C,0,2,0),h.add(C,8,5,8),h.add(C,6,0,6),h.add(C,11,0,11),h.add(C,13,13,13),h}();class v extends f.Disposable{constructor(a=n.VT500_TRANSITION_TABLE){super(),this._transitions=a,this._parseStack={state:0,handlers:[],handlerPos:0,transition:0,chunkPos:0},this.initialState=0,this.currentState=this.initialState,this._params=new S.Params,this._params.addParam(0),this._collect=0,this.precedingJoinState=0,this._printHandlerFb=(s,o,p)=>{},this._executeHandlerFb=s=>{},this._csiHandlerFb=(s,o)=>{},this._escHandlerFb=s=>{},this._errorHandlerFb=s=>s,this._printHandler=this._printHandlerFb,this._executeHandlers=Object.create(null),this._csiHandlers=Object.create(null),this._escHandlers=Object.create(null),this.register((0,f.toDisposable)(()=>{this._csiHandlers=Object.create(null),this._executeHandlers=Object.create(null),this._escHandlers=Object.create(null)})),this._oscParser=this.register(new u.OscParser),this._dcsParser=this.register(new _.DcsParser),this._errorHandler=this._errorHandlerFb,this.registerEscHandler({final:"\\"},()=>!0)}_identifier(a,s=[64,126]){let o=0;if(a.prefix){if(a.prefix.length>1)throw new Error("only one byte as prefix supported");if(o=a.prefix.charCodeAt(0),o&&60>o||o>63)throw new Error("prefix must be in range 0x3c .. 0x3f")}if(a.intermediates){if(a.intermediates.length>2)throw new Error("only two bytes as intermediates are supported");for(let w=0;w<a.intermediates.length;++w){const E=a.intermediates.charCodeAt(w);if(32>E||E>47)throw new Error("intermediate must be in range 0x20 .. 0x2f");o<<=8,o|=E}}if(a.final.length!==1)throw new Error("final must be a single byte");const p=a.final.charCodeAt(0);if(s[0]>p||p>s[1])throw new Error(`final must be in range ${s[0]} .. ${s[1]}`);return o<<=8,o|=p,o}identToString(a){const s=[];for(;a;)s.push(String.fromCharCode(255&a)),a>>=8;return s.reverse().join("")}setPrintHandler(a){this._printHandler=a}clearPrintHandler(){this._printHandler=this._printHandlerFb}registerEscHandler(a,s){const o=this._identifier(a,[48,126]);this._escHandlers[o]===void 0&&(this._escHandlers[o]=[]);const p=this._escHandlers[o];return p.push(s),{dispose:()=>{const w=p.indexOf(s);w!==-1&&p.splice(w,1)}}}clearEscHandler(a){this._escHandlers[this._identifier(a,[48,126])]&&delete this._escHandlers[this._identifier(a,[48,126])]}setEscHandlerFallback(a){this._escHandlerFb=a}setExecuteHandler(a,s){this._executeHandlers[a.charCodeAt(0)]=s}clearExecuteHandler(a){this._executeHandlers[a.charCodeAt(0)]&&delete this._executeHandlers[a.charCodeAt(0)]}setExecuteHandlerFallback(a){this._executeHandlerFb=a}registerCsiHandler(a,s){const o=this._identifier(a);this._csiHandlers[o]===void 0&&(this._csiHandlers[o]=[]);const p=this._csiHandlers[o];return p.push(s),{dispose:()=>{const w=p.indexOf(s);w!==-1&&p.splice(w,1)}}}clearCsiHandler(a){this._csiHandlers[this._identifier(a)]&&delete this._csiHandlers[this._identifier(a)]}setCsiHandlerFallback(a){this._csiHandlerFb=a}registerDcsHandler(a,s){return this._dcsParser.registerHandler(this._identifier(a),s)}clearDcsHandler(a){this._dcsParser.clearHandler(this._identifier(a))}setDcsHandlerFallback(a){this._dcsParser.setHandlerFallback(a)}registerOscHandler(a,s){return this._oscParser.registerHandler(a,s)}clearOscHandler(a){this._oscParser.clearHandler(a)}setOscHandlerFallback(a){this._oscParser.setHandlerFallback(a)}setErrorHandler(a){this._errorHandler=a}clearErrorHandler(){this._errorHandler=this._errorHandlerFb}reset(){this.currentState=this.initialState,this._oscParser.reset(),this._dcsParser.reset(),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingJoinState=0,this._parseStack.state!==0&&(this._parseStack.state=2,this._parseStack.handlers=[])}_preserveStack(a,s,o,p,w){this._parseStack.state=a,this._parseStack.handlers=s,this._parseStack.handlerPos=o,this._parseStack.transition=p,this._parseStack.chunkPos=w}parse(a,s,o){let p,w=0,E=0,k=0;if(this._parseStack.state)if(this._parseStack.state===2)this._parseStack.state=0,k=this._parseStack.chunkPos+1;else{if(o===void 0||this._parseStack.state===1)throw this._parseStack.state=1,new Error("improper continuation due to previous async handler, giving up parsing");const y=this._parseStack.handlers;let b=this._parseStack.handlerPos-1;switch(this._parseStack.state){case 3:if(o===!1&&b>-1){for(;b>=0&&(p=y[b](this._params),p!==!0);b--)if(p instanceof Promise)return this._parseStack.handlerPos=b,p}this._parseStack.handlers=[];break;case 4:if(o===!1&&b>-1){for(;b>=0&&(p=y[b](),p!==!0);b--)if(p instanceof Promise)return this._parseStack.handlerPos=b,p}this._parseStack.handlers=[];break;case 6:if(w=a[this._parseStack.chunkPos],p=this._dcsParser.unhook(w!==24&&w!==26,o),p)return p;w===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0;break;case 5:if(w=a[this._parseStack.chunkPos],p=this._oscParser.end(w!==24&&w!==26,o),p)return p;w===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0}this._parseStack.state=0,k=this._parseStack.chunkPos+1,this.precedingJoinState=0,this.currentState=15&this._parseStack.transition}for(let y=k;y<s;++y){switch(w=a[y],E=this._transitions.table[this.currentState<<8|(w<160?w:C)],E>>4){case 2:for(let T=y+1;;++T){if(T>=s||(w=a[T])<32||w>126&&w<C){this._printHandler(a,y,T),y=T-1;break}if(++T>=s||(w=a[T])<32||w>126&&w<C){this._printHandler(a,y,T),y=T-1;break}if(++T>=s||(w=a[T])<32||w>126&&w<C){this._printHandler(a,y,T),y=T-1;break}if(++T>=s||(w=a[T])<32||w>126&&w<C){this._printHandler(a,y,T),y=T-1;break}}break;case 3:this._executeHandlers[w]?this._executeHandlers[w]():this._executeHandlerFb(w),this.precedingJoinState=0;break;case 0:break;case 1:if(this._errorHandler({position:y,code:w,currentState:this.currentState,collect:this._collect,params:this._params,abort:!1}).abort)return;break;case 7:const b=this._csiHandlers[this._collect<<8|w];let D=b?b.length-1:-1;for(;D>=0&&(p=b[D](this._params),p!==!0);D--)if(p instanceof Promise)return this._preserveStack(3,b,D,E,y),p;D<0&&this._csiHandlerFb(this._collect<<8|w,this._params),this.precedingJoinState=0;break;case 8:do switch(w){case 59:this._params.addParam(0);break;case 58:this._params.addSubParam(-1);break;default:this._params.addDigit(w-48)}while(++y<s&&(w=a[y])>47&&w<60);y--;break;case 9:this._collect<<=8,this._collect|=w;break;case 10:const B=this._escHandlers[this._collect<<8|w];let O=B?B.length-1:-1;for(;O>=0&&(p=B[O](),p!==!0);O--)if(p instanceof Promise)return this._preserveStack(4,B,O,E,y),p;O<0&&this._escHandlerFb(this._collect<<8|w),this.precedingJoinState=0;break;case 11:this._params.reset(),this._params.addParam(0),this._collect=0;break;case 12:this._dcsParser.hook(this._collect<<8|w,this._params);break;case 13:for(let T=y+1;;++T)if(T>=s||(w=a[T])===24||w===26||w===27||w>127&&w<C){this._dcsParser.put(a,y,T),y=T-1;break}break;case 14:if(p=this._dcsParser.unhook(w!==24&&w!==26),p)return this._preserveStack(6,[],0,E,y),p;w===27&&(E|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingJoinState=0;break;case 4:this._oscParser.start();break;case 5:for(let T=y+1;;T++)if(T>=s||(w=a[T])<32||w>127&&w<C){this._oscParser.put(a,y,T),y=T-1;break}break;case 6:if(p=this._oscParser.end(w!==24&&w!==26),p)return this._preserveStack(5,[],0,E,y),p;w===27&&(E|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingJoinState=0}this.currentState=15&E}}}n.EscapeSequenceParser=v},6242:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.OscHandler=n.OscParser=void 0;const f=c(5770),S=c(482),u=[];n.OscParser=class{constructor(){this._state=0,this._active=u,this._id=-1,this._handlers=Object.create(null),this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}registerHandler(_,g){this._handlers[_]===void 0&&(this._handlers[_]=[]);const C=this._handlers[_];return C.push(g),{dispose:()=>{const v=C.indexOf(g);v!==-1&&C.splice(v,1)}}}clearHandler(_){this._handlers[_]&&delete this._handlers[_]}setHandlerFallback(_){this._handlerFb=_}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=u}reset(){if(this._state===2)for(let _=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;_>=0;--_)this._active[_].end(!1);this._stack.paused=!1,this._active=u,this._id=-1,this._state=0}_start(){if(this._active=this._handlers[this._id]||u,this._active.length)for(let _=this._active.length-1;_>=0;_--)this._active[_].start();else this._handlerFb(this._id,"START")}_put(_,g,C){if(this._active.length)for(let v=this._active.length-1;v>=0;v--)this._active[v].put(_,g,C);else this._handlerFb(this._id,"PUT",(0,S.utf32ToString)(_,g,C))}start(){this.reset(),this._state=1}put(_,g,C){if(this._state!==3){if(this._state===1)for(;g<C;){const v=_[g++];if(v===59){this._state=2,this._start();break}if(v<48||57<v)return void(this._state=3);this._id===-1&&(this._id=0),this._id=10*this._id+v-48}this._state===2&&C-g>0&&this._put(_,g,C)}}end(_,g=!0){if(this._state!==0){if(this._state!==3)if(this._state===1&&this._start(),this._active.length){let C=!1,v=this._active.length-1,h=!1;if(this._stack.paused&&(v=this._stack.loopPosition-1,C=g,h=this._stack.fallThrough,this._stack.paused=!1),!h&&C===!1){for(;v>=0&&(C=this._active[v].end(_),C!==!0);v--)if(C instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=v,this._stack.fallThrough=!1,C;v--}for(;v>=0;v--)if(C=this._active[v].end(!1),C instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=v,this._stack.fallThrough=!0,C}else this._handlerFb(this._id,"END",_);this._active=u,this._id=-1,this._state=0}}},n.OscHandler=class{constructor(_){this._handler=_,this._data="",this._hitLimit=!1}start(){this._data="",this._hitLimit=!1}put(_,g,C){this._hitLimit||(this._data+=(0,S.utf32ToString)(_,g,C),this._data.length>f.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}end(_){let g=!1;if(this._hitLimit)g=!1;else if(_&&(g=this._handler(this._data),g instanceof Promise))return g.then(C=>(this._data="",this._hitLimit=!1,C));return this._data="",this._hitLimit=!1,g}}},8742:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.Params=void 0;const c=2147483647;class f{static fromArray(u){const _=new f;if(!u.length)return _;for(let g=Array.isArray(u[0])?1:0;g<u.length;++g){const C=u[g];if(Array.isArray(C))for(let v=0;v<C.length;++v)_.addSubParam(C[v]);else _.addParam(C)}return _}constructor(u=32,_=32){if(this.maxLength=u,this.maxSubParamsLength=_,_>256)throw new Error("maxSubParamsLength must not be greater than 256");this.params=new Int32Array(u),this.length=0,this._subParams=new Int32Array(_),this._subParamsLength=0,this._subParamsIdx=new Uint16Array(u),this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}clone(){const u=new f(this.maxLength,this.maxSubParamsLength);return u.params.set(this.params),u.length=this.length,u._subParams.set(this._subParams),u._subParamsLength=this._subParamsLength,u._subParamsIdx.set(this._subParamsIdx),u._rejectDigits=this._rejectDigits,u._rejectSubDigits=this._rejectSubDigits,u._digitIsSub=this._digitIsSub,u}toArray(){const u=[];for(let _=0;_<this.length;++_){u.push(this.params[_]);const g=this._subParamsIdx[_]>>8,C=255&this._subParamsIdx[_];C-g>0&&u.push(Array.prototype.slice.call(this._subParams,g,C))}return u}reset(){this.length=0,this._subParamsLength=0,this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}addParam(u){if(this._digitIsSub=!1,this.length>=this.maxLength)this._rejectDigits=!0;else{if(u<-1)throw new Error("values lesser than -1 are not allowed");this._subParamsIdx[this.length]=this._subParamsLength<<8|this._subParamsLength,this.params[this.length++]=u>c?c:u}}addSubParam(u){if(this._digitIsSub=!0,this.length)if(this._rejectDigits||this._subParamsLength>=this.maxSubParamsLength)this._rejectSubDigits=!0;else{if(u<-1)throw new Error("values lesser than -1 are not allowed");this._subParams[this._subParamsLength++]=u>c?c:u,this._subParamsIdx[this.length-1]++}}hasSubParams(u){return(255&this._subParamsIdx[u])-(this._subParamsIdx[u]>>8)>0}getSubParams(u){const _=this._subParamsIdx[u]>>8,g=255&this._subParamsIdx[u];return g-_>0?this._subParams.subarray(_,g):null}getSubParamsAll(){const u={};for(let _=0;_<this.length;++_){const g=this._subParamsIdx[_]>>8,C=255&this._subParamsIdx[_];C-g>0&&(u[_]=this._subParams.slice(g,C))}return u}addDigit(u){let _;if(this._rejectDigits||!(_=this._digitIsSub?this._subParamsLength:this.length)||this._digitIsSub&&this._rejectSubDigits)return;const g=this._digitIsSub?this._subParams:this.params,C=g[_-1];g[_-1]=~C?Math.min(10*C+u,c):u}}n.Params=f},5741:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.AddonManager=void 0,n.AddonManager=class{constructor(){this._addons=[]}dispose(){for(let c=this._addons.length-1;c>=0;c--)this._addons[c].instance.dispose()}loadAddon(c,f){const S={instance:f,dispose:f.dispose,isDisposed:!1};this._addons.push(S),f.dispose=()=>this._wrappedAddonDispose(S),f.activate(c)}_wrappedAddonDispose(c){if(c.isDisposed)return;let f=-1;for(let S=0;S<this._addons.length;S++)if(this._addons[S]===c){f=S;break}if(f===-1)throw new Error("Could not dispose an addon that has not been loaded");c.isDisposed=!0,c.dispose.apply(c.instance),this._addons.splice(f,1)}}},8771:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.BufferApiView=void 0;const f=c(3785),S=c(511);n.BufferApiView=class{constructor(u,_){this._buffer=u,this.type=_}init(u){return this._buffer=u,this}get cursorY(){return this._buffer.y}get cursorX(){return this._buffer.x}get viewportY(){return this._buffer.ydisp}get baseY(){return this._buffer.ybase}get length(){return this._buffer.lines.length}getLine(u){const _=this._buffer.lines.get(u);if(_)return new f.BufferLineApiView(_)}getNullCell(){return new S.CellData}}},3785:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.BufferLineApiView=void 0;const f=c(511);n.BufferLineApiView=class{constructor(S){this._line=S}get isWrapped(){return this._line.isWrapped}get length(){return this._line.length}getCell(S,u){if(!(S<0||S>=this._line.length))return u?(this._line.loadCell(S,u),u):this._line.loadCell(S,new f.CellData)}translateToString(S,u,_){return this._line.translateToString(S,u,_)}}},8285:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.BufferNamespaceApi=void 0;const f=c(8771),S=c(8460),u=c(844);class _ extends u.Disposable{constructor(C){super(),this._core=C,this._onBufferChange=this.register(new S.EventEmitter),this.onBufferChange=this._onBufferChange.event,this._normal=new f.BufferApiView(this._core.buffers.normal,"normal"),this._alternate=new f.BufferApiView(this._core.buffers.alt,"alternate"),this._core.buffers.onBufferActivate(()=>this._onBufferChange.fire(this.active))}get active(){if(this._core.buffers.active===this._core.buffers.normal)return this.normal;if(this._core.buffers.active===this._core.buffers.alt)return this.alternate;throw new Error("Active buffer is neither normal nor alternate")}get normal(){return this._normal.init(this._core.buffers.normal)}get alternate(){return this._alternate.init(this._core.buffers.alt)}}n.BufferNamespaceApi=_},7975:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.ParserApi=void 0,n.ParserApi=class{constructor(c){this._core=c}registerCsiHandler(c,f){return this._core.registerCsiHandler(c,S=>f(S.toArray()))}addCsiHandler(c,f){return this.registerCsiHandler(c,f)}registerDcsHandler(c,f){return this._core.registerDcsHandler(c,(S,u)=>f(S,u.toArray()))}addDcsHandler(c,f){return this.registerDcsHandler(c,f)}registerEscHandler(c,f){return this._core.registerEscHandler(c,f)}addEscHandler(c,f){return this.registerEscHandler(c,f)}registerOscHandler(c,f){return this._core.registerOscHandler(c,f)}addOscHandler(c,f){return this.registerOscHandler(c,f)}}},7090:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.UnicodeApi=void 0,n.UnicodeApi=class{constructor(c){this._core=c}register(c){this._core.unicodeService.register(c)}get versions(){return this._core.unicodeService.versions}get activeVersion(){return this._core.unicodeService.activeVersion}set activeVersion(c){this._core.unicodeService.activeVersion=c}}},744:function(m,n,c){var f=this&&this.__decorate||function(h,a,s,o){var p,w=arguments.length,E=w<3?a:o===null?o=Object.getOwnPropertyDescriptor(a,s):o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")E=Reflect.decorate(h,a,s,o);else for(var k=h.length-1;k>=0;k--)(p=h[k])&&(E=(w<3?p(E):w>3?p(a,s,E):p(a,s))||E);return w>3&&E&&Object.defineProperty(a,s,E),E},S=this&&this.__param||function(h,a){return function(s,o){a(s,o,h)}};Object.defineProperty(n,"__esModule",{value:!0}),n.BufferService=n.MINIMUM_ROWS=n.MINIMUM_COLS=void 0;const u=c(8460),_=c(844),g=c(5295),C=c(2585);n.MINIMUM_COLS=2,n.MINIMUM_ROWS=1;let v=n.BufferService=class extends _.Disposable{get buffer(){return this.buffers.active}constructor(h){super(),this.isUserScrolling=!1,this._onResize=this.register(new u.EventEmitter),this.onResize=this._onResize.event,this._onScroll=this.register(new u.EventEmitter),this.onScroll=this._onScroll.event,this.cols=Math.max(h.rawOptions.cols||0,n.MINIMUM_COLS),this.rows=Math.max(h.rawOptions.rows||0,n.MINIMUM_ROWS),this.buffers=this.register(new g.BufferSet(h,this))}resize(h,a){this.cols=h,this.rows=a,this.buffers.resize(h,a),this._onResize.fire({cols:h,rows:a})}reset(){this.buffers.reset(),this.isUserScrolling=!1}scroll(h,a=!1){const s=this.buffer;let o;o=this._cachedBlankLine,o&&o.length===this.cols&&o.getFg(0)===h.fg&&o.getBg(0)===h.bg||(o=s.getBlankLine(h,a),this._cachedBlankLine=o),o.isWrapped=a;const p=s.ybase+s.scrollTop,w=s.ybase+s.scrollBottom;if(s.scrollTop===0){const E=s.lines.isFull;w===s.lines.length-1?E?s.lines.recycle().copyFrom(o):s.lines.push(o.clone()):s.lines.splice(w+1,0,o.clone()),E?this.isUserScrolling&&(s.ydisp=Math.max(s.ydisp-1,0)):(s.ybase++,this.isUserScrolling||s.ydisp++)}else{const E=w-p+1;s.lines.shiftElements(p+1,E-1,-1),s.lines.set(w,o.clone())}this.isUserScrolling||(s.ydisp=s.ybase),this._onScroll.fire(s.ydisp)}scrollLines(h,a,s){const o=this.buffer;if(h<0){if(o.ydisp===0)return;this.isUserScrolling=!0}else h+o.ydisp>=o.ybase&&(this.isUserScrolling=!1);const p=o.ydisp;o.ydisp=Math.max(Math.min(o.ydisp+h,o.ybase),0),p!==o.ydisp&&(a||this._onScroll.fire(o.ydisp))}};n.BufferService=v=f([S(0,C.IOptionsService)],v)},7994:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.CharsetService=void 0,n.CharsetService=class{constructor(){this.glevel=0,this._charsets=[]}reset(){this.charset=void 0,this._charsets=[],this.glevel=0}setgLevel(c){this.glevel=c,this.charset=this._charsets[c]}setgCharset(c,f){this._charsets[c]=f,this.glevel===c&&(this.charset=f)}}},1753:function(m,n,c){var f=this&&this.__decorate||function(o,p,w,E){var k,y=arguments.length,b=y<3?p:E===null?E=Object.getOwnPropertyDescriptor(p,w):E;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")b=Reflect.decorate(o,p,w,E);else for(var D=o.length-1;D>=0;D--)(k=o[D])&&(b=(y<3?k(b):y>3?k(p,w,b):k(p,w))||b);return y>3&&b&&Object.defineProperty(p,w,b),b},S=this&&this.__param||function(o,p){return function(w,E){p(w,E,o)}};Object.defineProperty(n,"__esModule",{value:!0}),n.CoreMouseService=void 0;const u=c(2585),_=c(8460),g=c(844),C={NONE:{events:0,restrict:()=>!1},X10:{events:1,restrict:o=>o.button!==4&&o.action===1&&(o.ctrl=!1,o.alt=!1,o.shift=!1,!0)},VT200:{events:19,restrict:o=>o.action!==32},DRAG:{events:23,restrict:o=>o.action!==32||o.button!==3},ANY:{events:31,restrict:o=>!0}};function v(o,p){let w=(o.ctrl?16:0)|(o.shift?4:0)|(o.alt?8:0);return o.button===4?(w|=64,w|=o.action):(w|=3&o.button,4&o.button&&(w|=64),8&o.button&&(w|=128),o.action===32?w|=32:o.action!==0||p||(w|=3)),w}const h=String.fromCharCode,a={DEFAULT:o=>{const p=[v(o,!1)+32,o.col+32,o.row+32];return p[0]>255||p[1]>255||p[2]>255?"":`\x1B[M${h(p[0])}${h(p[1])}${h(p[2])}`},SGR:o=>{const p=o.action===0&&o.button!==4?"m":"M";return`\x1B[<${v(o,!0)};${o.col};${o.row}${p}`},SGR_PIXELS:o=>{const p=o.action===0&&o.button!==4?"m":"M";return`\x1B[<${v(o,!0)};${o.x};${o.y}${p}`}};let s=n.CoreMouseService=class extends g.Disposable{constructor(o,p){super(),this._bufferService=o,this._coreService=p,this._protocols={},this._encodings={},this._activeProtocol="",this._activeEncoding="",this._lastEvent=null,this._onProtocolChange=this.register(new _.EventEmitter),this.onProtocolChange=this._onProtocolChange.event;for(const w of Object.keys(C))this.addProtocol(w,C[w]);for(const w of Object.keys(a))this.addEncoding(w,a[w]);this.reset()}addProtocol(o,p){this._protocols[o]=p}addEncoding(o,p){this._encodings[o]=p}get activeProtocol(){return this._activeProtocol}get areMouseEventsActive(){return this._protocols[this._activeProtocol].events!==0}set activeProtocol(o){if(!this._protocols[o])throw new Error(`unknown protocol "${o}"`);this._activeProtocol=o,this._onProtocolChange.fire(this._protocols[o].events)}get activeEncoding(){return this._activeEncoding}set activeEncoding(o){if(!this._encodings[o])throw new Error(`unknown encoding "${o}"`);this._activeEncoding=o}reset(){this.activeProtocol="NONE",this.activeEncoding="DEFAULT",this._lastEvent=null}triggerMouseEvent(o){if(o.col<0||o.col>=this._bufferService.cols||o.row<0||o.row>=this._bufferService.rows||o.button===4&&o.action===32||o.button===3&&o.action!==32||o.button!==4&&(o.action===2||o.action===3)||(o.col++,o.row++,o.action===32&&this._lastEvent&&this._equalEvents(this._lastEvent,o,this._activeEncoding==="SGR_PIXELS"))||!this._protocols[this._activeProtocol].restrict(o))return!1;const p=this._encodings[this._activeEncoding](o);return p&&(this._activeEncoding==="DEFAULT"?this._coreService.triggerBinaryEvent(p):this._coreService.triggerDataEvent(p,!0)),this._lastEvent=o,!0}explainEvents(o){return{down:!!(1&o),up:!!(2&o),drag:!!(4&o),move:!!(8&o),wheel:!!(16&o)}}_equalEvents(o,p,w){if(w){if(o.x!==p.x||o.y!==p.y)return!1}else if(o.col!==p.col||o.row!==p.row)return!1;return o.button===p.button&&o.action===p.action&&o.ctrl===p.ctrl&&o.alt===p.alt&&o.shift===p.shift}};n.CoreMouseService=s=f([S(0,u.IBufferService),S(1,u.ICoreService)],s)},6975:function(m,n,c){var f=this&&this.__decorate||function(s,o,p,w){var E,k=arguments.length,y=k<3?o:w===null?w=Object.getOwnPropertyDescriptor(o,p):w;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")y=Reflect.decorate(s,o,p,w);else for(var b=s.length-1;b>=0;b--)(E=s[b])&&(y=(k<3?E(y):k>3?E(o,p,y):E(o,p))||y);return k>3&&y&&Object.defineProperty(o,p,y),y},S=this&&this.__param||function(s,o){return function(p,w){o(p,w,s)}};Object.defineProperty(n,"__esModule",{value:!0}),n.CoreService=void 0;const u=c(1439),_=c(8460),g=c(844),C=c(2585),v=Object.freeze({insertMode:!1}),h=Object.freeze({applicationCursorKeys:!1,applicationKeypad:!1,bracketedPasteMode:!1,origin:!1,reverseWraparound:!1,sendFocus:!1,wraparound:!0});let a=n.CoreService=class extends g.Disposable{constructor(s,o,p){super(),this._bufferService=s,this._logService=o,this._optionsService=p,this.isCursorInitialized=!1,this.isCursorHidden=!1,this._onData=this.register(new _.EventEmitter),this.onData=this._onData.event,this._onUserInput=this.register(new _.EventEmitter),this.onUserInput=this._onUserInput.event,this._onBinary=this.register(new _.EventEmitter),this.onBinary=this._onBinary.event,this._onRequestScrollToBottom=this.register(new _.EventEmitter),this.onRequestScrollToBottom=this._onRequestScrollToBottom.event,this.modes=(0,u.clone)(v),this.decPrivateModes=(0,u.clone)(h)}reset(){this.modes=(0,u.clone)(v),this.decPrivateModes=(0,u.clone)(h)}triggerDataEvent(s,o=!1){if(this._optionsService.rawOptions.disableStdin)return;const p=this._bufferService.buffer;o&&this._optionsService.rawOptions.scrollOnUserInput&&p.ybase!==p.ydisp&&this._onRequestScrollToBottom.fire(),o&&this._onUserInput.fire(),this._logService.debug(`sending data "${s}"`,()=>s.split("").map(w=>w.charCodeAt(0))),this._onData.fire(s)}triggerBinaryEvent(s){this._optionsService.rawOptions.disableStdin||(this._logService.debug(`sending binary "${s}"`,()=>s.split("").map(o=>o.charCodeAt(0))),this._onBinary.fire(s))}};n.CoreService=a=f([S(0,C.IBufferService),S(1,C.ILogService),S(2,C.IOptionsService)],a)},9074:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.DecorationService=void 0;const f=c(8055),S=c(8460),u=c(844),_=c(6106);let g=0,C=0;class v extends u.Disposable{get decorations(){return this._decorations.values()}constructor(){super(),this._decorations=new _.SortedList(s=>s==null?void 0:s.marker.line),this._onDecorationRegistered=this.register(new S.EventEmitter),this.onDecorationRegistered=this._onDecorationRegistered.event,this._onDecorationRemoved=this.register(new S.EventEmitter),this.onDecorationRemoved=this._onDecorationRemoved.event,this.register((0,u.toDisposable)(()=>this.reset()))}registerDecoration(s){if(s.marker.isDisposed)return;const o=new h(s);if(o){const p=o.marker.onDispose(()=>o.dispose());o.onDispose(()=>{o&&(this._decorations.delete(o)&&this._onDecorationRemoved.fire(o),p.dispose())}),this._decorations.insert(o),this._onDecorationRegistered.fire(o)}return o}reset(){for(const s of this._decorations.values())s.dispose();this._decorations.clear()}*getDecorationsAtCell(s,o,p){let w=0,E=0;for(const k of this._decorations.getKeyIterator(o))w=k.options.x??0,E=w+(k.options.width??1),s>=w&&s<E&&(!p||(k.options.layer??"bottom")===p)&&(yield k)}forEachDecorationAtCell(s,o,p,w){this._decorations.forEachByKey(o,E=>{g=E.options.x??0,C=g+(E.options.width??1),s>=g&&s<C&&(!p||(E.options.layer??"bottom")===p)&&w(E)})}}n.DecorationService=v;class h extends u.Disposable{get isDisposed(){return this._isDisposed}get backgroundColorRGB(){return this._cachedBg===null&&(this.options.backgroundColor?this._cachedBg=f.css.toColor(this.options.backgroundColor):this._cachedBg=void 0),this._cachedBg}get foregroundColorRGB(){return this._cachedFg===null&&(this.options.foregroundColor?this._cachedFg=f.css.toColor(this.options.foregroundColor):this._cachedFg=void 0),this._cachedFg}constructor(s){super(),this.options=s,this.onRenderEmitter=this.register(new S.EventEmitter),this.onRender=this.onRenderEmitter.event,this._onDispose=this.register(new S.EventEmitter),this.onDispose=this._onDispose.event,this._cachedBg=null,this._cachedFg=null,this.marker=s.marker,this.options.overviewRulerOptions&&!this.options.overviewRulerOptions.position&&(this.options.overviewRulerOptions.position="full")}dispose(){this._onDispose.fire(),super.dispose()}}},4348:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.InstantiationService=n.ServiceCollection=void 0;const f=c(2585),S=c(8343);class u{constructor(...g){this._entries=new Map;for(const[C,v]of g)this.set(C,v)}set(g,C){const v=this._entries.get(g);return this._entries.set(g,C),v}forEach(g){for(const[C,v]of this._entries.entries())g(C,v)}has(g){return this._entries.has(g)}get(g){return this._entries.get(g)}}n.ServiceCollection=u,n.InstantiationService=class{constructor(){this._services=new u,this._services.set(f.IInstantiationService,this)}setService(_,g){this._services.set(_,g)}getService(_){return this._services.get(_)}createInstance(_,...g){const C=(0,S.getServiceDependencies)(_).sort((a,s)=>a.index-s.index),v=[];for(const a of C){const s=this._services.get(a.id);if(!s)throw new Error(`[createInstance] ${_.name} depends on UNKNOWN service ${a.id}.`);v.push(s)}const h=C.length>0?C[0].index:g.length;if(g.length!==h)throw new Error(`[createInstance] First service dependency of ${_.name} at position ${h+1} conflicts with ${g.length} static arguments`);return new _(...g,...v)}}},7866:function(m,n,c){var f=this&&this.__decorate||function(h,a,s,o){var p,w=arguments.length,E=w<3?a:o===null?o=Object.getOwnPropertyDescriptor(a,s):o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")E=Reflect.decorate(h,a,s,o);else for(var k=h.length-1;k>=0;k--)(p=h[k])&&(E=(w<3?p(E):w>3?p(a,s,E):p(a,s))||E);return w>3&&E&&Object.defineProperty(a,s,E),E},S=this&&this.__param||function(h,a){return function(s,o){a(s,o,h)}};Object.defineProperty(n,"__esModule",{value:!0}),n.traceCall=n.setTraceLogger=n.LogService=void 0;const u=c(844),_=c(2585),g={trace:_.LogLevelEnum.TRACE,debug:_.LogLevelEnum.DEBUG,info:_.LogLevelEnum.INFO,warn:_.LogLevelEnum.WARN,error:_.LogLevelEnum.ERROR,off:_.LogLevelEnum.OFF};let C,v=n.LogService=class extends u.Disposable{get logLevel(){return this._logLevel}constructor(h){super(),this._optionsService=h,this._logLevel=_.LogLevelEnum.OFF,this._updateLogLevel(),this.register(this._optionsService.onSpecificOptionChange("logLevel",()=>this._updateLogLevel())),C=this}_updateLogLevel(){this._logLevel=g[this._optionsService.rawOptions.logLevel]}_evalLazyOptionalParams(h){for(let a=0;a<h.length;a++)typeof h[a]=="function"&&(h[a]=h[a]())}_log(h,a,s){this._evalLazyOptionalParams(s),h.call(console,(this._optionsService.options.logger?"":"xterm.js: ")+a,...s)}trace(h,...a){var s;this._logLevel<=_.LogLevelEnum.TRACE&&this._log(((s=this._optionsService.options.logger)==null?void 0:s.trace.bind(this._optionsService.options.logger))??console.log,h,a)}debug(h,...a){var s;this._logLevel<=_.LogLevelEnum.DEBUG&&this._log(((s=this._optionsService.options.logger)==null?void 0:s.debug.bind(this._optionsService.options.logger))??console.log,h,a)}info(h,...a){var s;this._logLevel<=_.LogLevelEnum.INFO&&this._log(((s=this._optionsService.options.logger)==null?void 0:s.info.bind(this._optionsService.options.logger))??console.info,h,a)}warn(h,...a){var s;this._logLevel<=_.LogLevelEnum.WARN&&this._log(((s=this._optionsService.options.logger)==null?void 0:s.warn.bind(this._optionsService.options.logger))??console.warn,h,a)}error(h,...a){var s;this._logLevel<=_.LogLevelEnum.ERROR&&this._log(((s=this._optionsService.options.logger)==null?void 0:s.error.bind(this._optionsService.options.logger))??console.error,h,a)}};n.LogService=v=f([S(0,_.IOptionsService)],v),n.setTraceLogger=function(h){C=h},n.traceCall=function(h,a,s){if(typeof s.value!="function")throw new Error("not supported");const o=s.value;s.value=function(...p){if(C.logLevel!==_.LogLevelEnum.TRACE)return o.apply(this,p);C.trace(`GlyphRenderer#${o.name}(${p.map(E=>JSON.stringify(E)).join(", ")})`);const w=o.apply(this,p);return C.trace(`GlyphRenderer#${o.name} return`,w),w}}},7302:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.OptionsService=n.DEFAULT_OPTIONS=void 0;const f=c(8460),S=c(844),u=c(6114);n.DEFAULT_OPTIONS={cols:80,rows:24,cursorBlink:!1,cursorStyle:"block",cursorWidth:1,cursorInactiveStyle:"outline",customGlyphs:!0,drawBoldTextInBrightColors:!0,documentOverride:null,fastScrollModifier:"alt",fastScrollSensitivity:5,fontFamily:"courier-new, courier, monospace",fontSize:15,fontWeight:"normal",fontWeightBold:"bold",ignoreBracketedPasteMode:!1,lineHeight:1,letterSpacing:0,linkHandler:null,logLevel:"info",logger:null,scrollback:1e3,scrollOnUserInput:!0,scrollSensitivity:1,screenReaderMode:!1,smoothScrollDuration:0,macOptionIsMeta:!1,macOptionClickForcesSelection:!1,minimumContrastRatio:1,disableStdin:!1,allowProposedApi:!1,allowTransparency:!1,tabStopWidth:8,theme:{},rescaleOverlappingGlyphs:!1,rightClickSelectsWord:u.isMac,windowOptions:{},windowsMode:!1,windowsPty:{},wordSeparator:" ()[]{}',\"`",altClickMovesCursor:!0,convertEol:!1,termName:"xterm",cancelEvents:!1,overviewRulerWidth:0};const _=["normal","bold","100","200","300","400","500","600","700","800","900"];class g extends S.Disposable{constructor(v){super(),this._onOptionChange=this.register(new f.EventEmitter),this.onOptionChange=this._onOptionChange.event;const h={...n.DEFAULT_OPTIONS};for(const a in v)if(a in h)try{const s=v[a];h[a]=this._sanitizeAndValidateOption(a,s)}catch(s){console.error(s)}this.rawOptions=h,this.options={...h},this._setupOptions(),this.register((0,S.toDisposable)(()=>{this.rawOptions.linkHandler=null,this.rawOptions.documentOverride=null}))}onSpecificOptionChange(v,h){return this.onOptionChange(a=>{a===v&&h(this.rawOptions[v])})}onMultipleOptionChange(v,h){return this.onOptionChange(a=>{v.indexOf(a)!==-1&&h()})}_setupOptions(){const v=a=>{if(!(a in n.DEFAULT_OPTIONS))throw new Error(`No option with key "${a}"`);return this.rawOptions[a]},h=(a,s)=>{if(!(a in n.DEFAULT_OPTIONS))throw new Error(`No option with key "${a}"`);s=this._sanitizeAndValidateOption(a,s),this.rawOptions[a]!==s&&(this.rawOptions[a]=s,this._onOptionChange.fire(a))};for(const a in this.rawOptions){const s={get:v.bind(this,a),set:h.bind(this,a)};Object.defineProperty(this.options,a,s)}}_sanitizeAndValidateOption(v,h){switch(v){case"cursorStyle":if(h||(h=n.DEFAULT_OPTIONS[v]),!function(a){return a==="block"||a==="underline"||a==="bar"}(h))throw new Error(`"${h}" is not a valid value for ${v}`);break;case"wordSeparator":h||(h=n.DEFAULT_OPTIONS[v]);break;case"fontWeight":case"fontWeightBold":if(typeof h=="number"&&1<=h&&h<=1e3)break;h=_.includes(h)?h:n.DEFAULT_OPTIONS[v];break;case"cursorWidth":h=Math.floor(h);case"lineHeight":case"tabStopWidth":if(h<1)throw new Error(`${v} cannot be less than 1, value: ${h}`);break;case"minimumContrastRatio":h=Math.max(1,Math.min(21,Math.round(10*h)/10));break;case"scrollback":if((h=Math.min(h,4294967295))<0)throw new Error(`${v} cannot be less than 0, value: ${h}`);break;case"fastScrollSensitivity":case"scrollSensitivity":if(h<=0)throw new Error(`${v} cannot be less than or equal to 0, value: ${h}`);break;case"rows":case"cols":if(!h&&h!==0)throw new Error(`${v} must be numeric, value: ${h}`);break;case"windowsPty":h=h??{}}return h}}n.OptionsService=g},2660:function(m,n,c){var f=this&&this.__decorate||function(g,C,v,h){var a,s=arguments.length,o=s<3?C:h===null?h=Object.getOwnPropertyDescriptor(C,v):h;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")o=Reflect.decorate(g,C,v,h);else for(var p=g.length-1;p>=0;p--)(a=g[p])&&(o=(s<3?a(o):s>3?a(C,v,o):a(C,v))||o);return s>3&&o&&Object.defineProperty(C,v,o),o},S=this&&this.__param||function(g,C){return function(v,h){C(v,h,g)}};Object.defineProperty(n,"__esModule",{value:!0}),n.OscLinkService=void 0;const u=c(2585);let _=n.OscLinkService=class{constructor(g){this._bufferService=g,this._nextId=1,this._entriesWithId=new Map,this._dataByLinkId=new Map}registerLink(g){const C=this._bufferService.buffer;if(g.id===void 0){const p=C.addMarker(C.ybase+C.y),w={data:g,id:this._nextId++,lines:[p]};return p.onDispose(()=>this._removeMarkerFromLink(w,p)),this._dataByLinkId.set(w.id,w),w.id}const v=g,h=this._getEntryIdKey(v),a=this._entriesWithId.get(h);if(a)return this.addLineToLink(a.id,C.ybase+C.y),a.id;const s=C.addMarker(C.ybase+C.y),o={id:this._nextId++,key:this._getEntryIdKey(v),data:v,lines:[s]};return s.onDispose(()=>this._removeMarkerFromLink(o,s)),this._entriesWithId.set(o.key,o),this._dataByLinkId.set(o.id,o),o.id}addLineToLink(g,C){const v=this._dataByLinkId.get(g);if(v&&v.lines.every(h=>h.line!==C)){const h=this._bufferService.buffer.addMarker(C);v.lines.push(h),h.onDispose(()=>this._removeMarkerFromLink(v,h))}}getLinkData(g){var C;return(C=this._dataByLinkId.get(g))==null?void 0:C.data}_getEntryIdKey(g){return`${g.id};;${g.uri}`}_removeMarkerFromLink(g,C){const v=g.lines.indexOf(C);v!==-1&&(g.lines.splice(v,1),g.lines.length===0&&(g.data.id!==void 0&&this._entriesWithId.delete(g.key),this._dataByLinkId.delete(g.id)))}};n.OscLinkService=_=f([S(0,u.IBufferService)],_)},8343:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.createDecorator=n.getServiceDependencies=n.serviceRegistry=void 0;const c="di$target",f="di$dependencies";n.serviceRegistry=new Map,n.getServiceDependencies=function(S){return S[f]||[]},n.createDecorator=function(S){if(n.serviceRegistry.has(S))return n.serviceRegistry.get(S);const u=function(_,g,C){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");(function(v,h,a){h[c]===h?h[f].push({id:v,index:a}):(h[f]=[{id:v,index:a}],h[c]=h)})(u,_,C)};return u.toString=()=>S,n.serviceRegistry.set(S,u),u}},2585:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.IDecorationService=n.IUnicodeService=n.IOscLinkService=n.IOptionsService=n.ILogService=n.LogLevelEnum=n.IInstantiationService=n.ICharsetService=n.ICoreService=n.ICoreMouseService=n.IBufferService=void 0;const f=c(8343);var S;n.IBufferService=(0,f.createDecorator)("BufferService"),n.ICoreMouseService=(0,f.createDecorator)("CoreMouseService"),n.ICoreService=(0,f.createDecorator)("CoreService"),n.ICharsetService=(0,f.createDecorator)("CharsetService"),n.IInstantiationService=(0,f.createDecorator)("InstantiationService"),function(u){u[u.TRACE=0]="TRACE",u[u.DEBUG=1]="DEBUG",u[u.INFO=2]="INFO",u[u.WARN=3]="WARN",u[u.ERROR=4]="ERROR",u[u.OFF=5]="OFF"}(S||(n.LogLevelEnum=S={})),n.ILogService=(0,f.createDecorator)("LogService"),n.IOptionsService=(0,f.createDecorator)("OptionsService"),n.IOscLinkService=(0,f.createDecorator)("OscLinkService"),n.IUnicodeService=(0,f.createDecorator)("UnicodeService"),n.IDecorationService=(0,f.createDecorator)("DecorationService")},1480:(m,n,c)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.UnicodeService=void 0;const f=c(8460),S=c(225);class u{static extractShouldJoin(g){return(1&g)!=0}static extractWidth(g){return g>>1&3}static extractCharKind(g){return g>>3}static createPropertyValue(g,C,v=!1){return(16777215&g)<<3|(3&C)<<1|(v?1:0)}constructor(){this._providers=Object.create(null),this._active="",this._onChange=new f.EventEmitter,this.onChange=this._onChange.event;const g=new S.UnicodeV6;this.register(g),this._active=g.version,this._activeProvider=g}dispose(){this._onChange.dispose()}get versions(){return Object.keys(this._providers)}get activeVersion(){return this._active}set activeVersion(g){if(!this._providers[g])throw new Error(`unknown Unicode version "${g}"`);this._active=g,this._activeProvider=this._providers[g],this._onChange.fire(g)}register(g){this._providers[g.version]=g}wcwidth(g){return this._activeProvider.wcwidth(g)}getStringCellWidth(g){let C=0,v=0;const h=g.length;for(let a=0;a<h;++a){let s=g.charCodeAt(a);if(55296<=s&&s<=56319){if(++a>=h)return C+this.wcwidth(s);const w=g.charCodeAt(a);56320<=w&&w<=57343?s=1024*(s-55296)+w-56320+65536:C+=this.wcwidth(w)}const o=this.charProperties(s,v);let p=u.extractWidth(o);u.extractShouldJoin(o)&&(p-=u.extractWidth(v)),C+=p,v=o}return C}charProperties(g,C){return this._activeProvider.charProperties(g,C)}}n.UnicodeService=u}},i={};function l(m){var n=i[m];if(n!==void 0)return n.exports;var c=i[m]={exports:{}};return r[m].call(c.exports,c,c.exports,l),c.exports}var d={};return(()=>{var m=d;Object.defineProperty(m,"__esModule",{value:!0}),m.Terminal=void 0;const n=l(9042),c=l(3236),f=l(844),S=l(5741),u=l(8285),_=l(7975),g=l(7090),C=["cols","rows"];class v extends f.Disposable{constructor(a){super(),this._core=this.register(new c.Terminal(a)),this._addonManager=this.register(new S.AddonManager),this._publicOptions={...this._core.options};const s=p=>this._core.options[p],o=(p,w)=>{this._checkReadonlyOptions(p),this._core.options[p]=w};for(const p in this._core.options){const w={get:s.bind(this,p),set:o.bind(this,p)};Object.defineProperty(this._publicOptions,p,w)}}_checkReadonlyOptions(a){if(C.includes(a))throw new Error(`Option "${a}" can only be set in the constructor`)}_checkProposedApi(){if(!this._core.optionsService.rawOptions.allowProposedApi)throw new Error("You must set the allowProposedApi option to true to use proposed API")}get onBell(){return this._core.onBell}get onBinary(){return this._core.onBinary}get onCursorMove(){return this._core.onCursorMove}get onData(){return this._core.onData}get onKey(){return this._core.onKey}get onLineFeed(){return this._core.onLineFeed}get onRender(){return this._core.onRender}get onResize(){return this._core.onResize}get onScroll(){return this._core.onScroll}get onSelectionChange(){return this._core.onSelectionChange}get onTitleChange(){return this._core.onTitleChange}get onWriteParsed(){return this._core.onWriteParsed}get element(){return this._core.element}get parser(){return this._parser||(this._parser=new _.ParserApi(this._core)),this._parser}get unicode(){return this._checkProposedApi(),new g.UnicodeApi(this._core)}get textarea(){return this._core.textarea}get rows(){return this._core.rows}get cols(){return this._core.cols}get buffer(){return this._buffer||(this._buffer=this.register(new u.BufferNamespaceApi(this._core))),this._buffer}get markers(){return this._checkProposedApi(),this._core.markers}get modes(){const a=this._core.coreService.decPrivateModes;let s="none";switch(this._core.coreMouseService.activeProtocol){case"X10":s="x10";break;case"VT200":s="vt200";break;case"DRAG":s="drag";break;case"ANY":s="any"}return{applicationCursorKeysMode:a.applicationCursorKeys,applicationKeypadMode:a.applicationKeypad,bracketedPasteMode:a.bracketedPasteMode,insertMode:this._core.coreService.modes.insertMode,mouseTrackingMode:s,originMode:a.origin,reverseWraparoundMode:a.reverseWraparound,sendFocusMode:a.sendFocus,wraparoundMode:a.wraparound}}get options(){return this._publicOptions}set options(a){for(const s in a)this._publicOptions[s]=a[s]}blur(){this._core.blur()}focus(){this._core.focus()}input(a,s=!0){this._core.input(a,s)}resize(a,s){this._verifyIntegers(a,s),this._core.resize(a,s)}open(a){this._core.open(a)}attachCustomKeyEventHandler(a){this._core.attachCustomKeyEventHandler(a)}attachCustomWheelEventHandler(a){this._core.attachCustomWheelEventHandler(a)}registerLinkProvider(a){return this._core.registerLinkProvider(a)}registerCharacterJoiner(a){return this._checkProposedApi(),this._core.registerCharacterJoiner(a)}deregisterCharacterJoiner(a){this._checkProposedApi(),this._core.deregisterCharacterJoiner(a)}registerMarker(a=0){return this._verifyIntegers(a),this._core.registerMarker(a)}registerDecoration(a){return this._checkProposedApi(),this._verifyPositiveIntegers(a.x??0,a.width??0,a.height??0),this._core.registerDecoration(a)}hasSelection(){return this._core.hasSelection()}select(a,s,o){this._verifyIntegers(a,s,o),this._core.select(a,s,o)}getSelection(){return this._core.getSelection()}getSelectionPosition(){return this._core.getSelectionPosition()}clearSelection(){this._core.clearSelection()}selectAll(){this._core.selectAll()}selectLines(a,s){this._verifyIntegers(a,s),this._core.selectLines(a,s)}dispose(){super.dispose()}scrollLines(a){this._verifyIntegers(a),this._core.scrollLines(a)}scrollPages(a){this._verifyIntegers(a),this._core.scrollPages(a)}scrollToTop(){this._core.scrollToTop()}scrollToBottom(){this._core.scrollToBottom()}scrollToLine(a){this._verifyIntegers(a),this._core.scrollToLine(a)}clear(){this._core.clear()}write(a,s){this._core.write(a,s)}writeln(a,s){this._core.write(a),this._core.write(`\r
`,s)}paste(a){this._core.paste(a)}refresh(a,s){this._verifyIntegers(a,s),this._core.refresh(a,s)}reset(){this._core.reset()}clearTextureAtlas(){this._core.clearTextureAtlas()}loadAddon(a){this._addonManager.loadAddon(this,a)}static get strings(){return n}_verifyIntegers(...a){for(const s of a)if(s===1/0||isNaN(s)||s%1!=0)throw new Error("This API only accepts integers")}_verifyPositiveIntegers(...a){for(const s of a)if(s&&(s===1/0||isNaN(s)||s%1!=0||s<0))throw new Error("This API only accepts positive integers")}}m.Terminal=v})(),d})())})(Tu);var gp=Tu.exports,Bu={exports:{}};(function(e,t){(function(r,i){e.exports=i()})(self,()=>(()=>{var r={};return(()=>{var i=r;Object.defineProperty(i,"__esModule",{value:!0}),i.FitAddon=void 0,i.FitAddon=class{activate(l){this._terminal=l}dispose(){}fit(){const l=this.proposeDimensions();if(!l||!this._terminal||isNaN(l.cols)||isNaN(l.rows))return;const d=this._terminal._core;this._terminal.rows===l.rows&&this._terminal.cols===l.cols||(d._renderService.clear(),this._terminal.resize(l.cols,l.rows))}proposeDimensions(){if(!this._terminal||!this._terminal.element||!this._terminal.element.parentElement)return;const l=this._terminal._core,d=l._renderService.dimensions;if(d.css.cell.width===0||d.css.cell.height===0)return;const m=this._terminal.options.scrollback===0?0:l.viewport.scrollBarWidth,n=window.getComputedStyle(this._terminal.element.parentElement),c=parseInt(n.getPropertyValue("height")),f=Math.max(0,parseInt(n.getPropertyValue("width"))),S=window.getComputedStyle(this._terminal.element),u=c-(parseInt(S.getPropertyValue("padding-top"))+parseInt(S.getPropertyValue("padding-bottom"))),_=f-(parseInt(S.getPropertyValue("padding-right"))+parseInt(S.getPropertyValue("padding-left")))-m;return{cols:Math.max(2,Math.floor(_/d.css.cell.width)),rows:Math.max(1,Math.floor(u/d.css.cell.height))}}}})(),r})())})(Bu);var mp=Bu.exports,Ou={exports:{}};(function(e,t){(function(r,i){e.exports=i()})(self,()=>(()=>{var r={6:(m,n)=>{function c(S){try{const u=new URL(S),_=u.password&&u.username?`${u.protocol}//${u.username}:${u.password}@${u.host}`:u.username?`${u.protocol}//${u.username}@${u.host}`:`${u.protocol}//${u.host}`;return S.toLocaleLowerCase().startsWith(_.toLocaleLowerCase())}catch{return!1}}Object.defineProperty(n,"__esModule",{value:!0}),n.LinkComputer=n.WebLinkProvider=void 0,n.WebLinkProvider=class{constructor(S,u,_,g={}){this._terminal=S,this._regex=u,this._handler=_,this._options=g}provideLinks(S,u){const _=f.computeLink(S,this._regex,this._terminal,this._handler);u(this._addCallbacks(_))}_addCallbacks(S){return S.map(u=>(u.leave=this._options.leave,u.hover=(_,g)=>{if(this._options.hover){const{range:C}=u;this._options.hover(_,g,C)}},u))}};class f{static computeLink(u,_,g,C){const v=new RegExp(_.source,(_.flags||"")+"g"),[h,a]=f._getWindowedLineStrings(u-1,g),s=h.join("");let o;const p=[];for(;o=v.exec(s);){const w=o[0];if(!c(w))continue;const[E,k]=f._mapStrIdx(g,a,0,o.index),[y,b]=f._mapStrIdx(g,E,k,w.length);if(E===-1||k===-1||y===-1||b===-1)continue;const D={start:{x:k+1,y:E+1},end:{x:b,y:y+1}};p.push({range:D,text:w,activate:C})}return p}static _getWindowedLineStrings(u,_){let g,C=u,v=u,h=0,a="";const s=[];if(g=_.buffer.active.getLine(u)){const o=g.translateToString(!0);if(g.isWrapped&&o[0]!==" "){for(h=0;(g=_.buffer.active.getLine(--C))&&h<2048&&(a=g.translateToString(!0),h+=a.length,s.push(a),g.isWrapped&&a.indexOf(" ")===-1););s.reverse()}for(s.push(o),h=0;(g=_.buffer.active.getLine(++v))&&g.isWrapped&&h<2048&&(a=g.translateToString(!0),h+=a.length,s.push(a),a.indexOf(" ")===-1););}return[s,C]}static _mapStrIdx(u,_,g,C){const v=u.buffer.active,h=v.getNullCell();let a=g;for(;C;){const s=v.getLine(_);if(!s)return[-1,-1];for(let o=a;o<s.length;++o){s.getCell(o,h);const p=h.getChars();if(h.getWidth()&&(C-=p.length||1,o===s.length-1&&p==="")){const w=v.getLine(_+1);w&&w.isWrapped&&(w.getCell(0,h),h.getWidth()===2&&(C+=1))}if(C<0)return[_,o]}_++,a=0}return[_,a]}}n.LinkComputer=f}},i={};function l(m){var n=i[m];if(n!==void 0)return n.exports;var c=i[m]={exports:{}};return r[m](c,c.exports,l),c.exports}var d={};return(()=>{var m=d;Object.defineProperty(m,"__esModule",{value:!0}),m.WebLinksAddon=void 0;const n=l(6),c=/(https?|HTTPS?):[/]{2}[^\s"'!*(){}|\\\^<>`]*[^\s"':,.!?{}|\\\^~\[\]`()<>]/;function f(S,u){const _=window.open();if(_){try{_.opener=null}catch{}_.location.href=u}else console.warn("Opening link blocked as opener could not be cleared")}m.WebLinksAddon=class{constructor(S=f,u={}){this._handler=S,this._options=u}activate(S){this._terminal=S;const u=this._options,_=u.urlRegex||c;this._linkProvider=this._terminal.registerLinkProvider(new n.WebLinkProvider(this._terminal,_,this._handler,u))}dispose(){var S;(S=this._linkProvider)==null||S.dispose()}}})(),d})())})(Ou);var Sp=Ou.exports,Pu={exports:{}};(function(e,t){(function(r,i){e.exports=i()})(self,()=>(()=>{var r={345:(m,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.runAndSubscribe=n.forwardEvent=n.EventEmitter=void 0,n.EventEmitter=class{constructor(){this._listeners=[],this._disposed=!1}get event(){return this._event||(this._event=c=>(this._listeners.push(c),{dispose:()=>{if(!this._disposed){for(let f=0;f<this._listeners.length;f++)if(this._listeners[f]===c)return void this._listeners.splice(f,1)}}})),this._event}fire(c,f){const S=[];for(let u=0;u<this._listeners.length;u++)S.push(this._listeners[u]);for(let u=0;u<S.length;u++)S[u].call(void 0,c,f)}dispose(){this.clearListeners(),this._disposed=!0}clearListeners(){this._listeners&&(this._listeners.length=0)}},n.forwardEvent=function(c,f){return c(S=>f.fire(S))},n.runAndSubscribe=function(c,f){return f(void 0),c(S=>f(S))}},859:(m,n)=>{function c(f){for(const S of f)S.dispose();f.length=0}Object.defineProperty(n,"__esModule",{value:!0}),n.getDisposeArrayDisposable=n.disposeArray=n.toDisposable=n.MutableDisposable=n.Disposable=void 0,n.Disposable=class{constructor(){this._disposables=[],this._isDisposed=!1}dispose(){this._isDisposed=!0;for(const f of this._disposables)f.dispose();this._disposables.length=0}register(f){return this._disposables.push(f),f}unregister(f){const S=this._disposables.indexOf(f);S!==-1&&this._disposables.splice(S,1)}},n.MutableDisposable=class{constructor(){this._isDisposed=!1}get value(){return this._isDisposed?void 0:this._value}set value(f){var S;this._isDisposed||f===this._value||((S=this._value)==null||S.dispose(),this._value=f)}clear(){this.value=void 0}dispose(){var f;this._isDisposed=!0,(f=this._value)==null||f.dispose(),this._value=void 0}},n.toDisposable=function(f){return{dispose:f}},n.disposeArray=c,n.getDisposeArrayDisposable=function(f){return{dispose:()=>c(f)}}}},i={};function l(m){var n=i[m];if(n!==void 0)return n.exports;var c=i[m]={exports:{}};return r[m](c,c.exports,l),c.exports}var d={};return(()=>{var m=d;Object.defineProperty(m,"__esModule",{value:!0}),m.SearchAddon=void 0;const n=l(345),c=l(859),f=" ~!@#$%^&*()+`-=[]{}|\\;:\"',./<>?";class S extends c.Disposable{constructor(_){super(),this._highlightedLines=new Set,this._highlightDecorations=[],this._selectedDecoration=this.register(new c.MutableDisposable),this._linesCacheTimeoutId=0,this._linesCacheDisposables=new c.MutableDisposable,this._onDidChangeResults=this.register(new n.EventEmitter),this.onDidChangeResults=this._onDidChangeResults.event,this._highlightLimit=(_==null?void 0:_.highlightLimit)??1e3}activate(_){this._terminal=_,this.register(this._terminal.onWriteParsed(()=>this._updateMatches())),this.register(this._terminal.onResize(()=>this._updateMatches())),this.register((0,c.toDisposable)(()=>this.clearDecorations()))}_updateMatches(){var _;this._highlightTimeout&&window.clearTimeout(this._highlightTimeout),this._cachedSearchTerm&&((_=this._lastSearchOptions)!=null&&_.decorations)&&(this._highlightTimeout=setTimeout(()=>{const g=this._cachedSearchTerm;this._cachedSearchTerm=void 0,this.findPrevious(g,{...this._lastSearchOptions,incremental:!0,noScroll:!0})},200))}clearDecorations(_){this._selectedDecoration.clear(),(0,c.disposeArray)(this._highlightDecorations),this._highlightDecorations=[],this._highlightedLines.clear(),_||(this._cachedSearchTerm=void 0)}clearActiveDecoration(){this._selectedDecoration.clear()}findNext(_,g){if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");const C=!this._lastSearchOptions||this._didOptionsChange(this._lastSearchOptions,g);this._lastSearchOptions=g,g!=null&&g.decorations&&(this._cachedSearchTerm===void 0||_!==this._cachedSearchTerm||C)&&this._highlightAllMatches(_,g);const v=this._findNextAndSelect(_,g);return this._fireResults(g),this._cachedSearchTerm=_,v}_highlightAllMatches(_,g){if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");if(!_||_.length===0)return void this.clearDecorations();g=g||{},this.clearDecorations(!0);const C=[];let v,h=this._find(_,0,0,g);for(;h&&((v==null?void 0:v.row)!==h.row||(v==null?void 0:v.col)!==h.col)&&!(C.length>=this._highlightLimit);)v=h,C.push(v),h=this._find(_,v.col+v.term.length>=this._terminal.cols?v.row+1:v.row,v.col+v.term.length>=this._terminal.cols?0:v.col+1,g);for(const a of C){const s=this._createResultDecoration(a,g.decorations);s&&(this._highlightedLines.add(s.marker.line),this._highlightDecorations.push({decoration:s,match:a,dispose(){s.dispose()}}))}}_find(_,g,C,v){var s;if(!this._terminal||!_||_.length===0)return(s=this._terminal)==null||s.clearSelection(),void this.clearDecorations();if(C>this._terminal.cols)throw new Error(`Invalid col: ${C} to search in terminal of ${this._terminal.cols} cols`);let h;this._initLinesCache();const a={startRow:g,startCol:C};if(h=this._findInLine(_,a,v),!h)for(let o=g+1;o<this._terminal.buffer.active.baseY+this._terminal.rows&&(a.startRow=o,a.startCol=0,h=this._findInLine(_,a,v),!h);o++);return h}_findNextAndSelect(_,g){var o;if(!this._terminal||!_||_.length===0)return(o=this._terminal)==null||o.clearSelection(),this.clearDecorations(),!1;const C=this._terminal.getSelectionPosition();this._terminal.clearSelection();let v=0,h=0;C&&(this._cachedSearchTerm===_?(v=C.end.x,h=C.end.y):(v=C.start.x,h=C.start.y)),this._initLinesCache();const a={startRow:h,startCol:v};let s=this._findInLine(_,a,g);if(!s)for(let p=h+1;p<this._terminal.buffer.active.baseY+this._terminal.rows&&(a.startRow=p,a.startCol=0,s=this._findInLine(_,a,g),!s);p++);if(!s&&h!==0)for(let p=0;p<h&&(a.startRow=p,a.startCol=0,s=this._findInLine(_,a,g),!s);p++);return!s&&C&&(a.startRow=C.start.y,a.startCol=0,s=this._findInLine(_,a,g)),this._selectResult(s,g==null?void 0:g.decorations,g==null?void 0:g.noScroll)}findPrevious(_,g){if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");const C=!this._lastSearchOptions||this._didOptionsChange(this._lastSearchOptions,g);this._lastSearchOptions=g,g!=null&&g.decorations&&(this._cachedSearchTerm===void 0||_!==this._cachedSearchTerm||C)&&this._highlightAllMatches(_,g);const v=this._findPreviousAndSelect(_,g);return this._fireResults(g),this._cachedSearchTerm=_,v}_didOptionsChange(_,g){return!!g&&(_.caseSensitive!==g.caseSensitive||_.regex!==g.regex||_.wholeWord!==g.wholeWord)}_fireResults(_){if(_!=null&&_.decorations){let g=-1;if(this._selectedDecoration.value){const C=this._selectedDecoration.value.match;for(let v=0;v<this._highlightDecorations.length;v++){const h=this._highlightDecorations[v].match;if(h.row===C.row&&h.col===C.col&&h.size===C.size){g=v;break}}}this._onDidChangeResults.fire({resultIndex:g,resultCount:this._highlightDecorations.length})}}_findPreviousAndSelect(_,g){var p;if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");if(!this._terminal||!_||_.length===0)return(p=this._terminal)==null||p.clearSelection(),this.clearDecorations(),!1;const C=this._terminal.getSelectionPosition();this._terminal.clearSelection();let v=this._terminal.buffer.active.baseY+this._terminal.rows-1,h=this._terminal.cols;const a=!0;this._initLinesCache();const s={startRow:v,startCol:h};let o;if(C&&(s.startRow=v=C.start.y,s.startCol=h=C.start.x,this._cachedSearchTerm!==_&&(o=this._findInLine(_,s,g,!1),o||(s.startRow=v=C.end.y,s.startCol=h=C.end.x))),o||(o=this._findInLine(_,s,g,a)),!o){s.startCol=Math.max(s.startCol,this._terminal.cols);for(let w=v-1;w>=0&&(s.startRow=w,o=this._findInLine(_,s,g,a),!o);w--);}if(!o&&v!==this._terminal.buffer.active.baseY+this._terminal.rows-1)for(let w=this._terminal.buffer.active.baseY+this._terminal.rows-1;w>=v&&(s.startRow=w,o=this._findInLine(_,s,g,a),!o);w--);return this._selectResult(o,g==null?void 0:g.decorations,g==null?void 0:g.noScroll)}_initLinesCache(){const _=this._terminal;this._linesCache||(this._linesCache=new Array(_.buffer.active.length),this._linesCacheDisposables.value=(0,c.getDisposeArrayDisposable)([_.onLineFeed(()=>this._destroyLinesCache()),_.onCursorMove(()=>this._destroyLinesCache()),_.onResize(()=>this._destroyLinesCache())])),window.clearTimeout(this._linesCacheTimeoutId),this._linesCacheTimeoutId=window.setTimeout(()=>this._destroyLinesCache(),15e3)}_destroyLinesCache(){this._linesCache=void 0,this._linesCacheDisposables.clear(),this._linesCacheTimeoutId&&(window.clearTimeout(this._linesCacheTimeoutId),this._linesCacheTimeoutId=0)}_isWholeWord(_,g,C){return(_===0||f.includes(g[_-1]))&&(_+C.length===g.length||f.includes(g[_+C.length]))}_findInLine(_,g,C={},v=!1){var B;const h=this._terminal,a=g.startRow,s=g.startCol,o=h.buffer.active.getLine(a);if(o!=null&&o.isWrapped)return v?void(g.startCol+=h.cols):(g.startRow--,g.startCol+=h.cols,this._findInLine(_,g,C));let p=(B=this._linesCache)==null?void 0:B[a];p||(p=this._translateBufferLineToStringWithWrap(a,!0),this._linesCache&&(this._linesCache[a]=p));const[w,E]=p,k=this._bufferColsToStringOffset(a,s),y=C.caseSensitive?_:_.toLowerCase(),b=C.caseSensitive?w:w.toLowerCase();let D=-1;if(C.regex){const O=RegExp(y,"g");let T;if(v)for(;T=O.exec(b.slice(0,k));)D=O.lastIndex-T[0].length,_=T[0],O.lastIndex-=_.length-1;else T=O.exec(b.slice(k)),T&&T[0].length>0&&(D=k+(O.lastIndex-T[0].length),_=T[0])}else v?k-y.length>=0&&(D=b.lastIndexOf(y,k-y.length)):D=b.indexOf(y,k);if(D>=0){if(C.wholeWord&&!this._isWholeWord(D,b,_))return;let O=0;for(;O<E.length-1&&D>=E[O+1];)O++;let T=O;for(;T<E.length-1&&D+_.length>=E[T+1];)T++;const H=D-E[O],j=D+_.length-E[T],$=this._stringLengthToBufferSize(a+O,H);return{term:_,col:$,row:a+O,size:this._stringLengthToBufferSize(a+T,j)-$+h.cols*(T-O)}}}_stringLengthToBufferSize(_,g){const C=this._terminal.buffer.active.getLine(_);if(!C)return 0;for(let v=0;v<g;v++){const h=C.getCell(v);if(!h)break;const a=h.getChars();a.length>1&&(g-=a.length-1);const s=C.getCell(v+1);s&&s.getWidth()===0&&g++}return g}_bufferColsToStringOffset(_,g){const C=this._terminal;let v=_,h=0,a=C.buffer.active.getLine(v);for(;g>0&&a;){for(let s=0;s<g&&s<C.cols;s++){const o=a.getCell(s);if(!o)break;o.getWidth()&&(h+=o.getCode()===0?1:o.getChars().length)}if(v++,a=C.buffer.active.getLine(v),a&&!a.isWrapped)break;g-=C.cols}return h}_translateBufferLineToStringWithWrap(_,g){var s;const C=this._terminal,v=[],h=[0];let a=C.buffer.active.getLine(_);for(;a;){const o=C.buffer.active.getLine(_+1),p=!!o&&o.isWrapped;let w=a.translateToString(!p&&g);if(p&&o){const E=a.getCell(a.length-1);E&&E.getCode()===0&&E.getWidth()===1&&((s=o.getCell(0))==null?void 0:s.getWidth())===2&&(w=w.slice(0,-1))}if(v.push(w),!p)break;h.push(h[h.length-1]+w.length),_++,a=o}return[v.join(""),h]}_selectResult(_,g,C){const v=this._terminal;if(this._selectedDecoration.clear(),!_)return v.clearSelection(),!1;if(v.select(_.col,_.row,_.size),g){const h=v.registerMarker(-v.buffer.active.baseY-v.buffer.active.cursorY+_.row);if(h){const a=v.registerDecoration({marker:h,x:_.col,width:_.size,backgroundColor:g.activeMatchBackground,layer:"top",overviewRulerOptions:{color:g.activeMatchColorOverviewRuler}});if(a){const s=[];s.push(h),s.push(a.onRender(o=>this._applyStyles(o,g.activeMatchBorder,!0))),s.push(a.onDispose(()=>(0,c.disposeArray)(s))),this._selectedDecoration.value={decoration:a,match:_,dispose(){a.dispose()}}}}}if(!C&&(_.row>=v.buffer.active.viewportY+v.rows||_.row<v.buffer.active.viewportY)){let h=_.row-v.buffer.active.viewportY;h-=Math.floor(v.rows/2),v.scrollLines(h)}return!0}_applyStyles(_,g,C){_.classList.contains("xterm-find-result-decoration")||(_.classList.add("xterm-find-result-decoration"),g&&(_.style.outline=`1px solid ${g}`)),C&&_.classList.add("xterm-find-active-result-decoration")}_createResultDecoration(_,g){const C=this._terminal,v=C.registerMarker(-C.buffer.active.baseY-C.buffer.active.cursorY+_.row);if(!v)return;const h=C.registerDecoration({marker:v,x:_.col,width:_.size,backgroundColor:g.matchBackground,overviewRulerOptions:this._highlightedLines.has(v.line)?void 0:{color:g.matchOverviewRuler,position:"center"}});if(h){const a=[];a.push(v),a.push(h.onRender(s=>this._applyStyles(s,g.matchBorder,!1))),a.push(h.onDispose(()=>(0,c.disposeArray)(a)))}return h}}m.SearchAddon=S})(),d})())})(Pu);var yp=Pu.exports;function wp({terminalId:e,socket:t,programId:r,programName:i,status:l,onStop:d,onRestart:m}){const n=pe.useRef(null),c=pe.useRef(null),f=pe.useRef(null),[S,u]=pe.useState(!1),[_,g]=pe.useState(!1),[C,v]=pe.useState(""),[h,a]=pe.useState(!1);pe.useEffect(()=>{if(!n.current||!t)return;c.current&&c.current.dispose();const w=new gp.Terminal({theme:{background:"#0d1117",foreground:"#f0f6fc",cursor:"#58a6ff",cursorAccent:"#0d1117",selection:"#58a6ff40",black:"#484f58",red:"#ff7b72",green:"#3fb950",yellow:"#d29922",blue:"#58a6ff",magenta:"#bc8cff",cyan:"#39c5cf",white:"#b1bac4",brightBlack:"#6e7681",brightRed:"#ffa198",brightGreen:"#56d364",brightYellow:"#e3b341",brightBlue:"#79c0ff",brightMagenta:"#d2a8ff",brightCyan:"#56d4dd",brightWhite:"#f0f6fc"},fontFamily:"JetBrains Mono, Fira Code, Monaco, Consolas, monospace",fontSize:13,lineHeight:1.2,cursorBlink:!0,cursorStyle:"block",scrollback:5e4,tabStopWidth:4,allowTransparency:!1,convertEol:!0,rows:30,cols:120,scrollOnUserInput:!0,fastScrollModifier:"alt",fastScrollSensitivity:5,scrollSensitivity:1}),E=new mp.FitAddon,k=new Sp.WebLinksAddon,y=new yp.SearchAddon;w.loadAddon(E),w.loadAddon(k),w.loadAddon(y),w.open(n.current),c.current=w,f.current=E,setTimeout(()=>{E.fit()},100),w.write(`\x1B[36m╔══════════════════════════════════════════════════════════════╗\x1B[0m\r
`),w.write(`\x1B[36m║                 🚀 TERMINAL DASHBOARD                        ║\x1B[0m\r
`),w.write(`\x1B[36m╚══════════════════════════════════════════════════════════════╝\x1B[0m\r
`),w.write(`\x1B[32m✅ Connected to: ${i||"Terminal"}\x1B[0m\r
`),w.write(`\x1B[33m📋 Terminal ID: ${e.slice(0,8)}\x1B[0m\r
`),w.write(`\x1B[34m🔌 Status: ${l||"Ready"}\x1B[0m\r
`),w.write(`\x1B[90m─────────────────────────────────────────────────────────────\x1B[0m\r
`),l!=="running"&&w.write(`\x1B[33m⚠️  Program not running. Use the Start button to begin.\x1B[0m\r
`),w.write(`\r
`),w.onData(T=>{t.emit("terminal-input",{terminalId:e,input:T})}),w.onResize(({cols:T,rows:H})=>{t.emit("terminal-resize",{terminalId:e,cols:T,rows:H})});const b=T=>{T.terminalId===e&&(w.write(T.data),v(H=>H+T.data))},D=T=>{T.terminalId===e&&w.write(`\r
\x1B[31m[Process exited with code ${T.exitCode}]\x1B[0m\r
`)};t.on("terminal-data",b),t.on("terminal-exit",D);const B=()=>{E&&c.current&&setTimeout(()=>{E.fit()},50)},O=new ResizeObserver(()=>{B()});return n.current&&O.observe(n.current),window.addEventListener("resize",B),()=>{t.off("terminal-data",b),t.off("terminal-exit",D),window.removeEventListener("resize",B),O.disconnect(),w&&w.dispose()}},[e,t]);const s=()=>{u(!S),setTimeout(()=>{f.current&&f.current.fit()},100)},o=()=>{if(c.current){const w=c.current.getSelection();if(w)navigator.clipboard.writeText(w);else{const E=c.current.buffer.active;let k="";for(let y=0;y<E.length;y++){const b=E.getLine(y);b&&(k+=b.translateToString(!0)+`
`)}navigator.clipboard.writeText(k)}}},p=()=>{const w=new Blob([C],{type:"text/plain"}),E=URL.createObjectURL(w),k=document.createElement("a");k.href=E,k.download=`terminal-${e}-${new Date().toISOString().slice(0,19)}.log`,document.body.appendChild(k),k.click(),document.body.removeChild(k),URL.revokeObjectURL(E)};return I.jsxs("div",{className:`terminal-container ${S?"fixed inset-0 z-50":""}`,children:[I.jsxs("div",{className:"terminal-header",children:[I.jsxs("div",{className:"flex items-center gap-3",children:[I.jsxs("div",{className:"flex items-center gap-2",children:[I.jsx("div",{className:`w-3 h-3 rounded-full ${l==="running"?"bg-green-500":"bg-red-500"}`}),I.jsx("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),I.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full"})]}),I.jsx("span",{className:"text-sm font-medium",children:i||`Terminal ${e.slice(0,8)}`}),I.jsx("span",{className:`text-xs px-2 py-1 rounded ${l==="running"?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400"}`,children:l||"unknown"})]}),I.jsxs("div",{className:"flex items-center gap-2",children:[l==="running"?I.jsx("button",{onClick:()=>d&&d(e),className:"p-2 hover:bg-red-500/20 rounded text-red-400 hover:text-red-300 transition-colors",title:"Stop Program",children:I.jsx(jl,{className:"w-4 h-4"})}):I.jsx("button",{onClick:()=>m&&m(r),className:"p-2 hover:bg-green-500/20 rounded text-green-400 hover:text-green-300 transition-colors",title:"Restart Program",children:I.jsx(Ru,{className:"w-4 h-4"})}),I.jsx("button",{onClick:()=>{c.current&&(c.current.clear(),v(""))},className:"p-2 hover:bg-blue-500/20 rounded text-blue-400 hover:text-blue-300 transition-colors",title:"Clear Terminal",children:I.jsx(fp,{className:"w-4 h-4"})}),I.jsx("div",{className:"w-px h-6 bg-terminal-border mx-1"}),I.jsx("button",{onClick:()=>g(!_),className:"p-2 hover:bg-terminal-bg rounded text-terminal-muted hover:text-terminal-text transition-colors",title:"Search",children:I.jsx(tc,{className:"w-4 h-4"})}),I.jsx("button",{onClick:o,className:"p-2 hover:bg-terminal-bg rounded text-terminal-muted hover:text-terminal-text transition-colors",title:"Copy content",children:I.jsx(lp,{className:"w-4 h-4"})}),I.jsx("button",{onClick:p,className:"p-2 hover:bg-terminal-bg rounded text-terminal-muted hover:text-terminal-text transition-colors",title:"Download log",children:I.jsx(ap,{className:"w-4 h-4"})}),I.jsx("button",{onClick:s,className:"p-2 hover:bg-terminal-bg rounded text-terminal-muted hover:text-terminal-text transition-colors",title:S?"Exit fullscreen":"Fullscreen",children:S?I.jsx(up,{className:"w-4 h-4"}):I.jsx(hp,{className:"w-4 h-4"})})]})]}),_&&I.jsx("div",{className:"bg-terminal-surface border-b border-terminal-border p-3",children:I.jsxs("div",{className:"flex items-center gap-2",children:[I.jsx(tc,{className:"w-4 h-4 text-terminal-muted"}),I.jsx("input",{type:"text",placeholder:"Search in terminal...",className:"flex-1 bg-terminal-bg border border-terminal-border rounded px-3 py-1 text-sm focus:outline-none focus:border-blue-500",onKeyDown:w=>{w.key==="Enter"&&c.current&&console.log("Search:",w.target.value)}})]})}),I.jsx("div",{ref:n,className:`terminal-content ${S?"h-screen":""}`,style:{height:S?"calc(100vh - 120px)":"calc(100vh - 300px)",minHeight:"400px"}})]})}function Cp({view:e,setView:t,terminalsCount:r,runningCount:i}){return I.jsx("header",{className:"bg-terminal-surface border-b border-terminal-border",children:I.jsx("div",{className:"container mx-auto px-4 py-4",children:I.jsxs("div",{className:"flex items-center justify-between",children:[I.jsx("div",{className:"flex items-center gap-4",children:I.jsxs("div",{className:"flex items-center gap-3",children:[I.jsx("div",{className:"p-2 bg-blue-500/20 rounded-lg",children:I.jsx(dp,{className:"w-6 h-6 text-blue-400"})}),I.jsxs("div",{children:[I.jsx("h1",{className:"text-xl font-bold",children:"Terminal Dashboard"}),I.jsx("p",{className:"text-sm text-terminal-muted",children:"Testnet Program Manager"})]})]})}),I.jsxs("div",{className:"flex items-center gap-4",children:[I.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[I.jsxs("div",{className:"flex items-center gap-1",children:[I.jsx("div",{className:"status-dot status-running"}),I.jsx("span",{className:"text-terminal-muted",children:"Running:"}),I.jsx("span",{className:"text-green-400 font-medium",children:i})]}),I.jsx("div",{className:"w-px h-4 bg-terminal-border"}),I.jsxs("div",{className:"flex items-center gap-1",children:[I.jsx(Ws,{className:"w-4 h-4 text-terminal-muted"}),I.jsx("span",{className:"text-terminal-muted",children:"Terminals:"}),I.jsx("span",{className:"text-blue-400 font-medium",children:r})]})]}),I.jsxs("div",{className:"flex bg-terminal-bg rounded-lg p-1",children:[I.jsxs("button",{onClick:()=>t("dashboard"),className:`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all ${e==="dashboard"?"bg-blue-500/20 text-blue-400 border border-blue-500/30":"text-terminal-muted hover:text-terminal-text hover:bg-terminal-surface"}`,children:[I.jsx(cp,{className:"w-4 h-4"}),"Dashboard"]}),I.jsxs("button",{onClick:()=>t("terminal"),disabled:r===0,className:`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all ${e==="terminal"?"bg-blue-500/20 text-blue-400 border border-blue-500/30":r===0?"text-terminal-muted/50 cursor-not-allowed":"text-terminal-muted hover:text-terminal-text hover:bg-terminal-surface"}`,children:[I.jsx(Ws,{className:"w-4 h-4"}),"Terminals",r>0&&I.jsx("span",{className:"bg-blue-500/30 text-blue-400 text-xs px-1.5 py-0.5 rounded-full",children:r})]})]})]})]})})})}function Ep(){var v,h,a;const[e,t]=pe.useState(null),[r,i]=pe.useState([]),[l,d]=pe.useState([]),[m,n]=pe.useState(null),[c,f]=pe.useState("dashboard"),[S,u]=pe.useState(!0);pe.useEffect(()=>{const s=gs("http://localhost:3001");return t(s),fetch("/api/programs").then(o=>o.json()).then(o=>{i(o),u(!1)}).catch(o=>{console.error("Failed to fetch programs:",o),u(!1)}),s.on("connect",()=>{console.log("🔌 Connected to server")}),s.on("active-terminals",o=>{d(o)}),s.on("terminal-data",o=>{d(p=>p.map(w=>w.id===o.terminalId?{...w,lastActivity:o.timestamp}:w))}),s.on("terminal-exit",o=>{d(p=>p.map(w=>w.id===o.terminalId?{...w,status:"stopped",exitCode:o.exitCode}:w))}),s.on("disconnect",()=>{console.log("🔌 Disconnected from server")}),()=>{s.close()}},[]);const _=async s=>{try{const p=await(await fetch(`/api/programs/${s}/start`,{method:"POST"})).json();if(p.success){const w={id:p.terminalId,programId:p.programId,programName:p.programName,status:"running",startTime:new Date().toISOString(),pid:p.pid};d(E=>[...E,w]),n(p.terminalId),f("terminal")}}catch(o){console.error("Failed to start program:",o)}},g=async s=>{try{await fetch(`/api/terminals/${s}/stop`,{method:"POST"}),d(o=>o.map(p=>p.id===s?{...p,status:"stopped"}:p))}catch(o){console.error("Failed to stop terminal:",o)}},C=s=>{if(d(o=>o.filter(p=>p.id!==s)),m===s){const o=l.filter(p=>p.id!==s);n(o.length>0?o[0].id:null),o.length===0&&f("dashboard")}};return S?I.jsx("div",{className:"min-h-screen bg-terminal-bg flex items-center justify-center",children:I.jsxs("div",{className:"text-center",children:[I.jsx("div",{className:"loading-spinner mx-auto mb-4"}),I.jsx("p",{className:"text-terminal-muted",children:"Loading Terminal Dashboard..."})]})}):I.jsxs("div",{className:"min-h-screen bg-terminal-bg text-terminal-text",children:[I.jsx(Cp,{view:c,setView:f,terminalsCount:l.length,runningCount:l.filter(s=>s.status==="running").length}),I.jsx("main",{className:"container mx-auto px-4 py-6",children:c==="dashboard"?I.jsxs("div",{children:[I.jsxs("div",{className:"mb-8",children:[I.jsxs("h1",{className:"text-3xl font-bold mb-2 flex items-center gap-3",children:[I.jsx(Ws,{className:"text-blue-400"}),"Terminal Dashboard"]}),I.jsx("p",{className:"text-terminal-muted",children:"Manage and monitor your testnet programs with real-time terminal access"})]}),I.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8",children:[I.jsx("div",{className:"bg-terminal-surface border border-terminal-border rounded-lg p-4",children:I.jsxs("div",{className:"flex items-center gap-3",children:[I.jsx("div",{className:"p-2 bg-blue-500/20 rounded-lg",children:I.jsx(Ws,{className:"w-5 h-5 text-blue-400"})}),I.jsxs("div",{children:[I.jsx("p",{className:"text-sm text-terminal-muted",children:"Active Terminals"}),I.jsx("p",{className:"text-2xl font-bold",children:l.length})]})]})}),I.jsx("div",{className:"bg-terminal-surface border border-terminal-border rounded-lg p-4",children:I.jsxs("div",{className:"flex items-center gap-3",children:[I.jsx("div",{className:"p-2 bg-green-500/20 rounded-lg",children:I.jsx(op,{className:"w-5 h-5 text-green-400"})}),I.jsxs("div",{children:[I.jsx("p",{className:"text-sm text-terminal-muted",children:"Running Programs"}),I.jsx("p",{className:"text-2xl font-bold text-green-400",children:l.filter(s=>s.status==="running").length})]})]})}),I.jsx("div",{className:"bg-terminal-surface border border-terminal-border rounded-lg p-4",children:I.jsxs("div",{className:"flex items-center gap-3",children:[I.jsx("div",{className:"p-2 bg-purple-500/20 rounded-lg",children:I.jsx(Du,{className:"w-5 h-5 text-purple-400"})}),I.jsxs("div",{children:[I.jsx("p",{className:"text-sm text-terminal-muted",children:"Available Programs"}),I.jsx("p",{className:"text-2xl font-bold text-purple-400",children:r.length})]})]})})]}),I.jsx(pp,{programs:r,terminals:l,onStartProgram:_,onStopProgram:g,onViewTerminal:s=>{n(s),f("terminal")}})]}):I.jsxs("div",{className:"space-y-4",children:[I.jsx(vp,{terminals:l,activeTerminal:m,onSelectTerminal:n,onCloseTerminal:C,onStopTerminal:g}),m&&I.jsx(wp,{terminalId:m,socket:e,programId:(v=l.find(s=>s.id===m))==null?void 0:v.programId,programName:(h=l.find(s=>s.id===m))==null?void 0:h.programName,status:(a=l.find(s=>s.id===m))==null?void 0:a.status,onStop:g,onRestart:_})]})})]})}zn.createRoot(document.getElementById("root")).render(I.jsx(Ju.StrictMode,{children:I.jsx(Ep,{})}));
