# 🚀 Terminal Dashboard - Multi-Program Console Manager

**Terminal Dashboard** adalah sistem manajemen terminal berbasis web yang memungkinkan Anda menjalankan banyak program bot secara bersamaan melalui antarmuka web yang modern dan interaktif.

![Terminal Dashboard](https://img.shields.io/badge/Status-Ready-brightgreen)
![Programs](https://img.shields.io/badge/Programs-20+-blue)
![Platform](https://img.shields.io/badge/Platform-Web-orange)

## ✨ Fitur Utama

### 🎯 **Core Features**
- **🌐 Web-based Terminal Interface** - Akses terminal melalui browser
- **⚡ Real-time Communication** - WebSocket untuk update instan
- **🔄 Multiple Concurrent Programs** - Jalankan banyak bot bersamaan
- **🎮 Interactive Management** - Kontrol program dengan mudah
- **📊 Resource Monitoring** - Monitor penggunaan resource
- **📱 Responsive Design** - Bekerja di desktop, tablet, mobile

### 🤖 **Available Bot Programs (20+)**
- **🌐 0G Network Bot** - Automated 0G Network transactions
- **⚡ Pharos Protocol Bot** - Pharos protocol automation
- **🤖 Maitrix Auto Bot** - Maitrix automation with blessed UI
- **🌉 T1 Auto Bridge** - T1 bridge automation tool
- **📈 Rise Protocol Bot** - Rise protocol automation
- **🎨 INFTS Auto Bot** - Intelligent NFTs on Sui testnet
- **🔗 Union Auto Bot** - Union testnet automation
- **🚀 Huddle Deploy Bot** - ERC-20 token deployment
- **💹 TradeGPT Auto Bot** - TradeGPT Finance automation
- **🎯 Enso Auto Bot** - Enso protocol automation
- **🪁 Kite AI Auto Bot** - Galkite AI interaction
- **🌟 Merak Bot** - Merak application on Sui
- **💰 CoinShift Auto Bot** - CoinShift daily tasks
- **🔥 Bytenova Auto Bot** - Bytenova daily tasks
- **🔄 Euclid Bot** - Euclid testnet swaps
- **🔐 INCO Auto Bot** - INCO protocol automation
- **⭐ Aster Auto Bot** - Aster daily tasks
- **🎮 R2 Testnet Auto Bot** - R2 testnet automation
- **💫 Somnia Exchange Bot** - Somnia exchange automation
- **🏛️ Rome EVM Deployer** - EVM contract deployment

## 🏗️ Arsitektur Sistem

```
┌─────────────────┐    WebSocket    ┌─────────────────┐    node-pty    ┌─────────────────┐
│   React Frontend │ ◄──────────────► │  Node.js Server │ ◄─────────────► │   Bot Programs  │
│                 │                 │                 │                │                 │
│ • xterm.js      │                 │ • Express       │                │ • 20+ Programs  │
│ • Socket.io     │                 │ • Socket.io     │                │ • Multi-chain   │
│ • Tailwind CSS  │                 │ • node-pty      │                │ • Various Tasks │
│ • Lucide Icons  │                 │ • fs-extra      │                │ • Automation    │
└─────────────────┘                 └─────────────────┘                └─────────────────┘
```

## 🚀 Quick Start

### **Metode 1: Menggunakan Script Otomatis (Recommended)**

```bash
# 1. Jalankan script utama
chmod +x run-terminal-dashboard.sh
./run-terminal-dashboard.sh

# 2. Pilih menu:
# - Install dependencies (1)
# - Clean up system (2)  
# - Start dashboard (3)

# 3. Akses dashboard
# http://localhost:3001
```

### **Metode 2: Manual Setup**

```bash
# 1. Install semua dependencies
chmod +x install-all.sh
./install-all.sh

# 2. Bersihkan sistem
chmod +x cleanup.sh
./cleanup.sh

# 3. Start Terminal Dashboard
cd terminal-dashboard
chmod +x start.sh stop.sh
./start.sh

# 4. Akses dashboard
# http://localhost:3001
```

## 📋 Persyaratan Sistem

- **Node.js** v16+ 
- **npm** v7+
- **Git** (untuk cloning)
- **Browser** modern (Chrome, Firefox, Safari, Edge)
- **RAM** minimal 4GB (recommended 8GB+)
- **Storage** minimal 5GB free space

## 🎮 Cara Penggunaan

### **1. Akses Dashboard**
- Buka browser dan kunjungi `http://localhost:3001`
- Dashboard akan menampilkan semua program yang tersedia

### **2. Menjalankan Program**
- Klik pada card program yang ingin dijalankan
- Terminal akan terbuka secara real-time
- Program akan berjalan di session terpisah

### **3. Mengelola Multiple Programs**
- Gunakan tab untuk switch antar program
- Setiap program berjalan independen
- Monitor resource usage di dashboard

### **4. Kontrol Program**
- **Start**: Klik program card
- **Stop**: Gunakan tombol stop di terminal
- **Input**: Ketik langsung di terminal
- **Copy Output**: Select dan copy text

## 📁 Struktur Proyek

```
testnet/
├── terminal-dashboard/          # Main dashboard system
│   ├── backend/                # Node.js server
│   ├── frontend/               # React frontend
│   ├── start.sh               # Start script
│   └── stop.sh                # Stop script
├── 0g/                        # 0G Network Bot
├── Pharos/                    # Pharos Protocol Bot
├── Maitrix/                   # Maitrix Auto Bot
├── T1/                        # T1 Auto Bridge
├── Rise/                      # Rise Protocol Bot
├── INFTS/                     # INFTS Auto Bot
├── Union/                     # Union Auto Bot
├── Huddle/                    # Huddle Deploy Bot
├── TradeGPT/                  # TradeGPT Auto Bot
├── Enso/                      # Enso Auto Bot
├── kite/                      # Kite AI Auto Bot
├── merak/                     # Merak Bot
├── Shift/                     # CoinShift Auto Bot
├── Byte/                      # Bytenova Auto Bot
├── Euclid-Bot/                # Euclid Bot
├── Inco/                      # INCO Auto Bot
├── AsterAutoBot-NTE/          # Aster Auto Bot
├── R2nte/                     # R2 Testnet Auto Bot
├── SomniaExchangeBot-NTE/     # Somnia Exchange Bot
├── rome/                      # Rome EVM Deployer
├── run-terminal-dashboard.sh  # Main launcher script
├── install-all.sh            # Dependencies installer
├── cleanup.sh                # System cleanup
└── README.md                 # This file
```

## 🔧 Konfigurasi Program

Setiap program bot memiliki konfigurasi sendiri:

### **Wallet Configuration**
- Buat file `wallet.json` atau `.env` di setiap direktori program
- Isi dengan private keys dan konfigurasi yang diperlukan
- Lihat README.md di setiap direktori untuk detail

### **Proxy Configuration**
- Buat file `proxy.txt` jika diperlukan
- Format: `ip:port:username:password`
- Atau `ip:port` untuk proxy tanpa auth

## 🛠️ Troubleshooting

### **Dashboard tidak bisa diakses**
```bash
# Check if port 3001 is in use
netstat -tuln | grep 3001

# Restart dashboard
cd terminal-dashboard
./stop.sh && ./start.sh
```

### **Program tidak bisa start**
```bash
# Check program dependencies
cd [program-directory]
npm install

# Check configuration files
ls -la *.json *.env *.txt
```

### **Memory issues**
```bash
# Clean up system
./cleanup.sh

# Check running processes
ps aux | grep node
```

## 📊 Monitoring & Logs

### **Dashboard Logs**
```bash
# Backend logs
tail -f terminal-dashboard/backend.log

# Check system status
./run-terminal-dashboard.sh
# Choose option 5 (Show system status)
```

### **Program Logs**
- Setiap program memiliki log sendiri
- Akses melalui terminal interface di dashboard
- Download logs melalui web interface

## 🔒 Keamanan

- **Private Keys**: Simpan dengan aman, jangan share
- **Proxy**: Gunakan proxy terpercaya
- **Network**: Jalankan di network yang aman
- **Access**: Dashboard hanya accessible dari localhost

## 🤝 Kontribusi

Untuk menambah program bot baru:

1. Buat direktori program baru
2. Tambahkan konfigurasi di `terminal-dashboard/backend/server.js`
3. Test program secara individual
4. Update dokumentasi

## 📞 Support

- **Issues**: Buat issue di repository
- **Documentation**: Lihat README.md di setiap program
- **Updates**: Check repository secara berkala

## 📄 License

MIT License - Lihat file LICENSE untuk detail lengkap.

---

**🎉 Selamat menggunakan Terminal Dashboard! Jalankan semua bot Anda dengan mudah melalui satu interface yang powerful!**
